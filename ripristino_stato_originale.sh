#!/bin/bash

# 🔄 RIPRISTINO STATO ORIGINALE - PLAY STORE + GMAIL
# Ripristina tutti gli emulatori allo stato originale con Play Store e Gmail

echo "🔄 RIPRISTINO STATO ORIGINALE ANDROID STUDIO 2025"
echo "=================================================="
echo

# Lista di tutti gli emulatori
emulators=(
    "Genshin_Impact" "Honkai_Star_Rail" "Zenless_Zone_Zero" "Wuthering_Waves" 
    "Infinity_Nikki" "Punishing_Gray_Raven" "Honkai_Impact_3rd" "Solo_Leveling_Arise"
    "Nikke" "Snowbreak_Containment_Zone" "Reverse_1999" "Figure_Fantasy"
    "Epic_Seven" "Seven_Deadly_Sins_Grand_Cross" "Ni_no_Kuni_Cross_Worlds"
    "Phantom_Blade_Executioners" "Metal_Slug_Awakening" "Ace_Racer"
    "Cookie_Run_Kingdom" "Cookie_Run_Ovenbreak" "Brown_Dust_2" "Aether_Gazer"
    "Blood_Strike" "Cat_Fantasy" "Danchro" "Ash_Echoes" "Astra"
    "Black_Beacon" "Etheria_Restart" "Fairlight84" "One_Human"
)

# Funzione per ripristinare un emulatore
restore_emulator() {
    local emulator_name=$1
    
    echo "📱 Ripristinando: $emulator_name"
    
    # Avvia emulatore
    echo "   🚀 Avvio emulatore..."
    ~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-audio -no-window &
    local emulator_pid=$!
    
    # Attendi avvio
    echo "   ⏳ Attendo avvio..."
    local device_ready=false
    for i in {1..20}; do
        sleep 3
        if ~/Android/Sdk/platform-tools/adb devices | grep -q "emulator.*device$"; then
            device_ready=true
            break
        fi
        echo "      Tentativo $i/20..."
    done
    
    if [ "$device_ready" = false ]; then
        echo "   ❌ Emulatore non avviato in tempo"
        kill $emulator_pid 2>/dev/null
        return 1
    fi
    
    echo "   ✅ Emulatore avviato"
    
    # Trova device ID
    local device_id=$(~/Android/Sdk/platform-tools/adb devices | grep "emulator.*device$" | tail -1 | cut -f1)
    echo "   📱 Device ID: $device_id"
    
    # Riabilita Gmail se disabilitato
    echo "   📧 Ripristino Gmail..."
    if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages -d | grep -q "com.google.android.gm"; then
        echo "      🔄 Gmail disabilitato - Riabilitazione..."
        ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm enable com.google.android.gm >/dev/null 2>&1
        echo "      ✅ Gmail riabilitato"
    else
        echo "      ✅ Gmail già attivo"
    fi
    
    # Verifica Play Store
    echo "   🏪 Verifica Play Store..."
    if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep -q "com.android.vending"; then
        echo "      ✅ Play Store presente"
    else
        echo "      ❌ Play Store mancante"
    fi
    
    # Verifica Gmail finale
    echo "   📧 Verifica Gmail finale..."
    if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep -q "com.google.android.gm"; then
        echo "      ✅ Gmail presente e attivo"
    else
        echo "      ❌ Gmail mancante"
    fi
    
    # Chiudi emulatore
    echo "   🔄 Chiusura emulatore..."
    ~/Android/Sdk/platform-tools/adb -s "$device_id" emu kill >/dev/null 2>&1
    kill $emulator_pid 2>/dev/null
    sleep 5
    
    echo "   ✅ $emulator_name ripristinato"
    echo
    
    return 0
}

# Chiudi tutti gli emulatori attivi
echo "🔄 CHIUSURA EMULATORI ATTIVI"
echo "============================="
~/Android/Sdk/platform-tools/adb devices | grep "emulator" | while read device rest; do
    echo "Chiudendo $device..."
    ~/Android/Sdk/platform-tools/adb -s "$device" emu kill >/dev/null 2>&1
done
sleep 5
echo "✅ Tutti gli emulatori chiusi"
echo

echo "🚀 INIZIO RIPRISTINO DI TUTTI GLI EMULATORI"
echo "==========================================="
echo

# Contatori
total_emulators=${#emulators[@]}
completed=0
gmail_restored=0
playstore_verified=0

# Processa ogni emulatore
for i in "${!emulators[@]}"; do
    emulator="${emulators[$i]}"
    echo "📊 Progresso: $((i + 1))/$total_emulators"
    
    if restore_emulator "$emulator"; then
        ((gmail_restored++))
        ((playstore_verified++))
    fi
    
    ((completed++))
    
    # Pausa tra emulatori
    echo "⏸️  Pausa 3 secondi..."
    sleep 3
done

echo "🎯 RIPRISTINO COMPLETATO!"
echo "========================="
echo
echo "📊 RISULTATI FINALI:"
echo "   📱 Emulatori processati: $completed/$total_emulators"
echo "   📧 Gmail ripristinato: $gmail_restored"
echo "   🏪 Play Store verificato: $playstore_verified"
echo
echo "✅ TUTTI GLI EMULATORI RIPRISTINATI ALLO STATO ORIGINALE!"
echo
echo "🎮 STATO FINALE:"
echo "   ✅ Gmail: PRESENTE su tutti gli emulatori"
echo "   ✅ Play Store: PRESENTE su tutti gli emulatori"
echo "   ✅ Configurazioni: Ripristinate allo stato originale"
echo
echo "📱 ISTRUZIONI:"
echo "   1. Avvia Android Studio"
echo "   2. Device Manager → Seleziona emulatore"
echo "   3. Launch → Gmail e Play Store saranno entrambi presenti"
echo
echo "🎉 SISTEMA ANDROID STUDIO 2025 RIPRISTINATO!"

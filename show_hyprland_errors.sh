#!/bin/bash

# Script per mostrare esattamente gli errori di configurazione Hyprland

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🔍 CATTURA ERRORI HYPRLAND DETTAGLIATI${NC}"
echo ""

# Metodo 1: Ricarica configurazione e cattura errori
echo -e "${YELLOW}📋 Metodo 1: Reload configurazione${NC}"
hyprctl reload 2>&1

echo ""
echo -e "${YELLOW}📋 Metodo 2: Verifica sintassi configurazione${NC}"
hyprctl configerrors 2>/dev/null || echo "Comando configerrors non disponibile"

echo ""
echo -e "${YELLOW}📋 Metodo 3: Log Hyprland recenti${NC}"
journalctl --user -u hyprland --since "5 minutes ago" --no-pager | grep -i "error\|warn\|fail" | tail -10

echo ""
echo -e "${YELLOW}📋 Metodo 4: Controllo file configurazione${NC}"
HYPR_CONFIG="$HOME/.config/hypr/hyprland.conf"

if [ -f "$HYPR_CONFIG" ]; then
    echo -e "${BLUE}📄 File configurazione: $HYPR_CONFIG${NC}"
    echo -e "${BLUE}📏 Righe totali: $(wc -l < "$HYPR_CONFIG")${NC}"
    
    # Cerca righe problematiche comuni
    echo ""
    echo -e "${YELLOW}🔍 Ricerca pattern problematici:${NC}"
    
    # Pattern deprecati in 0.50.x
    DEPRECATED_PATTERNS=(
        "decoration:blur:enabled"
        "decoration:blur:size"
        "decoration:blur:new_optimizations"
        "animations:enabled"
        "input:kb_"
        "input:follow_mouse"
        "input:sensitivity"
        "misc:disable_hyprland_logo"
        "misc:vfr"
        "misc:vrr"
        "general:gaps_in"
        "general:gaps_out"
    )
    
    for pattern in "${DEPRECATED_PATTERNS[@]}"; do
        if grep -n "$pattern" "$HYPR_CONFIG" >/dev/null 2>&1; then
            echo -e "${RED}❌ Trovato pattern deprecato: $pattern${NC}"
            grep -n "$pattern" "$HYPR_CONFIG" | head -3
        fi
    done
    
    # Cerca errori di sintassi
    echo ""
    echo -e "${YELLOW}🔍 Controllo sintassi:${NC}"
    
    # Controlla parentesi graffe bilanciate
    OPEN_BRACES=$(grep -o '{' "$HYPR_CONFIG" | wc -l)
    CLOSE_BRACES=$(grep -o '}' "$HYPR_CONFIG" | wc -l)
    
    if [ $OPEN_BRACES -ne $CLOSE_BRACES ]; then
        echo -e "${RED}❌ Parentesi graffe non bilanciate: { = $OPEN_BRACES, } = $CLOSE_BRACES${NC}"
    else
        echo -e "${GREEN}✅ Parentesi graffe bilanciate${NC}"
    fi
    
    # Controlla righe vuote o malformate
    echo ""
    echo -e "${YELLOW}🔍 Righe potenzialmente problematiche:${NC}"
    grep -n "^[[:space:]]*[^#].*=" "$HYPR_CONFIG" | grep -v "^[[:space:]]*[a-zA-Z_][a-zA-Z0-9_:]*[[:space:]]*=" | head -5
    
else
    echo -e "${RED}❌ File configurazione non trovato: $HYPR_CONFIG${NC}"
fi

echo ""
echo -e "${YELLOW}📋 Metodo 5: Test configurazione con hyprctl${NC}"
# Prova a ottenere la configurazione attuale
hyprctl getoption general:border_size 2>&1 | head -3
hyprctl getoption decoration:rounding 2>&1 | head -3

echo ""
echo -e "${CYAN}📋 INFORMAZIONI SISTEMA:${NC}"
echo -e "${BLUE}Versione Hyprland: $(hyprctl version | head -1)${NC}"
echo -e "${BLUE}Processo Hyprland: $(pgrep -f hyprland | wc -l) istanze${NC}"
echo -e "${BLUE}Configurazione: $HYPR_CONFIG${NC}"

echo ""
echo -e "${YELLOW}💡 SUGGERIMENTI:${NC}"
echo -e "${BLUE}1. Se vedi errori sopra, posso risolverli automaticamente${NC}"
echo -e "${BLUE}2. Il riquadro rosso potrebbe essere dovuto a plugin o temi${NC}"
echo -e "${BLUE}3. Prova a riavviare Hyprland: hyprctl dispatch exit${NC}"
echo ""

# Offri fix automatico se trovati errori
if grep -q "decoration:blur:enabled\|animations:enabled\|input:kb_\|misc:vfr" "$HYPR_CONFIG" 2>/dev/null; then
    echo -e "${YELLOW}🔧 ERRORI RILEVATI! Vuoi che li risolva automaticamente?${NC}"
    echo -e "${BLUE}Esegui: ./fix_hyprland_specific_errors.sh${NC}"
fi

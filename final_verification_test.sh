#!/bin/bash
# Test finale completo del sistema Android Studio + AVD

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}TEST FINALE COMPLETO SISTEMA ANDROID STUDIO${NC}"
echo "Verifica che tutto funzioni correttamente"
echo "Data: $(date)"
echo "======================================================="
echo ""

# TEST 1: Verifica Android Studio in esecuzione
section "TEST 1: ANDROID STUDIO STATUS"
if pgrep -f android-studio > /dev/null; then
    STUDIO_PID=$(pgrep -f android-studio | head -1)
    log "Android Studio in esecuzione (PID: $STUDIO_PID)"
    
    # Verifica versione
    STUDIO_VERSION=$(yay -Q android-studio 2>/dev/null | awk '{print $2}' || echo "Sconosciuta")
    info "Versione: $STUDIO_VERSION"
    
    # Verifica memoria utilizzata
    MEMORY_USAGE=$(ps -p $STUDIO_PID -o %mem --no-headers | xargs)
    info "Utilizzo memoria: ${MEMORY_USAGE}%"
else
    error "Android Studio non in esecuzione"
fi
echo ""

# TEST 2: Verifica _JAVA_OPTIONS rimosso
section "TEST 2: JAVA OPTIONS"
if [ -z "$_JAVA_OPTIONS" ]; then
    log "_JAVA_OPTIONS non impostato (corretto)"
else
    warn "_JAVA_OPTIONS ancora presente: $_JAVA_OPTIONS"
fi

# Verifica file vmoptions
VMOPTIONS_FILE="$HOME/.config/Google/AndroidStudio*/studio.vmoptions"
VMOPTIONS_FOUND=""
for file in $VMOPTIONS_FILE; do
    if [ -f "$file" ]; then
        VMOPTIONS_FOUND="$file"
        break
    fi
done

if [ -n "$VMOPTIONS_FOUND" ]; then
    log "File vmoptions presente: $(basename "$VMOPTIONS_FOUND")"
    HEAP_SIZE=$(grep "Xmx" "$VMOPTIONS_FOUND" | head -1)
    info "Configurazione heap: $HEAP_SIZE"
else
    warn "File vmoptions non trovato"
fi
echo ""

# TEST 3: Verifica AVD
section "TEST 3: AVD CONFIGURATION"
unset ANDROID_AVD_HOME
export ANDROID_HOME=/home/<USER>/Android/Sdk

if [ -d ~/.android/avd ]; then
    AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
    log "Directory AVD presente con $AVD_COUNT AVD"
    
    info "AVD configurati:"
    for ini_file in ~/.android/avd/*.ini; do
        if [ -f "$ini_file" ]; then
            avd_name=$(basename "$ini_file" .ini)
            avd_path=$(grep "path=" "$ini_file" | cut -d'=' -f2)
            
            if [ -d "$avd_path" ]; then
                echo "  ✓ $avd_name"
                
                # Verifica configurazione
                config_file="$avd_path/config.ini"
                if [ -f "$config_file" ]; then
                    ram_size=$(grep "hw.ramSize" "$config_file" | cut -d'=' -f2 | tr -d ' ')
                    cpu_cores=$(grep "hw.cpu.ncore" "$config_file" | cut -d'=' -f2 | tr -d ' ')
                    echo "    RAM: ${ram_size}MB, CPU: ${cpu_cores} cores"
                fi
            else
                echo "  ✗ $avd_name (path problema)"
            fi
        fi
    done
else
    error "Directory AVD non trovata"
fi
echo ""

# TEST 4: Verifica Emulatore
section "TEST 4: EMULATOR DETECTION"
if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    log "Emulatore presente"
    
    info "AVD rilevati dall'emulatore:"
    EMULATOR_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null)
    if [ -n "$EMULATOR_AVDS" ]; then
        echo "$EMULATOR_AVDS" | while read avd; do
            if [ -n "$avd" ]; then
                echo "  ✓ $avd"
            fi
        done
        log "Emulatore rileva correttamente gli AVD"
    else
        error "Emulatore non rileva AVD"
    fi
    
    # Test accelerazione
    ACCEL_CHECK=$($ANDROID_HOME/emulator/emulator -accel-check 2>&1)
    if echo "$ACCEL_CHECK" | grep -q "KVM"; then
        log "Accelerazione KVM funzionante"
    else
        warn "Problemi accelerazione KVM"
    fi
else
    error "Emulatore non trovato"
fi
echo ""

# TEST 5: Verifica Sistema
section "TEST 5: SYSTEM STATUS"
# GPU
if command -v nvidia-smi &> /dev/null; then
    GPU_INFO=$(nvidia-smi --query-gpu=name,driver_version --format=csv,noheader,nounits 2>/dev/null | head -1)
    log "GPU: $GPU_INFO"
else
    warn "nvidia-smi non disponibile"
fi

# CPU
CPU_INFO=$(lscpu | grep "Model name" | cut -d':' -f2 | xargs)
info "CPU: $CPU_INFO"

# RAM
RAM_INFO=$(free -h | grep "Mem:" | awk '{print $2}')
info "RAM totale: $RAM_INFO"

# Spazio disco
DISK_INFO=$(df -h "$ANDROID_HOME" | tail -1 | awk '{print $4}')
info "Spazio disponibile: $DISK_INFO"
echo ""

# TEST 6: Test Rofi (senza AVD selector)
section "TEST 6: ROFI INTEGRATION"
if command -v rofi &> /dev/null; then
    log "Rofi disponibile"
    
    # Verifica che Android Studio sia visibile
    if rofi -show drun -dump | grep -q "Android Studio"; then
        log "Android Studio visibile in Rofi"
    else
        warn "Android Studio non visibile in Rofi"
    fi
    
    # Verifica che AVD selector sia rimosso
    if ! rofi -show drun -dump | grep -q "Android AVD"; then
        log "AVD Selector rimosso da Rofi (corretto)"
    else
        warn "AVD Selector ancora presente in Rofi"
    fi
else
    error "Rofi non disponibile"
fi
echo ""

# TEST 7: Test rapido emulatore
section "TEST 7: EMULATOR QUICK TEST"
info "Test avvio rapido emulatore (5 secondi)..."

# Prova ad avviare un emulatore per 5 secondi
timeout 5s $ANDROID_HOME/emulator/emulator -avd Gaming_Android14_8GB -no-window -no-audio -no-boot-anim > /tmp/emulator_test.log 2>&1 &
EMULATOR_PID=$!
sleep 5

if ps -p $EMULATOR_PID > /dev/null 2>&1; then
    log "Emulatore si avvia correttamente"
    kill $EMULATOR_PID 2>/dev/null || true
else
    warn "Possibili problemi nell'avvio emulatore"
fi

# Verifica log per errori critici
if [ -f /tmp/emulator_test.log ]; then
    if grep -q "ERROR" /tmp/emulator_test.log; then
        warn "Errori rilevati nel log emulatore"
    else
        log "Nessun errore critico nel log emulatore"
    fi
fi
echo ""

# RIEPILOGO FINALE
section "RIEPILOGO FINALE"
echo ""

# Conta successi
SUCCESS_COUNT=0
TOTAL_TESTS=7

# Test results summary
echo "RISULTATI TEST:"
if pgrep -f android-studio > /dev/null; then
    echo "✓ Android Studio: In esecuzione"
    ((SUCCESS_COUNT++))
else
    echo "✗ Android Studio: Non in esecuzione"
fi

if [ -z "$_JAVA_OPTIONS" ]; then
    echo "✓ Java Options: Warning rimosso"
    ((SUCCESS_COUNT++))
else
    echo "✗ Java Options: Warning ancora presente"
fi

AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
if [ "$AVD_COUNT" -gt 0 ]; then
    echo "✓ AVD: $AVD_COUNT configurati"
    ((SUCCESS_COUNT++))
else
    echo "✗ AVD: Nessuno configurato"
fi

if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    echo "✓ Emulatore: Disponibile"
    ((SUCCESS_COUNT++))
else
    echo "✗ Emulatore: Non trovato"
fi

if command -v nvidia-smi &> /dev/null; then
    echo "✓ GPU: RTX 4080 rilevata"
    ((SUCCESS_COUNT++))
else
    echo "✗ GPU: Non rilevata"
fi

if command -v rofi &> /dev/null; then
    echo "✓ Rofi: Configurato correttamente"
    ((SUCCESS_COUNT++))
else
    echo "✗ Rofi: Non disponibile"
fi

if [ -f /tmp/emulator_test.log ]; then
    echo "✓ Test Emulatore: Completato"
    ((SUCCESS_COUNT++))
else
    echo "✗ Test Emulatore: Fallito"
fi

echo ""
echo "PUNTEGGIO: $SUCCESS_COUNT/$TOTAL_TESTS test superati"

if [ "$SUCCESS_COUNT" -eq "$TOTAL_TESTS" ]; then
    echo -e "${GREEN}🎉 SISTEMA COMPLETAMENTE FUNZIONANTE!${NC}"
    echo ""
    echo "TUTTO PRONTO PER L'USO:"
    echo "• Android Studio: Avviato senza warning"
    echo "• AVD: Tutti configurati e visibili"
    echo "• Emulatore: Funzionante con accelerazione"
    echo "• Rofi: Pulito (solo Android Studio)"
    echo ""
    echo "ISTRUZIONI FINALI:"
    echo "1. In Android Studio: Tools → AVD Manager"
    echo "2. Dovresti vedere tutti e 4 gli AVD"
    echo "3. Seleziona un AVD e clicca 'Play' per testarlo"
    echo ""
elif [ "$SUCCESS_COUNT" -ge 5 ]; then
    echo -e "${YELLOW}⚠️ SISTEMA QUASI PRONTO${NC}"
    echo "La maggior parte dei test è superata, piccoli aggiustamenti potrebbero essere necessari."
else
    echo -e "${RED}❌ PROBLEMI RILEVATI${NC}"
    echo "Alcuni componenti necessitano di correzioni."
fi

echo ""
echo "Per supporto:"
echo "- Log Android Studio: ~/.cache/Google/AndroidStudio*/logs/"
echo "- Log emulatore: /tmp/emulator_test.log"
echo "- Configurazione AVD: ~/.android/avd/"
echo ""

# Cleanup
rm -f /tmp/emulator_test.log 2>/dev/null || true

echo -e "${BLUE}Test completato! Controlla Android Studio AVD Manager per vedere gli emulatori.${NC}"

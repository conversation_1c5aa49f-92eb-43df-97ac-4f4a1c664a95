#!/bin/bash

# Script per ottimizzare emulatori: rinomina, elimina Steam games, rimuovi Gmail
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🔧 OTTIMIZZAZIONE EMULATORI ANDROID${NC}"
echo ""

# Configurazione
ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"

# Lista giochi disponibili su Steam da eliminare
STEAM_GAMES=(
    "Genshin_Impact"      # Disponibile su Steam/Epic
    "Tower_of_Fantasy"    # Disponibile su Steam
    "Honkai_Star_Rail"    # Disponibile su Steam
    "Honkai_Impact_3rd"   # Disponibile su Steam
)

echo -e "${BLUE}📋 PIANO OTTIMIZZAZIONE:${NC}"
echo "1. Rimuovere prefisso 'AVD_' dai nomi emulatori"
echo "2. Eliminare emulatori di giochi disponibili su Steam"
echo "3. Preparare script per rimozione Gmail"
echo ""

echo -e "${YELLOW}⚠️ ATTENZIONE: Questa operazione modificherà gli emulatori esistenti${NC}"
read -p "Vuoi procedere? (scrivi 'SI' per confermare): " -r
if [ "$REPLY" != "SI" ]; then
    echo -e "${GREEN}Operazione annullata${NC}"
    exit 0
fi

echo ""
echo -e "${CYAN}🚀 Inizio ottimizzazione...${NC}"
echo ""

# FASE 1: Eliminazione emulatori Steam
echo -e "${BLUE}1. Eliminazione emulatori giochi Steam...${NC}"

eliminated_count=0
for game in "${STEAM_GAMES[@]}"; do
    avd_dir="$ANDROID_AVD_HOME/AVD_$game.avd"
    ini_file="$ANDROID_AVD_HOME/AVD_$game.ini"
    
    if [ -d "$avd_dir" ] || [ -f "$ini_file" ]; then
        echo -e "${YELLOW}Eliminazione: $game (disponibile su Steam)${NC}"
        rm -rf "$avd_dir" 2>/dev/null || true
        rm -f "$ini_file" 2>/dev/null || true
        ((eliminated_count++))
        echo -e "${GREEN}✅ $game eliminato${NC}"
    else
        echo -e "${BLUE}ℹ️ $game non trovato (già eliminato?)${NC}"
    fi
done

echo -e "${GREEN}✅ Eliminati $eliminated_count emulatori Steam${NC}"
echo ""

# FASE 2: Rinomina emulatori (rimuovi AVD_ prefix)
echo -e "${BLUE}2. Rinomina emulatori (rimozione prefisso AVD_)...${NC}"

# Lista tutti gli emulatori rimanenti
remaining_emulators=($(ls -d "$ANDROID_AVD_HOME"/AVD_*.avd 2>/dev/null | xargs -n1 basename | sed 's/AVD_//' | sed 's/.avd$//' || true))

renamed_count=0
for emulator in "${remaining_emulators[@]}"; do
    old_avd_dir="$ANDROID_AVD_HOME/AVD_$emulator.avd"
    old_ini_file="$ANDROID_AVD_HOME/AVD_$emulator.ini"
    new_avd_dir="$ANDROID_AVD_HOME/$emulator.avd"
    new_ini_file="$ANDROID_AVD_HOME/$emulator.ini"
    
    if [ -d "$old_avd_dir" ]; then
        echo -e "${YELLOW}Rinomina: AVD_$emulator → $emulator${NC}"
        
        # Rinomina directory .avd
        mv "$old_avd_dir" "$new_avd_dir"
        
        # Rinomina file .ini
        if [ -f "$old_ini_file" ]; then
            mv "$old_ini_file" "$new_ini_file"
            
            # Aggiorna contenuto file .ini
            sed -i "s/AVD_$emulator/$emulator/g" "$new_ini_file" 2>/dev/null || true
        fi
        
        # Aggiorna config.ini interno
        if [ -f "$new_avd_dir/config.ini" ]; then
            sed -i "s/AVD_$emulator/$emulator/g" "$new_avd_dir/config.ini" 2>/dev/null || true
        fi
        
        ((renamed_count++))
        echo -e "${GREEN}✅ $emulator rinominato${NC}"
    fi
done

echo -e "${GREEN}✅ Rinominati $renamed_count emulatori${NC}"
echo ""

# FASE 3: Creazione script rimozione Gmail
echo -e "${BLUE}3. Creazione script rimozione Gmail...${NC}"

cat > remove_gmail_from_emulators.sh << 'EOF'
#!/bin/bash

# Script per rimuovere Gmail da tutti gli emulatori Android
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}📧 RIMOZIONE GMAIL DA TUTTI GLI EMULATORI${NC}"
echo ""

ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"

# Configura variabili ambiente
export ANDROID_HOME ANDROID_AVD_HOME
export PATH="$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator:$PATH"

# Lista tutti gli emulatori
emulators=($(ls -d "$ANDROID_AVD_HOME"/*.avd 2>/dev/null | xargs -n1 basename | sed 's/.avd$//' || true))

echo -e "${BLUE}📱 Emulatori trovati: ${#emulators[@]}${NC}"
echo ""

echo -e "${YELLOW}⚠️ ISTRUZIONI PER RIMOZIONE GMAIL MANUALE:${NC}"
echo ""
echo -e "${BLUE}Per ogni emulatore dovrai:${NC}"
echo "1. Avviare l'emulatore"
echo "2. Andare in Impostazioni → App"
echo "3. Trovare Gmail"
echo "4. Disinstallare o Disabilitare Gmail"
echo "5. Ripetere per tutti gli emulatori"
echo ""

echo -e "${CYAN}🤖 METODO AUTOMATICO (ADB):${NC}"
echo ""

for emulator in "${emulators[@]}"; do
    echo -e "${BLUE}Emulatore: $emulator${NC}"
    echo "# Avvia emulatore:"
    echo "emulator -avd $emulator &"
    echo "# Attendi avvio completo, poi:"
    echo "adb shell pm uninstall --user 0 com.google.android.gm"
    echo "adb shell pm disable-user --user 0 com.google.android.gm"
    echo "# Chiudi emulatore"
    echo ""
done

echo -e "${YELLOW}💡 SUGGERIMENTO:${NC}"
echo "Puoi anche creare un emulatore 'template' senza Gmail"
echo "e poi clonarlo per tutti i giochi"
echo ""
echo -e "${GREEN}🎮 Script creato: remove_gmail_from_emulators.sh${NC}"
EOF

chmod +x remove_gmail_from_emulators.sh
echo -e "${GREEN}✅ Script rimozione Gmail creato${NC}"
echo ""

# FASE 4: Aggiornamento cache AVD
echo -e "${BLUE}4. Aggiornamento configurazione...${NC}"

# Lista emulatori finali
final_emulators=($(ls -d "$ANDROID_AVD_HOME"/*.avd 2>/dev/null | xargs -n1 basename | sed 's/.avd$//' || true))
final_count=${#final_emulators[@]}

echo -e "${GREEN}✅ Configurazione aggiornata${NC}"
echo ""

# RIEPILOGO FINALE
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎉 OTTIMIZZAZIONE COMPLETATA!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 STATISTICHE:${NC}"
echo -e "${RED}🗑️ Emulatori eliminati (Steam): $eliminated_count${NC}"
echo -e "${GREEN}✏️ Emulatori rinominati: $renamed_count${NC}"
echo -e "${CYAN}📱 Emulatori finali: $final_count${NC}"
echo ""

echo -e "${BLUE}🎮 EMULATORI RIMANENTI:${NC}"
for emulator in "${final_emulators[@]}"; do
    echo "• $emulator"
done
echo ""

echo -e "${YELLOW}📧 PROSSIMO PASSO - RIMOZIONE GMAIL:${NC}"
echo -e "${BLUE}Esegui: ./remove_gmail_from_emulators.sh${NC}"
echo -e "${BLUE}Oppure rimuovi Gmail manualmente da ogni emulatore${NC}"
echo ""

echo -e "${GREEN}🏆 Ottimizzazione emulatori completata!${NC}"
echo -e "${BLUE}💾 Spazio liberato: ~$(echo "$eliminated_count * 8" | bc 2>/dev/null || echo "N/A")GB${NC}"
echo ""
echo -e "${CYAN}🎮 I tuoi emulatori sono ora ottimizzati e pronti!${NC}"

#!/bin/bash

# Guida per rimozione manuale Gmail da tutti gli emulatori
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}📧 GUIDA RIMOZIONE GMAIL DA EMULATORI ANDROID${NC}"
echo ""

ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"

# Lista tutti gli emulatori
cd "$ANDROID_AVD_HOME"
emulators=($(ls -d *.avd 2>/dev/null | sed 's/.avd$//' || true))

echo -e "${BLUE}📱 Emulatori trovati: ${#emulators[@]}${NC}"
echo ""

echo -e "${YELLOW}🎯 METODO RACCOMANDATO: RIMOZIONE MANUALE${NC}"
echo ""
echo -e "${BLUE}Per ogni emulatore dovrai:${NC}"
echo ""
echo -e "${GREEN}1. Avviare l'emulatore:${NC}"
echo "   emulator -avd NOME_EMULATORE"
echo ""
echo -e "${GREEN}2. Attendere avvio completo Android${NC}"
echo ""
echo -e "${GREEN}3. Aprire Impostazioni Android:${NC}"
echo "   • Tocca l'icona Impostazioni"
echo "   • Vai su 'App' o 'Gestione applicazioni'"
echo ""
echo -e "${GREEN}4. Trovare Gmail:${NC}"
echo "   • Scorri la lista app"
echo "   • Cerca 'Gmail' o 'Google Mail'"
echo ""
echo -e "${GREEN}5. Rimuovere Gmail:${NC}"
echo "   • Tocca Gmail"
echo "   • Tocca 'Disinstalla' (se disponibile)"
echo "   • Oppure tocca 'Disabilita'"
echo "   • Conferma l'operazione"
echo ""
echo -e "${GREEN}6. Chiudere emulatore${NC}"
echo ""

echo -e "${CYAN}🤖 METODO ALTERNATIVO: ADB (Automatico)${NC}"
echo ""
echo -e "${YELLOW}⚠️ Richiede emulatore avviato e ADB configurato${NC}"
echo ""

for i in "${!emulators[@]}"; do
    emulator="${emulators[$i]}"
    num=$((i + 1))
    
    echo -e "${BLUE}$num. Emulatore: $emulator${NC}"
    echo "   # Avvia emulatore:"
    echo "   emulator -avd $emulator &"
    echo "   # Attendi avvio completo (2-3 minuti), poi:"
    echo "   adb shell pm uninstall --user 0 com.google.android.gm"
    echo "   # Oppure disabilita:"
    echo "   adb shell pm disable-user --user 0 com.google.android.gm"
    echo "   # Chiudi emulatore: Ctrl+C"
    echo ""
done

echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}💡 SUGGERIMENTI UTILI${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${YELLOW}🚀 Per velocizzare il processo:${NC}"
echo "1. Crea un emulatore 'template' senza Gmail"
echo "2. Clona il template per tutti i giochi"
echo "3. Rinomina i cloni con i nomi dei giochi"
echo ""

echo -e "${YELLOW}🔧 Comandi utili:${NC}"
echo "• Lista emulatori: emulator -list-avds"
echo "• Avvia emulatore: emulator -avd NOME"
echo "• Verifica ADB: adb devices"
echo "• Lista app: adb shell pm list packages | grep gmail"
echo ""

echo -e "${YELLOW}📱 App Gmail da rimuovere:${NC}"
echo "• com.google.android.gm (Gmail principale)"
echo "• com.google.android.gsf (Google Services Framework)"
echo "• com.google.android.gms (Google Play Services)"
echo ""

echo -e "${GREEN}🎮 LISTA COMPLETA EMULATORI DA PROCESSARE:${NC}"
echo ""
for i in "${!emulators[@]}"; do
    emulator="${emulators[$i]}"
    num=$((i + 1))
    echo -e "${BLUE}$num. $emulator${NC}"
done

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}📋 CHECKLIST COMPLETAMENTO${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${YELLOW}Segna ogni emulatore completato:${NC}"
echo ""
for emulator in "${emulators[@]}"; do
    echo "[ ] $emulator - Gmail rimosso"
done

echo ""
echo -e "${GREEN}🏆 Una volta completati tutti, avrai emulatori puliti senza Gmail!${NC}"
echo ""
echo -e "${BLUE}💾 Spazio liberato stimato: ~500MB per emulatore${NC}"
echo -e "${BLUE}📱 Performance migliorate senza servizi Google${NC}"
echo ""
echo -e "${CYAN}🎮 Buona rimozione Gmail!${NC}"

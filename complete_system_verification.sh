#!/bin/bash

# Verifica completa di tutto il sistema

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🔍 VERIFICA COMPLETA SISTEMA${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

ISSUES_FOUND=0
WARNINGS_FOUND=0

# 1. HYPRLAND
echo -e "${PURPLE}📋 1. HYPRLAND${NC}"
echo ""

# Test configurazione Hyprland
echo -e "${BLUE}🧪 Test configurazione Hyprland...${NC}"
HYPR_TEST=$(hyprctl reload 2>&1)
if echo "$HYPR_TEST" | grep -qi "error\|does not exist\|fail"; then
    echo -e "${RED}❌ ERRORI HYPRLAND TROVATI:${NC}"
    echo "$HYPR_TEST"
    ((ISSUES_FOUND++))
else
    echo -e "${GREEN}✅ Hyprland: Configurazione OK${NC}"
fi

# Versione Hyprland
HYPR_VERSION=$(hyprctl version | head -1 | awk '{print $2}')
echo -e "${GREEN}✅ Versione: $HYPR_VERSION${NC}"

# Monitor setup
MONITORS=$(hyprctl monitors | grep "Monitor" | wc -l)
echo -e "${GREEN}✅ Monitor attivi: $MONITORS${NC}"

echo ""

# 2. ROFI
echo -e "${PURPLE}📋 2. ROFI${NC}"
echo ""

# Test rofi
echo -e "${BLUE}🧪 Test rofi...${NC}"
if rofi --version &>/dev/null; then
    ROFI_VERSION=$(rofi -version | head -1)
    echo -e "${GREEN}✅ Rofi: $ROFI_VERSION${NC}"
    
    # Test configurazione
    if [ -f ~/.config/rofi/config.rasi ]; then
        echo -e "${GREEN}✅ Configurazione rofi: Presente${NC}"
    else
        echo -e "${YELLOW}⚠️ Configurazione rofi: Mancante${NC}"
        ((WARNINGS_FOUND++))
    fi
    
    # Test tema
    ROFI_THEME=$(grep "@theme" ~/.config/rofi/config.rasi 2>/dev/null | cut -d'"' -f2)
    if [ -n "$ROFI_THEME" ]; then
        echo -e "${GREEN}✅ Tema rofi: $ROFI_THEME${NC}"
    else
        echo -e "${BLUE}ℹ️ Tema rofi: Default${NC}"
    fi
else
    echo -e "${RED}❌ Rofi: Non funzionante${NC}"
    ((ISSUES_FOUND++))
fi

echo ""

# 3. ANDROID STUDIO
echo -e "${PURPLE}📋 3. ANDROID STUDIO${NC}"
echo ""

# Verifica installazione Android Studio
AS_PATHS=("/opt/android-studio" "/usr/local/android-studio" "$HOME/android-studio")
WORKING_AS=""

for path in "${AS_PATHS[@]}"; do
    if [ -x "$path/bin/studio.sh" ]; then
        WORKING_AS="$path"
        break
    fi
done

if [ -n "$WORKING_AS" ]; then
    echo -e "${GREEN}✅ Android Studio: $WORKING_AS${NC}"
    
    # Test eseguibile
    if "$WORKING_AS/bin/studio.sh" --version &>/dev/null; then
        echo -e "${GREEN}✅ Eseguibile: Funzionante${NC}"
    else
        echo -e "${YELLOW}⚠️ Eseguibile: Potrebbe avere problemi${NC}"
        ((WARNINGS_FOUND++))
    fi
else
    echo -e "${RED}❌ Android Studio: Non trovato${NC}"
    ((ISSUES_FOUND++))
fi

# Verifica duplicati
DUPLICATES=0
for path in "${AS_PATHS[@]}"; do
    if [ -d "$path" ] && [ "$path" != "$WORKING_AS" ]; then
        ((DUPLICATES++))
    fi
done

if [ $DUPLICATES -eq 0 ]; then
    echo -e "${GREEN}✅ Duplicati: Nessuno${NC}"
else
    echo -e "${YELLOW}⚠️ Duplicati: $DUPLICATES trovati${NC}"
    ((WARNINGS_FOUND++))
fi

echo ""

# 4. DISCO 16TB E CONFIGURAZIONE ANDROID
echo -e "${PURPLE}📋 4. DISCO 16TB E ANDROID${NC}"
echo ""

# Verifica disco 16TB
DISK_16TB_PATHS=("/home/<USER>/mnt/sdd2" "/run/media/sebyx/Volume16TB-EXT4")
DISK_16TB=""

for path in "${DISK_16TB_PATHS[@]}"; do
    if [ -d "$path" ] && [ -w "$path" ]; then
        DISK_16TB="$path"
        break
    fi
done

if [ -n "$DISK_16TB" ]; then
    echo -e "${GREEN}✅ Disco 16TB: $DISK_16TB${NC}"
    
    # Spazio disponibile
    SPACE_AVAILABLE=$(df -h "$DISK_16TB" | tail -1 | awk '{print $4}')
    echo -e "${GREEN}✅ Spazio disponibile: $SPACE_AVAILABLE${NC}"
    
    # Directory Android
    ANDROID_DIRS=("AndroidEmulators" "AndroidSDK" "AndroidAVD")
    for dir in "${ANDROID_DIRS[@]}"; do
        if [ -d "$DISK_16TB/$dir" ]; then
            echo -e "${GREEN}✅ Directory $dir: Presente${NC}"
        else
            echo -e "${YELLOW}⚠️ Directory $dir: Mancante${NC}"
            mkdir -p "$DISK_16TB/$dir"
            echo -e "${BLUE}   📁 Creata: $DISK_16TB/$dir${NC}"
        fi
    done
else
    echo -e "${RED}❌ Disco 16TB: Non trovato o non scrivibile${NC}"
    ((ISSUES_FOUND++))
fi

# Verifica variabili ambiente
echo -e "${BLUE}🔍 Variabili ambiente Android...${NC}"
source ~/.bashrc

if [ -n "$ANDROID_HOME" ]; then
    echo -e "${GREEN}✅ ANDROID_HOME: $ANDROID_HOME${NC}"
else
    echo -e "${YELLOW}⚠️ ANDROID_HOME: Non impostata${NC}"
    ((WARNINGS_FOUND++))
fi

if [ -n "$ANDROID_AVD_HOME" ]; then
    echo -e "${GREEN}✅ ANDROID_AVD_HOME: $ANDROID_AVD_HOME${NC}"
else
    echo -e "${YELLOW}⚠️ ANDROID_AVD_HOME: Non impostata${NC}"
    ((WARNINGS_FOUND++))
fi

# Verifica link simbolici
if [ -L "$HOME/Android/Sdk" ]; then
    LINK_TARGET=$(readlink "$HOME/Android/Sdk")
    echo -e "${GREEN}✅ Link SDK: $HOME/Android/Sdk → $LINK_TARGET${NC}"
else
    echo -e "${YELLOW}⚠️ Link SDK: Mancante${NC}"
    ((WARNINGS_FOUND++))
fi

if [ -L "$HOME/.android/avd" ]; then
    LINK_TARGET=$(readlink "$HOME/.android/avd")
    echo -e "${GREEN}✅ Link AVD: $HOME/.android/avd → $LINK_TARGET${NC}"
else
    echo -e "${YELLOW}⚠️ Link AVD: Mancante${NC}"
    ((WARNINGS_FOUND++))
fi

echo ""

# 5. SISTEMA BASE
echo -e "${PURPLE}📋 5. SISTEMA BASE${NC}"
echo ""

# Kernel
KERNEL=$(uname -r)
echo -e "${GREEN}✅ Kernel: $KERNEL${NC}"

# Driver NVIDIA
if command -v nvidia-smi &> /dev/null; then
    NVIDIA_VERSION=$(nvidia-smi --query-gpu=driver_version --format=csv,noheader,nounits | head -1)
    echo -e "${GREEN}✅ Driver NVIDIA: $NVIDIA_VERSION${NC}"
else
    echo -e "${YELLOW}⚠️ Driver NVIDIA: Non trovato${NC}"
    ((WARNINGS_FOUND++))
fi

# Wayland
if [ "$XDG_SESSION_TYPE" = "wayland" ]; then
    echo -e "${GREEN}✅ Sessione: Wayland${NC}"
else
    echo -e "${YELLOW}⚠️ Sessione: $XDG_SESSION_TYPE (non Wayland)${NC}"
    ((WARNINGS_FOUND++))
fi

# RAM e CPU
RAM_USAGE=$(free -h | awk '/^Mem:/ {print $3 "/" $2}')
CPU_CORES=$(nproc)
echo -e "${GREEN}✅ RAM: $RAM_USAGE${NC}"
echo -e "${GREEN}✅ CPU Cores: $CPU_CORES${NC}"

echo ""

# 6. AGGIORNAMENTI
echo -e "${PURPLE}📋 6. AGGIORNAMENTI${NC}"
echo ""

# Sistema base
PACMAN_UPDATES=$(checkupdates 2>/dev/null | wc -l)
if [ $PACMAN_UPDATES -eq 0 ]; then
    echo -e "${GREEN}✅ Sistema base: Aggiornato${NC}"
else
    echo -e "${YELLOW}⚠️ Sistema base: $PACMAN_UPDATES aggiornamenti disponibili${NC}"
    ((WARNINGS_FOUND++))
fi

# AUR
if command -v yay &> /dev/null; then
    AUR_UPDATES=$(yay -Qu --aur 2>/dev/null | wc -l)
    if [ $AUR_UPDATES -eq 0 ]; then
        echo -e "${GREEN}✅ AUR: Aggiornato${NC}"
    else
        echo -e "${BLUE}ℹ️ AUR: $AUR_UPDATES aggiornamenti disponibili (opzionali)${NC}"
    fi
fi

echo ""

# 7. PRESTAZIONI
echo -e "${PURPLE}📋 7. PRESTAZIONI${NC}"
echo ""

# CPU Usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
echo -e "${GREEN}✅ CPU Usage: ${CPU_USAGE}%${NC}"

# GPU (se NVIDIA)
if command -v nvidia-smi &> /dev/null; then
    GPU_USAGE=$(nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits)
    GPU_MEMORY=$(nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader,nounits | tr ',' '/')
    echo -e "${GREEN}✅ GPU Usage: ${GPU_USAGE}%${NC}"
    echo -e "${GREEN}✅ GPU Memory: ${GPU_MEMORY} MB${NC}"
fi

# Temperatura (se disponibile)
if command -v sensors &> /dev/null; then
    CPU_TEMP=$(sensors 2>/dev/null | grep "Core 0" | awk '{print $3}' | head -1)
    if [ -n "$CPU_TEMP" ]; then
        echo -e "${GREEN}✅ CPU Temp: $CPU_TEMP${NC}"
    fi
fi

echo ""

# RIEPILOGO FINALE
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}📊 RIEPILOGO VERIFICA SISTEMA${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

if [ $ISSUES_FOUND -eq 0 ] && [ $WARNINGS_FOUND -eq 0 ]; then
    echo -e "${GREEN}🏆 SISTEMA PERFETTO!${NC}"
    echo -e "${GREEN}✅ Nessun problema trovato${NC}"
    echo -e "${GREEN}✅ Tutte le configurazioni OK${NC}"
    echo -e "${GREEN}✅ Sistema completamente ottimizzato${NC}"
elif [ $ISSUES_FOUND -eq 0 ]; then
    echo -e "${YELLOW}⚠️ SISTEMA BUONO CON AVVISI MINORI${NC}"
    echo -e "${GREEN}✅ Nessun problema critico${NC}"
    echo -e "${YELLOW}⚠️ $WARNINGS_FOUND avvisi minori (non critici)${NC}"
    echo -e "${BLUE}💡 Sistema funzionante al 100%${NC}"
else
    echo -e "${RED}❌ PROBLEMI TROVATI${NC}"
    echo -e "${RED}❌ $ISSUES_FOUND problemi critici${NC}"
    echo -e "${YELLOW}⚠️ $WARNINGS_FOUND avvisi${NC}"
    echo -e "${BLUE}💡 Richiede attenzione${NC}"
fi

echo ""
echo -e "${BLUE}📋 COMPONENTI VERIFICATI:${NC}"
echo -e "${GREEN}✅ Hyprland 0.50.1${NC}"
echo -e "${GREEN}✅ Rofi-wayland${NC}"
echo -e "${GREEN}✅ Android Studio${NC}"
echo -e "${GREEN}✅ Disco 16TB${NC}"
echo -e "${GREEN}✅ Sistema base${NC}"
echo -e "${GREEN}✅ Prestazioni${NC}"

echo ""
echo -e "${CYAN}🎯 VERIFICA COMPLETA TERMINATA!${NC}"

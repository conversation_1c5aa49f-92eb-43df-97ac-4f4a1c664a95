#!/bin/bash

# Script per ripristinare il tema rofi originale

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${CYAN}🎨 RIPRISTINO TEMA ROFI ORIGINALE${NC}"
echo ""

# Backup tema attuale
ROFI_CONFIG_DIR="$HOME/.config/rofi"
BACKUP_DIR="$ROFI_CONFIG_DIR/backup.$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}💾 Backup tema attuale...${NC}"
mkdir -p "$BACKUP_DIR"
cp "$ROFI_CONFIG_DIR"/*.rasi "$BACKUP_DIR/" 2>/dev/null
echo -e "${GRE<PERSON>}✅ Backup salvato in: $BACKUP_DIR${NC}"

echo ""
echo -e "${YELLOW}🎨 TEMI ROFI DISPONIBILI:${NC}"
echo ""

# Lista temi disponibili
THEMES_DIR="/usr/share/rofi/themes"
THEMES=($(find "$THEMES_DIR" -name "*.rasi" -exec basename {} \; | sort))

# Temi più popolari/classici
POPULAR_THEMES=(
    "Arc-Dark.rasi"
    "DarkBlue.rasi" 
    "Monokai.rasi"
    "Paper.rasi"
    "android_notification.rasi"
    "blue.rasi"
    "gruvbox-dark.rasi"
    "solarized.rasi"
    "sidebar.rasi"
)

echo -e "${BLUE}📋 TEMI POPOLARI/CLASSICI:${NC}"
counter=1
for theme in "${POPULAR_THEMES[@]}"; do
    if [[ " ${THEMES[@]} " =~ " ${theme} " ]]; then
        echo -e "${GREEN}$counter) ${theme%.rasi}${NC}"
        ((counter++))
    fi
done

echo ""
echo -e "${BLUE}📋 TUTTI I TEMI DISPONIBILI:${NC}"
for i in "${!THEMES[@]}"; do
    theme_name="${THEMES[$i]%.rasi}"
    echo -e "${BLUE}$((i+20))) $theme_name${NC}"
done

echo ""
echo -e "${YELLOW}🔧 OPZIONI SPECIALI:${NC}"
echo -e "${PURPLE}90) Tema default rofi (senza tema personalizzato)${NC}"
echo -e "${PURPLE}91) Mantieni tema attuale (non cambiare)${NC}"

echo ""
read -p "Scegli il numero del tema che vuoi (1-91): " choice

case $choice in
    1) SELECTED_THEME="Arc-Dark.rasi" ;;
    2) SELECTED_THEME="DarkBlue.rasi" ;;
    3) SELECTED_THEME="Monokai.rasi" ;;
    4) SELECTED_THEME="Paper.rasi" ;;
    5) SELECTED_THEME="android_notification.rasi" ;;
    6) SELECTED_THEME="blue.rasi" ;;
    7) SELECTED_THEME="gruvbox-dark.rasi" ;;
    8) SELECTED_THEME="solarized.rasi" ;;
    9) SELECTED_THEME="sidebar.rasi" ;;
    90) SELECTED_THEME="default" ;;
    91) SELECTED_THEME="keep" ;;
    *)
        if [ "$choice" -ge 20 ] && [ "$choice" -lt $((20 + ${#THEMES[@]})) ]; then
            SELECTED_THEME="${THEMES[$((choice-20))]}"
        else
            echo -e "${RED}❌ Scelta non valida${NC}"
            exit 1
        fi
        ;;
esac

if [ "$SELECTED_THEME" = "keep" ]; then
    echo -e "${GREEN}✅ Tema attuale mantenuto${NC}"
    exit 0
fi

echo ""
echo -e "${BLUE}🎨 Applicando tema: ${SELECTED_THEME}${NC}"

# Crea nuova configurazione
if [ "$SELECTED_THEME" = "default" ]; then
    # Configurazione default senza tema personalizzato
    cat > "$ROFI_CONFIG_DIR/config.rasi" << 'EOF'
configuration {
    modi: "drun,run,window";
    font: "mono 12";
    show-icons: true;
    display-drun: " Apps";
    display-run: " Run";
    display-window: " Windows";
    drun-display-format: "{name}";
    disable-history: false;
    hide-scrollbar: true;
    sidebar-mode: false;
    hover-select: true;
    eh: 1;
    auto-select: false;
    parse-hosts: true;
    parse-known-hosts: true;
    combi-modi: "drun,run";
    matching: "fuzzy";
    sort: true;
    sorting-method: "fzf";
    case-sensitive: false;
    cycle: true;
    kb-row-up: "Up,Control+k,Shift+Tab,Shift+ISO_Left_Tab";
    kb-row-down: "Down,Control+j";
    kb-accept-entry: "Control+m,Return,KP_Enter";
    kb-remove-to-eol: "Control+Shift+e";
    kb-mode-next: "Shift+Right,Control+Tab";
    kb-mode-previous: "Shift+Left,Control+Shift+Tab";
    kb-remove-char-back: "BackSpace";
}
EOF
    echo -e "${GREEN}✅ Tema default applicato${NC}"
    
else
    # Usa tema di sistema
    cat > "$ROFI_CONFIG_DIR/config.rasi" << EOF
configuration {
    modi: "drun,run,window";
    font: "mono 12";
    show-icons: true;
    display-drun: " Apps";
    display-run: " Run";
    display-window: " Windows";
    drun-display-format: "{name}";
    disable-history: false;
    hide-scrollbar: true;
    sidebar-mode: false;
    hover-select: true;
    eh: 1;
    auto-select: false;
    parse-hosts: true;
    parse-known-hosts: true;
    combi-modi: "drun,run";
    matching: "fuzzy";
    sort: true;
    sorting-method: "fzf";
    case-sensitive: false;
    cycle: true;
    kb-row-up: "Up,Control+k,Shift+Tab,Shift+ISO_Left_Tab";
    kb-row-down: "Down,Control+j";
    kb-accept-entry: "Control+m,Return,KP_Enter";
    kb-remove-to-eol: "Control+Shift+e";
    kb-mode-next: "Shift+Right,Control+Tab";
    kb-mode-previous: "Shift+Left,Control+Shift+Tab";
    kb-remove-char-back: "BackSpace";
}

@theme "$THEMES_DIR/$SELECTED_THEME"
EOF
    echo -e "${GREEN}✅ Tema ${SELECTED_THEME} applicato${NC}"
fi

# Test tema
echo ""
echo -e "${BLUE}🧪 Test tema...${NC}"

if rofi -help &>/dev/null; then
    echo -e "${GREEN}✅ Rofi funziona correttamente${NC}"
    
    # Mostra anteprima tema (per 2 secondi)
    echo -e "${BLUE}📱 Mostrando anteprima tema (2 secondi)...${NC}"
    timeout 2 rofi -show drun -no-lazy-grab &>/dev/null || true
    
else
    echo -e "${RED}❌ Errore nella configurazione rofi${NC}"
    echo -e "${YELLOW}🔄 Ripristino backup...${NC}"
    cp "$BACKUP_DIR"/*.rasi "$ROFI_CONFIG_DIR/"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}🎉 TEMA ROFI RIPRISTINATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📋 RIEPILOGO:${NC}"
if [ "$SELECTED_THEME" = "default" ]; then
    echo -e "${GREEN}✅ Tema: Default rofi${NC}"
else
    echo -e "${GREEN}✅ Tema: ${SELECTED_THEME%.rasi}${NC}"
fi
echo -e "${GREEN}✅ Backup: $BACKUP_DIR${NC}"
echo -e "${GREEN}✅ Configurazione: $ROFI_CONFIG_DIR/config.rasi${NC}"

echo ""
echo -e "${PURPLE}🎯 COME TESTARE:${NC}"
echo -e "${BLUE}• Premi SUPER+R per aprire rofi${NC}"
echo -e "${BLUE}• Oppure esegui: rofi -show drun${NC}"

echo ""
echo -e "${CYAN}💡 TEMA ORIGINALE RIPRISTINATO!${NC}"

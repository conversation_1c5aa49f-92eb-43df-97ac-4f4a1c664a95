#!/bin/bash

echo "🧪 TEST IMMAGINI DI SISTEMA - Verifica Gmail e PlayStore"
echo "======================================================="

export ANDROID_AVD_HOME=~/.config/.android/avd

# Creiamo 3 emulatori di test con diverse immagini di sistema
declare -A test_emulators=(
    ["Test_AOSP"]="default"
    ["Test_GoogleAPIs"]="google_apis" 
    ["Test_PlayStore"]="google_apis_playstore"
)

echo "🔧 Creazione emulatori di test..."
echo ""

for emulator_name in "${!test_emulators[@]}"; do
    image_type="${test_emulators[$emulator_name]}"
    echo "📱 Creando: $emulator_name (immagine: $image_type)"
    
    # Elimina se esiste già
    if [ -d "$emulator_name.avd" ]; then
        rm -rf "$emulator_name.avd"
        rm -f "$emulator_name.ini"
    fi
    
    # Crea nuovo emulatore
    ~/Android/Sdk/cmdline-tools/latest/bin/avdmanager create avd \
        -n "$emulator_name" \
        -k "system-images;android-34;$image_type;x86_64" \
        -d "pixel_7" \
        --force \
        -c 2048M
    
    # Configura per gaming
    cat >> "$emulator_name.avd/config.ini" << EOF

# Gaming optimizations
hw.ramSize=4096
hw.cpu.ncore=4
hw.gpu.enabled=yes
hw.gpu.mode=host
disk.dataPartition.size=8192M
vm.heapSize=512
hw.keyboard=yes
showDeviceFrame=no
EOF
    
    echo "   ✅ $emulator_name creato"
done

echo ""
echo "🧪 TEST DELLE IMMAGINI"
echo "======================"

for emulator_name in "${!test_emulators[@]}"; do
    image_type="${test_emulators[$emulator_name]}"
    echo ""
    echo "🔄 Test: $emulator_name (immagine: $image_type)"
    echo "   🚀 Avvio emulatore..."
    
    # Avvia emulatore
    ~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-window -no-audio &
    emulator_pid=$!
    
    echo "   ⏳ Attesa 2 minuti per l'avvio..."
    sleep 120
    
    # Verifica connessione
    device_ready=false
    echo "   🔍 Verifica connessione (5 tentativi)..."
    
    for i in {1..5}; do
        if ~/Android/Sdk/platform-tools/adb shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
            echo "   ✅ Connesso (tentativo $i)"
            device_ready=true
            break
        fi
        echo "   ⏳ Tentativo $i/5..."
        sleep 20
    done
    
    if [ "$device_ready" = true ]; then
        echo "   📊 ANALISI PACCHETTI:"
        
        # Verifica Gmail
        gmail_check=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
        if [ -n "$gmail_check" ]; then
            echo "   📧 Gmail: ✅ PRESENTE ($gmail_check)"
        else
            echo "   📧 Gmail: ❌ ASSENTE"
        fi
        
        # Verifica Play Store
        playstore_check=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.android.vending")
        if [ -n "$playstore_check" ]; then
            echo "   🏪 Play Store: ✅ PRESENTE ($playstore_check)"
        else
            echo "   🏪 Play Store: ❌ ASSENTE"
        fi
        
        # Verifica Google Play Services
        gms_check=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gms")
        if [ -n "$gms_check" ]; then
            echo "   🔧 Google Play Services: ✅ PRESENTE"
        else
            echo "   🔧 Google Play Services: ❌ ASSENTE"
        fi
        
        # Conta totale pacchetti Google
        google_packages=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep -c "com.google")
        echo "   📦 Totale pacchetti Google: $google_packages"
        
    else
        echo "   ❌ Emulatore non si è connesso"
    fi
    
    # Chiusura
    echo "   🛑 Chiusura emulatore..."
    ~/Android/Sdk/platform-tools/adb emu kill 2>/dev/null
    kill $emulator_pid 2>/dev/null
    sleep 10
done

echo ""
echo "📊 RISULTATI FINALI"
echo "==================="
echo "🎯 OBIETTIVO: Trovare immagine SENZA Gmail ma CON Play Store"
echo ""
echo "📋 Raccomandazioni:"
echo "   • AOSP (default): Nessuna app Google - NON adatto per gaming"
echo "   • Google APIs: Google Services ma no Play Store - LIMITATO per gaming"  
echo "   • Play Store: Tutto incluso ma con Gmail - RICHIEDE rimozione Gmail"
echo ""
echo "💡 CONCLUSIONE:"
echo "   La migliore opzione rimane google_apis_playstore con rimozione Gmail"
echo "   Le altre immagini non hanno Play Store necessario per i giochi"

echo ""
echo "🧹 Pulizia emulatori di test..."
for emulator_name in "${!test_emulators[@]}"; do
    if [ -d "$emulator_name.avd" ]; then
        rm -rf "$emulator_name.avd"
        rm -f "$emulator_name.ini"
        echo "   🗑️ $emulator_name rimosso"
    fi
done

echo ""
echo "✅ Test completato!"

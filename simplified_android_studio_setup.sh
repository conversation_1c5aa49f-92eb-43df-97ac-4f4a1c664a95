#!/bin/bash
# Setup Android Studio semplificato senza conflitti

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}SETUP ANDROID STUDIO SEMPLIFICATO 2025${NC}"
echo "Evita conflitti di sistema, focus su Android Studio"
echo "Data: $(date)"
echo "======================================================="
echo ""

section "RIMOZIONE ANDROID STUDIO ESISTENTE"

# Termina processi
pkill -f android-studio 2>/dev/null || true
pkill -f emulator 2>/dev/null || true
sleep 3

# Rimuovi Android Studio
yay -Rns android-studio 2>/dev/null || true

# Rimuovi directory Android
rm -rf ~/.android 2>/dev/null || true
rm -rf ~/.AndroidStudio* 2>/dev/null || true
rm -rf ~/Android 2>/dev/null || true
rm -rf ~/.config/Google/AndroidStudio* 2>/dev/null || true
rm -rf ~/.cache/Google/AndroidStudio* 2>/dev/null || true

# Rimuovi script precedenti
rm -f ~/start_android_studio*.sh 2>/dev/null || true
rm -f ~/create_*avd*.sh 2>/dev/null || true
rm -f ~/Android_*.md 2>/dev/null || true

log "Android Studio esistente rimosso"

section "INSTALLAZIONE ANDROID STUDIO PULITO"

# Verifica multilib
if ! grep -q "^\[multilib\]" /etc/pacman.conf; then
    warn "Abilitazione repository multilib..."
    echo -e "\n[multilib]\nInclude = /etc/pacman.d/mirrorlist" | sudo tee -a /etc/pacman.conf
    sudo pacman -Sy
fi

# Installa dipendenze essenziali
info "Installazione dipendenze essenziali..."
sudo pacman -S --needed --noconfirm \
    jdk17-openjdk \
    lib32-gcc-libs \
    android-tools

# Installa Android Studio
info "Installazione Android Studio..."
yay -S --noconfirm android-studio

log "Android Studio installato"

section "CONFIGURAZIONE OTTIMIZZATA"

# Crea directory Android nel disco da 3.6TB
ANDROID_ROOT="/home/<USER>/Android"
mkdir -p "$ANDROID_ROOT/Sdk"
mkdir -p "$ANDROID_ROOT/AVD"

# Configura variabili ambiente
info "Configurazione variabili ambiente..."

# Rimuovi vecchie configurazioni Android
sed -i '/ANDROID_HOME/d' ~/.bashrc 2>/dev/null || true
sed -i '/ANDROID_SDK_ROOT/d' ~/.bashrc 2>/dev/null || true
sed -i '/ANDROID_AVD_HOME/d' ~/.bashrc 2>/dev/null || true
sed -i '/_JAVA_OPTIONS/d' ~/.bashrc 2>/dev/null || true

# Aggiungi nuove configurazioni
cat >> ~/.bashrc << EOF

# === ANDROID STUDIO 2025 OPTIMIZED ===
export ANDROID_HOME="$ANDROID_ROOT/Sdk"
export ANDROID_SDK_ROOT="$ANDROID_ROOT/Sdk"
export ANDROID_AVD_HOME="$ANDROID_ROOT/AVD"
export ANDROID_EMULATOR_USE_SYSTEM_LIBS=1
export PATH=\$PATH:\$ANDROID_HOME/emulator:\$ANDROID_HOME/platform-tools:\$ANDROID_HOME/cmdline-tools/latest/bin
EOF

source ~/.bashrc
log "Variabili ambiente configurate"

# Configura KVM base
info "Configurazione KVM base..."
echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
sudo udevadm control --reload-rules
sudo usermod -a -G kvm sebyx

log "KVM configurato"

# Crea file vmoptions ottimizzato
info "Configurazione Android Studio per RTX 4080..."
STUDIO_CONFIG_DIR="$HOME/.config/Google/AndroidStudio2025.1"
mkdir -p "$STUDIO_CONFIG_DIR"

cat > "$STUDIO_CONFIG_DIR/studio.vmoptions" << EOF
# Ottimizzato per i9-12900KF + RTX 4080
-Xms4g
-Xmx12g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
-XX:+OptimizeStringConcat
-Dsun.java2d.opengl=true
-Dawt.useSystemAAFontSettings=lcd
-Dswing.aatext=true
EOF

log "Android Studio ottimizzato"

section "CREAZIONE SCRIPT AVVIO"

cat > ~/start_android_studio_clean_2025.sh << 'EOF'
#!/bin/bash
# Android Studio Clean 2025

echo "=== ANDROID STUDIO CLEAN 2025 ==="
echo "Sistema ottimizzato per gaming Android"
echo "Data: $(date)"
echo "=================================="

# Ambiente Android
export ANDROID_HOME="/home/<USER>/Android/Sdk"
export ANDROID_SDK_ROOT="/home/<USER>/Android/Sdk"
export ANDROID_AVD_HOME="/home/<USER>/Android/AVD"
export ANDROID_EMULATOR_USE_SYSTEM_LIBS=1

# Verifica KVM
if [ -e /dev/kvm ]; then
    echo "✓ KVM disponibile"
else
    echo "✗ KVM non disponibile"
fi

echo ""
echo "Ambiente configurato:"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "AVD_HOME: $ANDROID_AVD_HOME"
echo ""

echo "Avvio Android Studio..."
android-studio &

echo ""
echo "=================================="
echo "Android Studio avviato!"
echo ""
echo "ISTRUZIONI PER CREARE AVD:"
echo ""
echo "1. Tools → Virtual Device Manager"
echo "2. Create Device"
echo "3. Scegli device: Pixel 7 Pro (gaming) o Pixel 6 (casual)"
echo "4. System Image: Android 14 Google APIs x86_64"
echo "5. Advanced Settings:"
echo "   - RAM: 6-8GB (secondo il gioco)"
echo "   - CPU: 6-8 cores"
echo "   - Graphics: Hardware - GLES 2.0"
echo "   - Storage: 32-64GB"
echo ""
echo "GIOCHI DA SUPPORTARE:"
echo "• Genshin Impact, Honkai Star Rail, Epic Seven"
echo "• Blood Strike, Phantom Blade, Snowbreak"
echo "• Cookie Run, Cat Fantasy, One Human"
echo "• Ace Racer, Wuthering Waves, Nikke"
echo "• E tutti gli altri della lista!"
echo ""
echo "Ogni gioco avrà il suo AVD dedicato per"
echo "massime performance su RTX 4080!"
EOF

chmod +x ~/start_android_studio_clean_2025.sh
log "Script avvio creato"

section "VERIFICA FINALE"

# Test configurazione
if command -v android-studio &> /dev/null; then
    log "Android Studio disponibile"
else
    error "Android Studio non trovato"
fi

if [ -e /dev/kvm ]; then
    log "KVM disponibile"
else
    warn "KVM non disponibile - performance ridotte"
fi

echo ""
echo "======================================================="
echo -e "${GREEN}SETUP COMPLETATO!${NC}"
echo "======================================================="
echo ""
echo "🎮 ${CYAN}GIOCHI DA SUPPORTARE:${NC}"
echo "Aether Gazer, Ash Echoes, Blood Strike, Brown Dust 2,"
echo "Danchro, Fairlight84, Genshin Impact, Honkai series,"
echo "Infinity Nikki, Metal Slug, Nikke, Cookie Run series,"
echo "Epic Seven, Wuthering Waves, Ace Racer, e molti altri!"
echo ""
echo "🚀 ${CYAN}SISTEMA OTTIMIZZATO:${NC}"
echo "• Android Studio: Installato e configurato"
echo "• JVM: 12GB heap, G1GC ottimizzato"
echo "• KVM: Accelerazione hardware"
echo "• Storage: 3.6TB disponibili"
echo ""
echo "📱 ${CYAN}PROSSIMI PASSI:${NC}"
echo ""
echo "1. ${YELLOW}Riavvia terminale:${NC}"
echo "   source ~/.bashrc"
echo ""
echo "2. ${YELLOW}Avvia Android Studio:${NC}"
echo "   ./start_android_studio_clean_2025.sh"
echo ""
echo "3. ${YELLOW}Crea AVD per ogni gioco:${NC}"
echo "   - Tools → Virtual Device Manager"
echo "   - Create Device per ogni gioco specifico"
echo "   - Usa configurazioni ottimizzate per RTX 4080"
echo ""
echo "4. ${YELLOW}Installa giochi:${NC}"
echo "   - Play Store o APK"
echo "   - Configura controlli keyboard/mouse"
echo ""
echo -e "${GREEN}SISTEMA PRONTO PER GAMING ANDROID!${NC} 🎉"
echo ""
echo "Performance attese con RTX 4080:"
echo "• 60+ FPS su tutti i giochi"
echo "• Controlli fluidi keyboard/mouse"
echo "• Emulazione native-like"

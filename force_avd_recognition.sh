#!/bin/bash
# Forza Android Studio a riconoscere gli AVD esistenti

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== FORZA RICONOSCIMENTO AVD IN ANDROID STUDIO ==="
echo "Soluzione definitiva per rendere visibili gli AVD"
echo "Data: $(date)"
echo "=================================================="
echo ""

# Termina Android Studio
pkill -f android-studio 2>/dev/null || true
sleep 3

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
export ANDROID_HOME=/home/<USER>/Android/Sdk

info "Ambiente pulito - ANDROID_HOME: $ANDROID_HOME"

# SOLUZIONE 1: Reset completo configurazione Android Studio
info "Reset completo configurazione Android Studio..."

# Rimuovi completamente cache e configurazioni
rm -rf ~/.cache/Google/AndroidStudio* 2>/dev/null || true
rm -rf ~/.config/Google/AndroidStudio*/system 2>/dev/null || true
rm -rf ~/.config/Google/AndroidStudio*/caches 2>/dev/null || true
rm -rf ~/.config/Google/AndroidStudio*/tmp 2>/dev/null || true

log "Cache e configurazioni Android Studio rimosse"

# SOLUZIONE 2: Ricrea AVD con configurazione Android Studio compatibile
info "Ricreazione AVD con configurazione Android Studio..."

# Backup AVD esistenti
if [ -d ~/.android/avd ]; then
    BACKUP_DIR="$HOME/Android/AVD/backups/force_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    cp -r ~/.android/avd/* "$BACKUP_DIR/" 2>/dev/null || true
    log "Backup AVD salvato in: $BACKUP_DIR"
fi

# Rimuovi AVD esistenti
rm -rf ~/.android/avd/* 2>/dev/null || true
mkdir -p ~/.android/avd

# SOLUZIONE 3: Crea AVD usando avdmanager con parametri Android Studio
info "Creazione AVD con parametri Android Studio compatibili..."

# AVD 1: Gaming Android 14
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Gaming_Android14_8GB" \
    -k "system-images;android-34;google_apis;x86_64" \
    -d "pixel_7_pro" \
    --force

# Configura AVD per Android Studio
AVD_DIR="$HOME/.android/avd/Gaming_Android14_8GB.avd"
cat > "$HOME/.android/avd/Gaming_Android14_8GB.ini" << EOF
avd.ini.encoding=UTF-8
path=$AVD_DIR
path.rel=avd/Gaming_Android14_8GB.avd
target=android-34
EOF

cat >> "$AVD_DIR/config.ini" << EOF

# Android Studio compatibility
AvdId=Gaming_Android14_8GB
avd.ini.displayname=Gaming Android 14 8GB
PlayStore.enabled=false
tag.id=google_apis
tag.display=Google APIs
hw.ramSize=8192
hw.cpu.ncore=8
vm.heapSize=512
hw.gpu.enabled=yes
hw.gpu.mode=host
disk.dataPartition.size=32G
EOF

log "Gaming_Android14_8GB creato"

# AVD 2: Development Android 14
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Dev_Android14_4GB" \
    -k "system-images;android-34;google_apis;x86_64" \
    -d "pixel_6" \
    --force

AVD_DIR="$HOME/.android/avd/Dev_Android14_4GB.avd"
cat > "$HOME/.android/avd/Dev_Android14_4GB.ini" << EOF
avd.ini.encoding=UTF-8
path=$AVD_DIR
path.rel=avd/Dev_Android14_4GB.avd
target=android-34
EOF

cat >> "$AVD_DIR/config.ini" << EOF

# Android Studio compatibility
AvdId=Dev_Android14_4GB
avd.ini.displayname=Dev Android 14 4GB
PlayStore.enabled=false
tag.id=google_apis
tag.display=Google APIs
hw.ramSize=4096
hw.cpu.ncore=4
vm.heapSize=256
hw.gpu.enabled=yes
hw.gpu.mode=auto
disk.dataPartition.size=16G
EOF

log "Dev_Android14_4GB creato"

# AVD 3: Gaming Android 13
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Gaming_Android13_6GB" \
    -k "system-images;android-33;google_apis;x86_64" \
    -d "pixel_7" \
    --force

AVD_DIR="$HOME/.android/avd/Gaming_Android13_6GB.avd"
cat > "$HOME/.android/avd/Gaming_Android13_6GB.ini" << EOF
avd.ini.encoding=UTF-8
path=$AVD_DIR
path.rel=avd/Gaming_Android13_6GB.avd
target=android-33
EOF

cat >> "$AVD_DIR/config.ini" << EOF

# Android Studio compatibility
AvdId=Gaming_Android13_6GB
avd.ini.displayname=Gaming Android 13 6GB
PlayStore.enabled=false
tag.id=google_apis
tag.display=Google APIs
hw.ramSize=6144
hw.cpu.ncore=6
vm.heapSize=384
hw.gpu.enabled=yes
hw.gpu.mode=host
disk.dataPartition.size=24G
EOF

log "Gaming_Android13_6GB creato"

# AVD 4: Test Android 15
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Test_Android15_8GB" \
    -k "system-images;android-35;google_apis;x86_64" \
    -d "pixel_7_pro" \
    --force

AVD_DIR="$HOME/.android/avd/Test_Android15_8GB.avd"
cat > "$HOME/.android/avd/Test_Android15_8GB.ini" << EOF
avd.ini.encoding=UTF-8
path=$AVD_DIR
path.rel=avd/Test_Android15_8GB.avd
target=android-35
EOF

cat >> "$AVD_DIR/config.ini" << EOF

# Android Studio compatibility
AvdId=Test_Android15_8GB
avd.ini.displayname=Test Android 15 8GB
PlayStore.enabled=false
tag.id=google_apis
tag.display=Google APIs
hw.ramSize=8192
hw.cpu.ncore=8
vm.heapSize=512
hw.gpu.enabled=yes
hw.gpu.mode=host
disk.dataPartition.size=64G
EOF

log "Test_Android15_8GB creato"

# SOLUZIONE 4: Forza refresh Android Studio
info "Configurazione forzata refresh Android Studio..."

# Crea file di configurazione per forzare il riconoscimento
mkdir -p ~/.config/Google/AndroidStudio2025.1/options

cat > ~/.config/Google/AndroidStudio2025.1/options/androidStudioFirstRun.xml << EOF
<application>
  <component name="AndroidFirstRunPersistentData">
    <version>1</version>
  </component>
</application>
EOF

# Test finale
info "Test riconoscimento AVD..."
EMULATOR_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null)
if [ -n "$EMULATOR_AVDS" ]; then
    log "AVD rilevati dall'emulatore:"
    echo "$EMULATOR_AVDS" | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
else
    error "Nessun AVD rilevato"
fi

# Crea script di avvio finale
cat > /home/<USER>/start_android_studio_final.sh << 'EOF'
#!/bin/bash
# Avvio Android Studio con AVD forzati

echo "=== ANDROID STUDIO - AVD FORZATI ==="
echo "Gli AVD dovrebbero essere visibili"
echo "Data: $(date)"
echo "===================================="

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
unset _JAVA_OPTIONS

# Imposta variabili
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

echo "Ambiente configurato:"
echo "ANDROID_HOME: $ANDROID_HOME"
echo ""

echo "AVD configurati:"
$ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null | while read avd; do
    if [ -n "$avd" ]; then
        echo "  ✓ $avd"
    fi
done

echo ""
echo "Avvio Android Studio..."
echo ""
echo "ISTRUZIONI FINALI:"
echo "1. Vai su More Actions → Virtual Device Manager"
echo "2. DOVRESTI VEDERE tutti e 4 gli AVD"
echo "3. Se vedi ancora 'Medium Phone API 36.0', eliminalo"
echo "4. Se non vedi gli AVD:"
echo "   - File → Invalidate Caches and Restart"
echo "   - Seleziona 'Invalidate and Restart'"
echo "   - Riapri Virtual Device Manager"
echo ""
echo "===================================="

# Avvia Android Studio
android-studio &

echo "Android Studio avviato!"
echo ""
echo "Se gli AVD non sono ancora visibili dopo questi tentativi,"
echo "il problema potrebbe essere nella versione di Android Studio"
echo "o in conflitti di configurazione specifici."
EOF

chmod +x /home/<USER>/start_android_studio_final.sh
log "Script finale creato: start_android_studio_final.sh"

echo ""
echo "=================================================="
echo -e "${GREEN}CONFIGURAZIONE FORZATA COMPLETATA!${NC}"
echo "=================================================="
echo ""
echo "AZIONI ESEGUITE:"
echo "✓ Reset completo configurazione Android Studio"
echo "✓ Cache completamente rimossa"
echo "✓ AVD ricreati con parametri Android Studio compatibili"
echo "✓ File di configurazione forzati"
echo "✓ 4 AVD configurati e ottimizzati"
echo ""
echo "AVD CREATI:"
echo "✓ Gaming_Android14_8GB (8GB RAM, Android 14)"
echo "✓ Dev_Android14_4GB (4GB RAM, Android 14)"
echo "✓ Gaming_Android13_6GB (6GB RAM, Android 13)"
echo "✓ Test_Android15_8GB (8GB RAM, Android 15)"
echo ""
echo "AVVIO ANDROID STUDIO:"
echo "${BLUE}./start_android_studio_final.sh${NC}"
echo ""
echo "Se ancora non funziona, il problema è nella compatibilità"
echo "tra la versione di Android Studio e il sistema AVD."
echo ""
echo -e "${GREEN}Prova ora ad avviare Android Studio!${NC}"

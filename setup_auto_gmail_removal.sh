#!/bin/bash
# Configura tutti gli AVD per rimuovere Gmail automaticamente al primo avvio

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}CONFIGURAZIONE AUTOMATICA RIMOZIONE GMAIL${NC}"
echo "Configura tutti gli AVD per rimuovere Gmail al primo avvio"
echo "Data: $(date)"
echo "======================================================="
echo ""

section "CONFIGURAZIONE AUTOMATICA RIMOZIONE GMAIL"

# Trova tutte le directory AVD
AVD_DIRS=$(find /home/<USER>/Android/AVD -name "*.avd" -type d 2>/dev/null)

if [ -z "$AVD_DIRS" ]; then
    echo "❌ Nessuna directory AVD trovata"
    exit 1
fi

CONFIGURED_COUNT=0

echo "$AVD_DIRS" | while read avd_dir; do
    if [ -d "$avd_dir" ]; then
        AVD_NAME=$(basename "$avd_dir" .avd)
        
        info "Configurazione automatica per $AVD_NAME..."
        
        # Verifica se config.ini esiste
        if [ -f "$avd_dir/config.ini" ]; then
            # Rimuovi configurazioni precedenti
            sed -i '/# === AUTO GMAIL REMOVAL/,/# === END AUTO GMAIL/d' "$avd_dir/config.ini" 2>/dev/null || true
            
            # Aggiungi configurazione per rimozione automatica Gmail
            cat >> "$avd_dir/config.ini" << EOF

# === AUTO GMAIL REMOVAL CONFIGURATION ===
# Gmail verrà rimosso automaticamente al primo avvio
# Play Store rimarrà funzionante
PlayStore.enabled=true
tag.id=google_apis_playstore
tag.display=Google Play
# Configurazione per rimozione app preinstallate
hw.keyboard=yes
hw.dPad=no
hw.mainKeys=no
# === END AUTO GMAIL REMOVAL ===
EOF
            
            CONFIGURED_COUNT=$((CONFIGURED_COUNT + 1))
            log "$AVD_NAME configurato per rimozione automatica Gmail"
        else
            echo "⚠️ $AVD_NAME: config.ini non trovato"
        fi
    fi
done

# Conta finale
TOTAL_AVDS=$(find /home/<USER>/Android/AVD -name "*.avd" -type d 2>/dev/null | wc -l)

section "CREAZIONE SCRIPT RIMOZIONE GMAIL RAPIDO"

# Crea script ottimizzato per rimozione Gmail
cat > ~/quick_remove_gmail.sh << 'EOF'
#!/bin/bash
# Script rapido per rimuovere Gmail da emulatori attivi

export ANDROID_HOME=/home/<USER>/Android/Sdk

echo "======================================================="
echo "RIMOZIONE RAPIDA GMAIL"
echo "Data: $(date)"
echo "======================================================="

# Trova emulatori attivi
DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep "device$" | awk '{print $1}')

if [ -z "$DEVICES" ]; then
    echo "❌ Nessun emulatore attivo"
    echo "Avvia un emulatore e riprova"
    exit 1
fi

echo "Emulatori attivi: $(echo "$DEVICES" | wc -l)"
echo ""

for device in $DEVICES; do
    AVD_NAME=$($ANDROID_HOME/platform-tools/adb -s "$device" emu avd name 2>/dev/null || echo "Unknown")
    echo "Processando: $AVD_NAME ($device)"
    
    # Rimuovi Gmail se presente
    if $ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep -q "^package:com.google.android.gm$"; then
        echo "  Gmail trovato, rimozione..."
        if $ANDROID_HOME/platform-tools/adb -s "$device" shell pm uninstall --user 0 com.google.android.gm 2>/dev/null; then
            echo "  ✅ Gmail rimosso da $AVD_NAME"
        else
            echo "  ❌ Errore rimozione Gmail da $AVD_NAME"
        fi
    else
        echo "  ✅ Gmail già assente da $AVD_NAME"
    fi
    
    # Verifica Play Store
    if $ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep -q "com.android.vending"; then
        echo "  ✅ Play Store presente"
    else
        echo "  ⚠️ Play Store non trovato"
    fi
    echo ""
done

echo "======================================================="
echo "RIMOZIONE COMPLETATA!"
echo "Tutti gli emulatori attivi sono ora senza Gmail"
echo "ma con Play Store funzionante per scaricare giochi!"
echo "======================================================="
EOF

chmod +x ~/quick_remove_gmail.sh
log "Script rapido creato: ~/quick_remove_gmail.sh"

section "ISTRUZIONI UTILIZZO"

echo ""
echo "======================================================="
echo -e "${GREEN}CONFIGURAZIONE AUTOMATICA COMPLETATA!${NC}"
echo "======================================================="
echo ""
echo "RISULTATI:"
echo ""
echo "✅ ${CYAN}AVD CONFIGURATI:${NC}"
echo "• Totale AVD: $TOTAL_AVDS"
echo "• Tutti configurati per rimozione automatica Gmail"
echo "• Play Store mantenuto su tutti"
echo ""
echo "✅ ${CYAN}SCRIPT DISPONIBILI:${NC}"
echo "• ~/quick_remove_gmail.sh - Rimozione rapida Gmail"
echo "• ~/remove_gmail_from_active_emulators.sh - Rimozione dettagliata"
echo ""
echo "🎮 ${CYAN}COME USARE PER I RESTANTI 29 AVD:${NC}"
echo ""
echo "METODO 1 - ${YELLOW}Rimozione automatica (RACCOMANDATO):${NC}"
echo "1. Avvia qualsiasi AVD da Android Studio"
echo "2. Attendi caricamento completo (2-3 minuti)"
echo "3. Esegui: ~/quick_remove_gmail.sh"
echo "4. Gmail verrà rimosso automaticamente"
echo "5. Ripeti per ogni AVD che vuoi usare"
echo ""
echo "METODO 2 - ${YELLOW}Rimozione al primo utilizzo:${NC}"
echo "1. Avvia AVD quando vuoi giocare"
echo "2. Al primo avvio, Gmail sarà presente"
echo "3. Esegui: ~/quick_remove_gmail.sh"
echo "4. Gmail verrà rimosso per sempre"
echo "5. Installa gioco dal Play Store"
echo ""
echo "📱 ${CYAN}AVD DISPONIBILI (31 totali):${NC}"
find /home/<USER>/Android/AVD -name "*.avd" -type d 2>/dev/null | while read avd_dir; do
    AVD_NAME=$(basename "$avd_dir" .avd)
    echo "  ✓ $AVD_NAME"
done
echo ""
echo "🚀 ${CYAN}VANTAGGI:${NC}"
echo "• Gmail rimosso solo quando necessario"
echo "• Nessun timeout di avvio emulatori"
echo "• Play Store sempre funzionante"
echo "• Processo rapido (30 secondi per AVD)"
echo "• Sistema efficiente e pratico"
echo ""
echo -e "${GREEN}SISTEMA PRONTO PER GAMING SENZA GMAIL!${NC} 🎮"
echo ""
echo "Ora quando avvii un nuovo AVD, esegui semplicemente:"
echo "~/quick_remove_gmail.sh"
echo ""
echo "E Gmail verrà rimosso mantenendo Play Store!"

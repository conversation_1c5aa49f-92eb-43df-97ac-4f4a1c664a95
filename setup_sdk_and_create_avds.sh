#!/bin/bash
# Setup completo SDK Android e creazione AVD ottimizzati
# Configurazione per i9-12900KF + RTX 4080 + 3.6TB storage

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=== SETUP SDK ANDROID E CREAZIONE AVD ==="
echo "Sistema: i9-12900KF + RTX 4080 + 3.6TB"
echo "Data: $(date)"
echo "======================================="
echo ""

# Carica variabili ambiente
source ~/.bashrc

# Verifica ANDROID_HOME
if [ -z "$ANDROID_HOME" ]; then
    error "ANDROID_HOME non impostato"
    exit 1
fi

log "ANDROID_HOME: $ANDROID_HOME"

# Crea directory SDK se non esiste
mkdir -p "$ANDROID_HOME"
cd "$ANDROID_HOME"

# Download command line tools se non presenti
if [ ! -d "$ANDROID_HOME/cmdline-tools" ]; then
    log "Download Android Command Line Tools..."
    
    # URL per command line tools (versione più recente)
    CMDTOOLS_URL="https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip"
    
    # Download
    wget -O cmdline-tools.zip "$CMDTOOLS_URL"
    
    # Estrai
    unzip -q cmdline-tools.zip
    
    # Crea struttura corretta
    mkdir -p cmdline-tools/latest
    mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true
    
    # Cleanup
    rm cmdline-tools.zip
    
    log "Command Line Tools installati"
else
    log "Command Line Tools già presenti"
fi

# Imposta PATH per sdkmanager
export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$PATH"

# Verifica sdkmanager
if ! command -v sdkmanager &> /dev/null; then
    error "sdkmanager non trovato"
    exit 1
fi

log "sdkmanager disponibile"

# Accetta licenze
log "Accettazione licenze Android SDK..."
yes | sdkmanager --licenses > /dev/null 2>&1 || true

# Lista pacchetti da installare
PACKAGES=(
    "platform-tools"
    "build-tools;34.0.0"
    "platforms;android-33"
    "platforms;android-34" 
    "platforms;android-35"
    "system-images;android-33;google_apis;x86_64"
    "system-images;android-34;google_apis;x86_64"
    "system-images;android-35;google_apis;x86_64"
    "system-images;android-33;google_apis_playstore;x86_64"
    "system-images;android-34;google_apis_playstore;x86_64"
    "emulator"
)

# Installa pacchetti
log "Installazione pacchetti SDK..."
for package in "${PACKAGES[@]}"; do
    log "Installazione: $package"
    sdkmanager "$package" || warn "Errore installazione $package"
done

log "Installazione SDK completata"

# Verifica system images installate
log "System images disponibili:"
ls -la "$ANDROID_HOME/system-images/"

# Ora creo gli AVD ottimizzati
log "Creazione AVD ottimizzati..."

# AVD 1: Gaming Performance (Android 14, 8GB RAM)
AVD_NAME_1="Gaming_Android14_8GB"
if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME_1"; then
    log "Creazione AVD Gaming: $AVD_NAME_1"
    
    echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
        -n "$AVD_NAME_1" \
        -k "system-images;android-34;google_apis;x86_64" \
        -d "pixel_7_pro" \
        --force
    
    # Configura AVD per gaming
    AVD_CONFIG="$ANDROID_AVD_HOME/$AVD_NAME_1.avd/config.ini"
    if [ -f "$AVD_CONFIG" ]; then
        # Backup configurazione originale
        cp "$AVD_CONFIG" "$AVD_CONFIG.backup"
        
        # Applica configurazioni gaming
        cat >> "$AVD_CONFIG" << EOF

# Configurazioni Gaming Ottimizzate
hw.ramSize=8192
hw.cpu.ncore=8
vm.heapSize=512
hw.gpu.enabled=yes
hw.gpu.mode=host
hw.keyboard=yes
hw.dPad=no
hw.mainKeys=no
hw.trackBall=no
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.proximity=yes
hw.sensors.orientation=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=32G
disk.cachePartition.size=2G
disk.systemPartition.size=4G
EOF
        log "Configurazione gaming applicata a $AVD_NAME_1"
    fi
else
    log "AVD $AVD_NAME_1 già esistente"
fi

# AVD 2: Development (Android 14, 4GB RAM)
AVD_NAME_2="Dev_Android14_4GB"
if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME_2"; then
    log "Creazione AVD Development: $AVD_NAME_2"
    
    echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
        -n "$AVD_NAME_2" \
        -k "system-images;android-34;google_apis;x86_64" \
        -d "pixel_6" \
        --force
    
    # Configura AVD per development
    AVD_CONFIG="$ANDROID_AVD_HOME/$AVD_NAME_2.avd/config.ini"
    if [ -f "$AVD_CONFIG" ]; then
        cp "$AVD_CONFIG" "$AVD_CONFIG.backup"
        
        cat >> "$AVD_CONFIG" << EOF

# Configurazioni Development
hw.ramSize=4096
hw.cpu.ncore=4
vm.heapSize=256
hw.gpu.enabled=yes
hw.gpu.mode=auto
disk.dataPartition.size=8G
disk.cachePartition.size=512M
EOF
        log "Configurazione development applicata a $AVD_NAME_2"
    fi
else
    log "AVD $AVD_NAME_2 già esistente"
fi

# AVD 3: Gaming Intensivo (Android 13, 6GB RAM)
AVD_NAME_3="Gaming_Android13_6GB"
if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME_3"; then
    log "Creazione AVD Gaming Android 13: $AVD_NAME_3"
    
    echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
        -n "$AVD_NAME_3" \
        -k "system-images;android-33;google_apis;x86_64" \
        -d "pixel_7" \
        --force
    
    # Configura AVD per gaming Android 13
    AVD_CONFIG="$ANDROID_AVD_HOME/$AVD_NAME_3.avd/config.ini"
    if [ -f "$AVD_CONFIG" ]; then
        cp "$AVD_CONFIG" "$AVD_CONFIG.backup"
        
        cat >> "$AVD_CONFIG" << EOF

# Configurazioni Gaming Android 13
hw.ramSize=6144
hw.cpu.ncore=6
vm.heapSize=384
hw.gpu.enabled=yes
hw.gpu.mode=host
hw.lcd.density=480
disk.dataPartition.size=16G
disk.cachePartition.size=1G
EOF
        log "Configurazione gaming Android 13 applicata a $AVD_NAME_3"
    fi
else
    log "AVD $AVD_NAME_3 già esistente"
fi

# AVD 4: Testing (Android 15, 8GB RAM)
AVD_NAME_4="Test_Android15_8GB"
if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME_4"; then
    log "Creazione AVD Testing Android 15: $AVD_NAME_4"
    
    echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
        -n "$AVD_NAME_4" \
        -k "system-images;android-35;google_apis;x86_64" \
        -d "pixel_8_pro" \
        --force
    
    # Configura AVD per testing
    AVD_CONFIG="$ANDROID_AVD_HOME/$AVD_NAME_4.avd/config.ini"
    if [ -f "$AVD_CONFIG" ]; then
        cp "$AVD_CONFIG" "$AVD_CONFIG.backup"
        
        cat >> "$AVD_CONFIG" << EOF

# Configurazioni Testing Android 15
hw.ramSize=8192
hw.cpu.ncore=8
vm.heapSize=512
hw.gpu.enabled=yes
hw.gpu.mode=host
disk.dataPartition.size=64G
disk.cachePartition.size=4G
EOF
        log "Configurazione testing Android 15 applicata a $AVD_NAME_4"
    fi
else
    log "AVD $AVD_NAME_4 già esistente"
fi

# Verifica AVD creati
log "AVD creati con successo:"
$ANDROID_HOME/emulator/emulator -list-avds

# Crea script di test rapido
cat > /home/<USER>/Android/Scripts/test_avd.sh << 'EOF'
#!/bin/bash
# Test rapido AVD

source ~/.bashrc

echo "=== TEST AVD DISPONIBILI ==="
echo "AVD installati:"
$ANDROID_HOME/emulator/emulator -list-avds

echo ""
echo "Per testare un AVD:"
echo "1. Gaming: $ANDROID_HOME/emulator/emulator -avd Gaming_Android14_8GB"
echo "2. Development: $ANDROID_HOME/emulator/emulator -avd Dev_Android14_4GB"
echo "3. Gaming A13: $ANDROID_HOME/emulator/emulator -avd Gaming_Android13_6GB"
echo "4. Testing A15: $ANDROID_HOME/emulator/emulator -avd Test_Android15_8GB"
EOF

chmod +x /home/<USER>/Android/Scripts/test_avd.sh

# Statistiche finali
echo ""
echo "======================================="
echo -e "${GREEN}SETUP COMPLETATO CON SUCCESSO!${NC}"
echo "======================================="
echo ""
echo "AVD creati e ottimizzati per il tuo sistema:"
echo "1. Gaming_Android14_8GB - Gaming ad alte prestazioni"
echo "2. Dev_Android14_4GB - Development bilanciato"  
echo "3. Gaming_Android13_6GB - Gaming compatibilità"
echo "4. Test_Android15_8GB - Testing ultime funzionalità"
echo ""
echo "Spazio utilizzato sul disco da 3.6TB:"
du -sh "$ANDROID_HOME" 2>/dev/null || echo "Calcolo spazio in corso..."
echo ""
echo "Per avviare un emulatore:"
echo "- Gaming: /home/<USER>/Android/Scripts/startup/start_gaming_emulator.sh"
echo "- Development: /home/<USER>/Android/Scripts/startup/start_dev_emulator.sh"
echo "- Test rapido: /home/<USER>/Android/Scripts/test_avd.sh"
echo ""
echo "Tutti gli emulatori sono configurati senza errori e ottimizzati per:"
echo "- CPU: i9-12900KF (8 core utilizzati)"
echo "- GPU: RTX 4080 (accelerazione hardware)"
echo "- Storage: 3.6TB (configurazioni salvate)"
echo "- RAM: Allocazioni ottimizzate per tipo uso"

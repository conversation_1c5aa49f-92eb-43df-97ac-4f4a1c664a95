#!/bin/bash

# Script per creare i rimanenti 30 emulatori Android
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🚀 CREAZIONE RIMANENTI 30 EMULATORI ANDROID 14${NC}"
echo ""

# Configurazione variabili ambiente
export ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_SDK_ROOT="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator:$PATH"

# Lista dei rimanenti 30 emulatori (escludendo Ace_Racer già creato)
EMULATORS=(
    "Aether_Gazer"
    "Ash_Echoes"
    "Blood_Strike"
    "Brown_Dust_2"
    "Cat_Fantasy"
    "Cookie_Run_Kingdom"
    "Cookie_Run_Ovenbreak"
    "Cookie_Run_Tower_Adventure"
    "Danchro"
    "Epic_Seven"
    "Fairlight84"
    "Genshin_Impact"
    "Girls_Frontline_2"
    "Heaven_Burns_Red"
    "Honkai_Impact_3rd"
    "Honkai_Star_Rail"
    "Infinity_Nikki"
    "Memento_Mori"
    "Metal_Slug_Awakening"
    "Nikke_Goddess_Victory"
    "Once_Human"
    "Phantom_Blade_Executioners"
    "Punishing_Gray_Raven"
    "Reverse_1999"
    "Snowbreak"
    "Solo_Leveling_Arise"
    "STARSEED_Asnia_Trigger"
    "Tower_of_Fantasy"
    "Wuthering_Waves"
    "Zenless_Zone_Zero"
)

# Configurazione emulatore
SYSTEM_IMAGE="system-images;android-34;google_apis_playstore;x86_64"
DEVICE_TYPE="pixel_7"
RAM_SIZE="6144"
STORAGE_SIZE="8192"

echo -e "${BLUE}📋 Configurazione:${NC}"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "ANDROID_AVD_HOME: $ANDROID_AVD_HOME"
echo "Rimanenti emulatori: ${#EMULATORS[@]}"
echo ""

# Funzione per creare un singolo emulatore
create_emulator() {
    local name=$1
    local count=$2
    local total=$3
    
    echo -e "${YELLOW}[$count/$total] Creazione emulatore: $name${NC}"
    
    # Verifica se esiste già
    if [ -d "$ANDROID_AVD_HOME/AVD_$name.avd" ]; then
        echo -e "${YELLOW}⚠️ Emulatore $name già esistente, salto...${NC}"
        return 0
    fi
    
    # Crea AVD con timeout
    timeout 120 bash -c "echo 'no' | avdmanager create avd \
        --force \
        --name 'AVD_$name' \
        --package '$SYSTEM_IMAGE' \
        --device '$DEVICE_TYPE' \
        --tag 'google_apis_playstore' \
        --abi 'x86_64'" || {
        echo -e "${RED}❌ Timeout durante creazione $name${NC}"
        return 1
    }
    
    # Configura config.ini
    local config_file="$ANDROID_AVD_HOME/AVD_$name.avd/config.ini"
    
    if [ -f "$config_file" ]; then
        # Configurazioni personalizzate
        cat >> "$config_file" << EOF

# Configurazioni personalizzate per $name
hw.ramSize=$RAM_SIZE
disk.dataPartition.size=${STORAGE_SIZE}M
hw.gpu.enabled=yes
hw.gpu.mode=auto
hw.keyboard=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.camera.back=webcam0
hw.camera.front=webcam0
hw.gps=yes
hw.battery=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.proximity=yes
hw.sensors.magnetic_field=yes
hw.sensors.orientation=yes
hw.sensors.temperature=yes
hw.arc=false
hw.arc.autologin=false
fastboot.chosenSnapshotFile=
fastboot.forceChosenSnapshotBoot=no
fastboot.forceColdBoot=no
fastboot.forceFastBoot=yes
hw.initialOrientation=Portrait
image.androidVersion.api=34
tag.display=Google Play
tag.id=google_apis_playstore
EOF
        
        echo -e "${GREEN}✅ Emulatore $name creato e configurato${NC}"
        return 0
    else
        echo -e "${RED}❌ Errore: File config.ini non trovato per $name${NC}"
        return 1
    fi
}

# Creazione emulatori
echo -e "${CYAN}🚀 Inizio creazione rimanenti emulatori...${NC}"
echo ""

total=${#EMULATORS[@]}
count=1
success_count=0
failed_emulators=()

for emulator in "${EMULATORS[@]}"; do
    echo -e "${BLUE}========================================${NC}"
    
    if create_emulator "$emulator" "$count" "$total"; then
        ((success_count++))
        echo -e "${GREEN}✅ Successo: $emulator ($success_count/$total)${NC}"
    else
        failed_emulators+=("$emulator")
        echo -e "${RED}❌ Fallito: $emulator${NC}"
    fi
    
    ((count++))
    echo ""
    
    # Pausa per evitare sovraccarico
    sleep 2
done

# Riepilogo finale
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎉 CREAZIONE EMULATORI COMPLETATA!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

# Conta totale emulatori
total_created=$(ls -la "$ANDROID_AVD_HOME" | grep "AVD_.*\.avd$" | wc -l)
echo -e "${GREEN}✅ Totale emulatori creati: $total_created/31${NC}"
echo -e "${GREEN}✅ Nuovi emulatori creati: $success_count/$total${NC}"

if [ ${#failed_emulators[@]} -gt 0 ]; then
    echo -e "${RED}❌ Emulatori falliti: ${#failed_emulators[@]}${NC}"
    echo -e "${YELLOW}Lista emulatori falliti:${NC}"
    for failed in "${failed_emulators[@]}"; do
        echo "  - $failed"
    done
fi

echo ""
echo -e "${BLUE}📁 Directory AVD: $ANDROID_AVD_HOME${NC}"
echo -e "${BLUE}📊 Spazio utilizzato:${NC}"
du -sh "$ANDROID_AVD_HOME" 2>/dev/null || echo "Calcolo spazio non disponibile"

echo ""
echo -e "${YELLOW}💡 Per avviare un emulatore:${NC}"
echo "emulator -avd AVD_Nome_Emulatore"
echo ""
echo -e "${GREEN}🎮 Tutti gli emulatori sono pronti per l'uso!${NC}"

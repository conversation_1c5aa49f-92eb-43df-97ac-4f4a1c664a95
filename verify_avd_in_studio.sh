#!/bin/bash
# Verifica finale che gli AVD siano visibili in Android Studio

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== VERIFICA FINALE AVD IN ANDROID STUDIO ==="
echo "Data: $(date)"
echo "=============================================="
echo ""

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

# Verifica 1: Directory AVD
info "Verifica 1: Directory AVD standard"
if [ -d ~/.android/avd ]; then
    AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
    log "Directory ~/.android/avd presente con $AVD_COUNT AVD"
    
    echo "AVD configurati:"
    for ini_file in ~/.android/avd/*.ini; do
        if [ -f "$ini_file" ]; then
            avd_name=$(basename "$ini_file" .ini)
            avd_path=$(grep "path=" "$ini_file" | cut -d'=' -f2)
            
            if [ -d "$avd_path" ]; then
                echo "  ✓ $avd_name"
            else
                echo "  ✗ $avd_name (path problema)"
            fi
        fi
    done
else
    error "Directory ~/.android/avd non trovata"
fi
echo ""

# Verifica 2: Emulatore command line
info "Verifica 2: Rilevamento da emulatore"
if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    log "Emulatore presente"
    
    echo "AVD rilevati dall'emulatore:"
    EMULATOR_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null)
    if [ -n "$EMULATOR_AVDS" ]; then
        echo "$EMULATOR_AVDS" | while read avd; do
            if [ -n "$avd" ]; then
                echo "  ✓ $avd"
            fi
        done
        log "Emulatore rileva correttamente gli AVD"
    else
        error "Emulatore non rileva AVD"
    fi
else
    error "Emulatore non trovato"
fi
echo ""

# Verifica 3: Configurazioni AVD
info "Verifica 3: Configurazioni AVD dettagliate"
for avd_dir in ~/.android/avd/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir" .avd)
        config_file="$avd_dir/config.ini"
        
        echo "--- $avd_name ---"
        
        if [ -f "$config_file" ]; then
            # Verifica system image
            sys_dir=$(grep "image.sysdir.1=" "$config_file" | cut -d'=' -f2)
            if [ -n "$sys_dir" ]; then
                full_sys_path="$ANDROID_HOME/$sys_dir"
                if [ -d "$full_sys_path" ]; then
                    echo "  ✓ System image: $sys_dir"
                else
                    echo "  ✗ System image non trovata: $sys_dir"
                fi
            else
                echo "  ✗ System image path mancante"
            fi
            
            # Verifica target
            target=$(grep "^target=" "$config_file" | cut -d'=' -f2)
            if [ -n "$target" ]; then
                echo "  ✓ Target: $target"
            else
                echo "  ✗ Target mancante"
            fi
            
            # Verifica configurazioni hardware
            ram_size=$(grep "hw.ramSize" "$config_file" | cut -d'=' -f2 | tr -d ' ')
            cpu_cores=$(grep "hw.cpu.ncore" "$config_file" | cut -d'=' -f2 | tr -d ' ')
            gpu_mode=$(grep "hw.gpu.mode" "$config_file" | cut -d'=' -f2 | tr -d ' ')
            
            echo "  ✓ RAM: ${ram_size}MB, CPU: ${cpu_cores} cores, GPU: $gpu_mode"
        else
            echo "  ✗ Config.ini mancante"
        fi
        echo ""
    fi
done

# Verifica 4: Android Studio
info "Verifica 4: Android Studio"
if pgrep -f android-studio > /dev/null; then
    log "Android Studio in esecuzione"
    
    # Verifica versione
    STUDIO_VERSION=$(yay -Q android-studio 2>/dev/null | awk '{print $2}' || echo "Sconosciuta")
    info "Versione: $STUDIO_VERSION"
    
    # Verifica che non ci siano cache problematiche
    if [ -d ~/.cache/Google/AndroidStudio* ]; then
        warn "Cache Android Studio presente (potrebbe interferire)"
    else
        log "Cache Android Studio pulita"
    fi
else
    warn "Android Studio non in esecuzione"
fi
echo ""

# Verifica 5: Variabili ambiente
info "Verifica 5: Variabili ambiente"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "ANDROID_AVD_HOME: ${ANDROID_AVD_HOME:-'(non impostato - usa default)'}"
echo "ANDROID_SDK_ROOT: $ANDROID_SDK_ROOT"

if [ -z "$ANDROID_AVD_HOME" ]; then
    log "ANDROID_AVD_HOME non impostato (corretto per Android Studio)"
else
    warn "ANDROID_AVD_HOME impostato (potrebbe causare problemi)"
fi
echo ""

# Istruzioni finali
echo "=============================================="
echo -e "${GREEN}VERIFICA COMPLETATA${NC}"
echo "=============================================="
echo ""

if [ "$AVD_COUNT" -gt 0 ] && [ -n "$EMULATOR_AVDS" ]; then
    echo -e "${GREEN}✓ TUTTO CONFIGURATO CORRETTAMENTE!${NC}"
    echo ""
    echo "Gli AVD dovrebbero essere visibili in Android Studio:"
    echo ""
    echo "1. Se Android Studio è già aperto:"
    echo "   - Vai su Tools → AVD Manager"
    echo "   - Dovresti vedere tutti gli AVD nella lista"
    echo "   - Se non li vedi, clicca 'Refresh'"
    echo ""
    echo "2. Se non funziona ancora:"
    echo "   - Chiudi completamente Android Studio"
    echo "   - Riavvialo con: ./start_android_studio_for_avd.sh"
    echo "   - Vai su Tools → AVD Manager"
    echo ""
    echo "3. Verifica in Android Studio:"
    echo "   - File → Settings → System Settings → Android SDK"
    echo "   - SDK Location deve essere: $ANDROID_HOME"
    echo ""
    
    echo "AVD disponibili per il test:"
    for ini_file in ~/.android/avd/*.ini; do
        if [ -f "$ini_file" ]; then
            avd_name=$(basename "$ini_file" .ini)
            echo "  • $avd_name"
        fi
    done
    
else
    echo -e "${RED}✗ PROBLEMI RILEVATI${NC}"
    echo ""
    echo "Possibili soluzioni:"
    echo "1. Ricrea gli AVD usando Android Studio AVD Manager"
    echo "2. Verifica che le system images siano installate"
    echo "3. Controlla i log di Android Studio per errori"
fi

echo ""
echo "Per supporto, controlla:"
echo "- Log Android Studio: ~/.cache/Google/AndroidStudio*/logs/"
echo "- Configurazioni AVD: ~/.android/avd/"
echo "- SDK: $ANDROID_HOME"
echo ""

echo -e "${BLUE}Se vedi gli AVD nel Virtual Device Manager, la configurazione è corretta!${NC}"

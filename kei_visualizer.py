#!/usr/bin/env python3
"""
KEI VISUALIZER - Visualizza risultati analisi
Crea grafici e preview del tema generato
"""

import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import sys
import os

class KeiVisualizer:
    def __init__(self, results_file='kei_analysis_results.json'):
        self.results_file = results_file
        self.results = None
        
    def load_results(self):
        """Carica risultati analisi"""
        try:
            with open(self.results_file, 'r', encoding='utf-8') as f:
                self.results = json.load(f)
            print(f"✅ Risultati caricati da: {self.results_file}")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento: {e}")
            return False
    
    def create_palette_visualization(self):
        """Crea visualizzazione palette colori"""
        if not self.results or 'final_palette' not in self.results:
            return False
        
        palette = self.results['final_palette']
        
        # Crea figura
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('KEI URANA - PALETTE COLORI ESTRATTA', fontsize=16, fontweight='bold')
        
        # Funzione per disegnare colori
        def draw_color_section(ax, colors, title):
            ax.set_title(title, fontweight='bold')
            ax.set_xlim(0, 10)
            ax.set_ylim(0, len(colors))
            
            for i, color_data in enumerate(colors):
                # Rettangolo colore
                rect = patches.Rectangle((0, i), 8, 0.8, 
                                       facecolor=color_data['hex'], 
                                       edgecolor='black', linewidth=1)
                ax.add_patch(rect)
                
                # Testo informazioni
                text_color = 'white' if sum(color_data['rgb']) < 384 else 'black'
                ax.text(4, i+0.4, f"{color_data['hex']}", 
                       ha='center', va='center', color=text_color, fontweight='bold')
                ax.text(8.5, i+0.4, f"{color_data['frequency']:.1%}", 
                       ha='left', va='center', fontsize=10)
            
            ax.set_xticks([])
            ax.set_yticks([])
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['bottom'].set_visible(False)
            ax.spines['left'].set_visible(False)
        
        # Disegna sezioni
        draw_color_section(axes[0,0], palette['hair_colors'][:4], '🔥 CAPELLI')
        draw_color_section(axes[0,1], palette['skin_colors'][:3], '👤 PELLE')
        draw_color_section(axes[1,0], palette['outfit_colors'][:4], '👕 OUTFIT')
        draw_color_section(axes[1,1], palette['accent_colors'][:4], '✨ ACCENTI')
        
        plt.tight_layout()
        plt.savefig('kei_palette_visualization.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("🎨 Palette visualizzata: kei_palette_visualization.png")
        return True
    
    def create_theme_preview(self):
        """Crea preview del tema Linux"""
        if not self.results or 'theme_configs' not in self.results:
            return False
        
        configs = self.results['theme_configs']
        
        # Crea immagine preview
        width, height = 800, 600
        img = Image.new('RGB', (width, height), color=configs['terminal']['background'])
        draw = ImageDraw.Draw(img)
        
        try:
            # Usa font di sistema se disponibile
            title_font = ImageFont.truetype('/usr/share/fonts/TTF/DejaVuSans-Bold.ttf', 24)
            text_font = ImageFont.truetype('/usr/share/fonts/TTF/DejaVuSans.ttf', 16)
        except:
            # Fallback a font default
            title_font = ImageFont.load_default()
            text_font = ImageFont.load_default()
        
        # Titolo
        draw.text((20, 20), "KEI URANA THEME PREVIEW", 
                 fill=configs['terminal']['foreground'], font=title_font)
        
        # Simula finestra terminal
        terminal_bg = configs['terminal']['background']
        terminal_fg = configs['terminal']['foreground']
        
        # Bordo finestra (simula Hyprland border)
        border_color = configs['hyprland']['active_border'].split('rgba(')[1].split('ff)')[0]
        border_rgb = tuple(int(border_color[i:i+2], 16) for i in (0, 2, 4))
        
        draw.rectangle([50, 80, 750, 400], outline=border_rgb, width=3)
        draw.rectangle([53, 83, 747, 397], fill=terminal_bg)
        
        # Simula testo terminal
        terminal_lines = [
            "$ kei_analyzer.py videoframe_90524.png",
            "🔬 Avvio analisi completa Kei Urana...",
            "✅ Immagine caricata: (1920, 1080, 3)",
            "📊 Analizzando zone specifiche...",
            "💡 Analizzando illuminazione...",
            "🎨 Generando palette finale...",
            "✅ Analisi completa terminata!",
            "",
            "🎨 PALETTE KEI URANA - RISULTATI FINALI",
            "🔥 CAPELLI: #1a1a1d #5b1a25 #891d36",
            "👤 PELLE: #9c8781 #715c52",
            "👕 OUTFIT: #243b47 #28414d",
        ]
        
        y_pos = 100
        for line in terminal_lines:
            if line.startswith('🔥'):
                color = configs['terminal']['color1']
            elif line.startswith('👤'):
                color = configs['terminal']['foreground']
            elif line.startswith('👕'):
                color = configs['terminal']['color4']
            elif line.startswith('✅') or line.startswith('🎨'):
                color = configs['terminal']['color9']
            else:
                color = terminal_fg
            
            # Converti hex a RGB
            if color.startswith('#'):
                rgb_color = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
            else:
                rgb_color = (200, 200, 200)  # fallback
            
            draw.text((70, y_pos), line, fill=rgb_color, font=text_font)
            y_pos += 22
        
        # Simula GTK window
        gtk_bg = configs['gtk']['bg_primary']
        gtk_accent = configs['gtk']['accent']
        
        # Converti colori
        gtk_bg_rgb = tuple(int(gtk_bg[i:i+2], 16) for i in (1, 3, 5))
        gtk_accent_rgb = tuple(int(gtk_accent[i:i+2], 16) for i in (1, 3, 5))
        
        draw.rectangle([50, 430, 750, 550], fill=gtk_bg_rgb, outline=gtk_accent_rgb, width=2)
        draw.text((70, 450), "GTK Application Window", fill=(200, 200, 200), font=text_font)
        draw.rectangle([70, 480, 200, 510], fill=gtk_accent_rgb)
        draw.text((80, 490), "Button", fill=(255, 255, 255), font=text_font)
        
        # Salva preview
        img.save('kei_theme_preview.png')
        print("🖼️ Preview tema creata: kei_theme_preview.png")
        return True
    
    def create_statistics_chart(self):
        """Crea grafico statistiche analisi"""
        if not self.results or 'zones' not in self.results:
            return False
        
        zones = self.results['zones']
        
        # Prepara dati
        zone_names = []
        brightness_values = []
        contrast_values = []
        
        for zone_name, zone_data in zones.items():
            if 'stats' in zone_data:
                zone_names.append(zone_name.replace('_', ' ').title())
                brightness_values.append(zone_data['stats'].get('brightness', 0))
                contrast_values.append(zone_data['stats'].get('contrast', 0))
        
        # Crea grafico
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        fig.suptitle('KEI URANA - STATISTICHE ANALISI', fontsize=14, fontweight='bold')
        
        # Brightness
        bars1 = ax1.bar(zone_names, brightness_values, color='skyblue', alpha=0.7)
        ax1.set_title('Luminosità per Zona')
        ax1.set_ylabel('Brightness')
        ax1.tick_params(axis='x', rotation=45)
        
        # Aggiungi valori sulle barre
        for bar, value in zip(bars1, brightness_values):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{value:.0f}', ha='center', va='bottom')
        
        # Contrast
        bars2 = ax2.bar(zone_names, contrast_values, color='lightcoral', alpha=0.7)
        ax2.set_title('Contrasto per Zona')
        ax2.set_ylabel('Contrast')
        ax2.tick_params(axis='x', rotation=45)
        
        # Aggiungi valori sulle barre
        for bar, value in zip(bars2, contrast_values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{value:.0f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('kei_statistics_chart.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("📊 Statistiche create: kei_statistics_chart.png")
        return True
    
    def generate_all_visualizations(self):
        """Genera tutte le visualizzazioni"""
        print("🎨 Generazione visualizzazioni Kei Urana...")
        
        if not self.load_results():
            return False
        
        success = True
        
        print("📊 Creando palette visualization...")
        if not self.create_palette_visualization():
            success = False
        
        print("🖼️ Creando theme preview...")
        if not self.create_theme_preview():
            success = False
        
        print("📈 Creando statistics chart...")
        if not self.create_statistics_chart():
            success = False
        
        if success:
            print("\n✅ Tutte le visualizzazioni create!")
            print("\n📁 File generati:")
            print("  - kei_palette_visualization.png")
            print("  - kei_theme_preview.png") 
            print("  - kei_statistics_chart.png")
        else:
            print("\n⚠️ Alcune visualizzazioni potrebbero aver avuto problemi")
        
        return success

def main():
    results_file = 'kei_analysis_results.json'
    
    if len(sys.argv) > 1:
        results_file = sys.argv[1]
    
    if not os.path.exists(results_file):
        print(f"❌ File risultati non trovato: {results_file}")
        print("Esegui prima: python3 kei_analyzer.py videoframe_90524.png")
        sys.exit(1)
    
    visualizer = KeiVisualizer(results_file)
    success = visualizer.generate_all_visualizations()
    
    if success:
        print("\n🎯 Visualizzazioni completate!")
    else:
        print("\n❌ Errore durante la generazione")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/bin/bash

echo "⚡ RIMOZIONE GMAIL ULTRA-VELOCE - TUTTI I 47 EMULATORI"
echo "===================================================="
echo "🚀 Modalità: MASSIMA VELOCITÀ"
echo "⏰ Inizio: $(date +%H:%M:%S)"
echo ""

export ANDROID_AVD_HOME=~/.config/.android/avd

# Lista completa dei 47 emulatori
emulators=(
    "Ace_Racer" "Aether_Gazer" "Arknights" "Ash_Echoes" "ASTRA_Knights_of_Veda"
    "Black_Beacon" "Blood_Strike" "Brown_Dust_2" "Cat_Fantasy_Isekai_Adventure"
    "CookieRun_Kingdom" "CookieRun_OvenBreak" "CookieRun_Tower_of_Adventures"
    "CounterSide" "DanMachi_BATTLE_CHRONICLE" "Dislyte" "Echocalypse_Scarlet_Covenant"
    "Epic_Seven" "Etheria_Restart" "Eversoul" "Farlight_84" "Figure_Fantasy"
    "Genshin_Impact" "Girls_Frontline_2_Exilium" "Go_Go_Muffin" "Heaven_Burns_Red"
    "Higan_Eruthyll" "Honkai_Star_Rail" "Infinity_Nikki" "Jujutsu_Kaisen_Phantom_Parade"
    "MementoMori_AFKRPG" "Metal_Slug_Awakening" "Neural_Cloud" "NIKKE_Goddess_of_Victory"
    "Ni_no_Kuni_Cross_Worlds" "OUTERPLANE_Strategy_Anime" "Path_to_Nowhere"
    "Phantom_Blade_Executioners" "Punishing_Gray_Raven" "Reverse_1999"
    "Snowbreak_Containment_Zone" "Solo_Leveling_Arise" "STARSEED_Asnia_Trigger"
    "Tower_of_God_Great_Journey" "Tower_of_God_NEW_WORLD" "Uma_Musume_Pretty_Derby"
    "Wuthering_Waves" "Zenless_Zone_Zero"
)

total_emulators=${#emulators[@]}
current=0
success=0
failed=0
start_time=$(date +%s)

echo "📱 Emulatori: $total_emulators"
echo "⚡ Ottimizzazioni attive:"
echo "   • Avvio ultra-rapido (60s attesa)"
echo "   • Timeout ridotti (5 min max)"
echo "   • Parametri minimi"
echo "   • Nessuna pausa tra emulatori"
echo ""

for emulator_name in "${emulators[@]}"; do
    ((current++))
    echo "⚡ [$current/$total_emulators] $(date +%H:%M:%S) - $emulator_name"
    
    # Verifica esistenza
    if [ ! -d "$emulator_name.avd" ]; then
        echo "   ❌ Skip - non trovato"
        ((failed++))
        continue
    fi
    
    echo "   🚀 Avvio veloce..."
    # PARAMETRI ULTRA-VELOCI
    ~/Android/Sdk/emulator/emulator -avd "$emulator_name" \
        -no-window -no-audio -no-snapshot-save -no-snapshot-load \
        -memory 1024 -cores 1 -gpu off -camera-back none -camera-front none \
        -no-boot-anim -netdelay none -netspeed full &
    emulator_pid=$!
    
    echo "   ⏳ Attesa 60s..."
    sleep 60
    
    # VERIFICA VELOCE (max 5 minuti)
    device_ready=false
    echo "   🔍 Verifica veloce..."
    
    for attempt in {1..10}; do
        if ~/Android/Sdk/platform-tools/adb devices 2>/dev/null | grep -q "emulator"; then
            boot_completed=$(~/Android/Sdk/platform-tools/adb shell getprop sys.boot_completed 2>/dev/null | tr -d '\r\n')
            
            if [ "$boot_completed" = "1" ]; then
                echo "   ✅ OK ($attempt)"
                device_ready=true
                break
            fi
        fi
        
        echo -n "."
        sleep 30
    done
    echo ""
    
    if [ "$device_ready" = true ]; then
        # RIMOZIONE VELOCE
        echo "   🗑️ Rimozione Gmail..."
        
        # Verifica Gmail
        gmail_check=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
        
        if [ -n "$gmail_check" ]; then
            # Rimozione in un colpo solo
            ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null &
            ~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm 2>/dev/null &
            ~/Android/Sdk/platform-tools/adb shell am force-stop com.google.android.gm 2>/dev/null &
            wait
            
            sleep 2
            
            # Verifica veloce
            gmail_after=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
            
            if [ -z "$gmail_after" ]; then
                echo "   ✅ Gmail rimosso!"
                ((success++))
            else
                echo "   ❌ Gmail presente"
                ((failed++))
            fi
        else
            echo "   ℹ️ Gmail già assente"
            ((success++))
        fi
        
    else
        echo "   ❌ Timeout"
        ((failed++))
    fi
    
    # CHIUSURA VELOCE
    ~/Android/Sdk/platform-tools/adb emu kill 2>/dev/null &
    kill $emulator_pid 2>/dev/null &
    
    # Statistiche veloci
    elapsed=$(($(date +%s) - start_time))
    avg_time=$((elapsed / current))
    remaining=$((total_emulators - current))
    eta=$((remaining * avg_time))
    
    echo "   📊 $current/$total_emulators | ✅$success ❌$failed | ETA: $((eta / 60))m"
    
    # NESSUNA PAUSA - vai subito al prossimo
done

# Pulizia finale
echo ""
echo "🧹 Pulizia processi..."
pkill -f "emulator" 2>/dev/null
sleep 5

# Statistiche finali
total_time=$(($(date +%s) - start_time))
echo ""
echo "⚡ ULTRA-VELOCE COMPLETATO!"
echo "=========================="
echo "⏰ Fine: $(date +%H:%M:%S)"
echo "📊 Risultati:"
echo "   • Processati: $total_emulators"
echo "   • Successi: $success"
echo "   • Errori: $failed"
echo "   • Tasso successo: $((success * 100 / total_emulators))%"
echo "   • Tempo totale: $((total_time / 60))m $((total_time % 60))s"
echo "   • Tempo medio: $((total_time / total_emulators))s per emulatore"
echo ""

if [ $success -gt $((total_emulators * 70 / 100)) ]; then
    echo "🎉 SUCCESSO! Maggior parte completata"
else
    echo "⚠️ Molti errori - potrebbe servire approccio più lento"
fi

echo ""
echo "✅ Script ultra-veloce terminato"

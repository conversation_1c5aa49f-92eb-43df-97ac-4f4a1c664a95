#!/bin/bash
# Creazione AVD ottimizzati per i9-12900KF + RTX 4080 + 3.6TB

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== CREAZIONE AVD OTTIMIZZATI ==="
echo "Sistema: i9-12900KF + RTX 4080 + 3.6TB"
echo "Data: $(date)"
echo "=================================="
echo ""

# Carica variabili ambiente
source ~/.bashrc

# Verifica prerequisiti
if [ -z "$ANDROID_HOME" ]; then
    error "ANDROID_HOME non impostato"
    exit 1
fi

if [ ! -f "$ANDROID_HOME/cmdline-tools/latest/bin/avdmanager" ]; then
    error "avdmanager non trovato"
    exit 1
fi

AVDMANAGER="$ANDROID_HOME/cmdline-tools/latest/bin/avdmanager"

log "Prerequisiti verificati"
log "ANDROID_HOME: $ANDROID_HOME"
log "ANDROID_AVD_HOME: $ANDROID_AVD_HOME"

# Lista system images disponibili
info "System images disponibili:"
ls -la "$ANDROID_HOME/system-images/"

echo ""

# AVD 1: Gaming Performance (Android 14, 8GB RAM)
AVD_NAME_1="Gaming_Android14_8GB"
SYSTEM_IMAGE_1="system-images;android-34;google_apis;x86_64"

info "Creazione AVD Gaming: $AVD_NAME_1"
if $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME_1"; then
    warn "AVD $AVD_NAME_1 già esistente, lo ricreo..."
    echo "yes" | $AVDMANAGER delete avd -n "$AVD_NAME_1"
fi

echo "no" | $AVDMANAGER create avd \
    -n "$AVD_NAME_1" \
    -k "$SYSTEM_IMAGE_1" \
    -d "pixel_7_pro" \
    --force

# Configura AVD per gaming
AVD_CONFIG="$ANDROID_AVD_HOME/$AVD_NAME_1.avd/config.ini"
if [ -f "$AVD_CONFIG" ]; then
    cp "$AVD_CONFIG" "$AVD_CONFIG.backup"
    
    cat >> "$AVD_CONFIG" << EOF

# === CONFIGURAZIONI GAMING OTTIMIZZATE ===
hw.ramSize=8192
hw.cpu.ncore=8
vm.heapSize=512
hw.gpu.enabled=yes
hw.gpu.mode=host
hw.keyboard=yes
hw.dPad=no
hw.mainKeys=no
hw.trackBall=no
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.proximity=yes
hw.sensors.orientation=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=32G
disk.cachePartition.size=2G
disk.systemPartition.size=4G
# Ottimizzazioni RTX 4080
hw.lcd.density=560
hw.lcd.height=3120
hw.lcd.width=1440
EOF
    log "AVD Gaming configurato: $AVD_NAME_1"
else
    error "File config.ini non trovato per $AVD_NAME_1"
fi

echo ""

# AVD 2: Development (Android 14, 4GB RAM)
AVD_NAME_2="Dev_Android14_4GB"
SYSTEM_IMAGE_2="system-images;android-34;google_apis;x86_64"

info "Creazione AVD Development: $AVD_NAME_2"
if $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME_2"; then
    warn "AVD $AVD_NAME_2 già esistente, lo ricreo..."
    echo "yes" | $AVDMANAGER delete avd -n "$AVD_NAME_2"
fi

echo "no" | $AVDMANAGER create avd \
    -n "$AVD_NAME_2" \
    -k "$SYSTEM_IMAGE_2" \
    -d "pixel_6" \
    --force

# Configura AVD per development
AVD_CONFIG="$ANDROID_AVD_HOME/$AVD_NAME_2.avd/config.ini"
if [ -f "$AVD_CONFIG" ]; then
    cp "$AVD_CONFIG" "$AVD_CONFIG.backup"
    
    cat >> "$AVD_CONFIG" << EOF

# === CONFIGURAZIONI DEVELOPMENT ===
hw.ramSize=4096
hw.cpu.ncore=4
vm.heapSize=256
hw.gpu.enabled=yes
hw.gpu.mode=auto
hw.keyboard=yes
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=16G
disk.cachePartition.size=1G
disk.systemPartition.size=4G
hw.lcd.density=420
EOF
    log "AVD Development configurato: $AVD_NAME_2"
else
    error "File config.ini non trovato per $AVD_NAME_2"
fi

echo ""

# AVD 3: Gaming Android 13 (6GB RAM)
AVD_NAME_3="Gaming_Android13_6GB"
SYSTEM_IMAGE_3="system-images;android-33;google_apis;x86_64"

info "Creazione AVD Gaming Android 13: $AVD_NAME_3"
if $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME_3"; then
    warn "AVD $AVD_NAME_3 già esistente, lo ricreo..."
    echo "yes" | $AVDMANAGER delete avd -n "$AVD_NAME_3"
fi

echo "no" | $AVDMANAGER create avd \
    -n "$AVD_NAME_3" \
    -k "$SYSTEM_IMAGE_3" \
    -d "pixel_7" \
    --force

# Configura AVD per gaming Android 13
AVD_CONFIG="$ANDROID_AVD_HOME/$AVD_NAME_3.avd/config.ini"
if [ -f "$AVD_CONFIG" ]; then
    cp "$AVD_CONFIG" "$AVD_CONFIG.backup"
    
    cat >> "$AVD_CONFIG" << EOF

# === CONFIGURAZIONI GAMING ANDROID 13 ===
hw.ramSize=6144
hw.cpu.ncore=6
vm.heapSize=384
hw.gpu.enabled=yes
hw.gpu.mode=host
hw.keyboard=yes
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=24G
disk.cachePartition.size=1G
disk.systemPartition.size=4G
hw.lcd.density=480
EOF
    log "AVD Gaming Android 13 configurato: $AVD_NAME_3"
else
    error "File config.ini non trovato per $AVD_NAME_3"
fi

echo ""

# AVD 4: Testing Android 15 (8GB RAM) - se disponibile
if [ -d "$ANDROID_HOME/system-images/android-35/google_apis/x86_64" ]; then
    AVD_NAME_4="Test_Android15_8GB"
    SYSTEM_IMAGE_4="system-images;android-35;google_apis;x86_64"

    info "Creazione AVD Testing Android 15: $AVD_NAME_4"
    if $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME_4"; then
        warn "AVD $AVD_NAME_4 già esistente, lo ricreo..."
        echo "yes" | $AVDMANAGER delete avd -n "$AVD_NAME_4"
    fi

    echo "no" | $AVDMANAGER create avd \
        -n "$AVD_NAME_4" \
        -k "$SYSTEM_IMAGE_4" \
        -d "pixel_8_pro" \
        --force

    # Configura AVD per testing
    AVD_CONFIG="$ANDROID_AVD_HOME/$AVD_NAME_4.avd/config.ini"
    if [ -f "$AVD_CONFIG" ]; then
        cp "$AVD_CONFIG" "$AVD_CONFIG.backup"
        
        cat >> "$AVD_CONFIG" << EOF

# === CONFIGURAZIONI TESTING ANDROID 15 ===
hw.ramSize=8192
hw.cpu.ncore=8
vm.heapSize=512
hw.gpu.enabled=yes
hw.gpu.mode=host
hw.keyboard=yes
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=64G
disk.cachePartition.size=4G
disk.systemPartition.size=4G
hw.lcd.density=560
EOF
        log "AVD Testing Android 15 configurato: $AVD_NAME_4"
    else
        error "File config.ini non trovato per $AVD_NAME_4"
    fi
else
    warn "System image Android 15 non disponibile, salto la creazione"
fi

echo ""

# Verifica AVD creati
info "Verifica AVD creati:"
$ANDROID_HOME/emulator/emulator -list-avds

echo ""

# Aggiorna script di avvio
info "Aggiornamento script di avvio..."

# Aggiorna script gaming
cat > /home/<USER>/Android/Scripts/startup/start_gaming_emulator.sh << 'EOF'
#!/bin/bash
# Script avvio emulatore gaming ottimizzato

source ~/.bashrc

# Variabili ambiente ottimizzate
export ANDROID_EMULATOR_USE_SYSTEM_LIBS=1
export QT_X11_NO_MITSHM=1
export LIBGL_ALWAYS_INDIRECT=0

AVD_NAME="Gaming_Android14_8GB"

echo "=== AVVIO EMULATORE GAMING ==="
echo "AVD: $AVD_NAME"
echo "Configurazione: 8GB RAM, 8 CPU cores, GPU RTX 4080"
echo "=============================="

if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME"; then
    echo "ERRORE: AVD $AVD_NAME non trovato!"
    echo "AVD disponibili:"
    $ANDROID_HOME/emulator/emulator -list-avds
    exit 1
fi

# Parametri ottimizzati per gaming
EMULATOR_OPTS=(
    -avd "$AVD_NAME"
    -gpu host
    -cores 8
    -memory 8192
    -partition-size 8192
    -cache-size 2048
    -no-snapshot-load
    -no-snapshot-save
    -no-audio
    -netdelay none
    -netspeed full
    -qemu -enable-kvm
    -qemu -cpu host
    -qemu -smp 8
)

echo "Avvio emulatore con parametri ottimizzati..."
$ANDROID_HOME/emulator/emulator "${EMULATOR_OPTS[@]}"
EOF

chmod +x /home/<USER>/Android/Scripts/startup/start_gaming_emulator.sh

# Aggiorna script development
cat > /home/<USER>/Android/Scripts/startup/start_dev_emulator.sh << 'EOF'
#!/bin/bash
# Script avvio emulatore development

source ~/.bashrc

AVD_NAME="Dev_Android14_4GB"

echo "=== AVVIO EMULATORE DEVELOPMENT ==="
echo "AVD: $AVD_NAME"
echo "Configurazione: 4GB RAM, 4 CPU cores, GPU auto"
echo "=================================="

if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME"; then
    echo "ERRORE: AVD $AVD_NAME non trovato!"
    echo "AVD disponibili:"
    $ANDROID_HOME/emulator/emulator -list-avds
    exit 1
fi

EMULATOR_OPTS=(
    -avd "$AVD_NAME"
    -gpu auto
    -cores 4
    -memory 4096
    -partition-size 4096
    -cache-size 1024
    -netdelay none
    -netspeed full
)

echo "Avvio emulatore development..."
$ANDROID_HOME/emulator/emulator "${EMULATOR_OPTS[@]}"
EOF

chmod +x /home/<USER>/Android/Scripts/startup/start_dev_emulator.sh

log "Script di avvio aggiornati"

# Statistiche finali
echo ""
echo "=================================="
echo -e "${GREEN}AVD CREATI CON SUCCESSO!${NC}"
echo "=================================="
echo ""
echo "AVD ottimizzati per il tuo sistema:"
echo "1. Gaming_Android14_8GB - Gaming ad alte prestazioni (8GB RAM, 8 cores)"
echo "2. Dev_Android14_4GB - Development bilanciato (4GB RAM, 4 cores)"
echo "3. Gaming_Android13_6GB - Gaming compatibilità (6GB RAM, 6 cores)"
if [ -d "$ANDROID_HOME/system-images/android-35/google_apis/x86_64" ]; then
    echo "4. Test_Android15_8GB - Testing ultime funzionalità (8GB RAM, 8 cores)"
fi
echo ""

# Calcola spazio utilizzato
SPACE_USED=$(du -sh "$ANDROID_HOME" 2>/dev/null | cut -f1)
AVD_SPACE=$(du -sh "$ANDROID_AVD_HOME" 2>/dev/null | cut -f1)
echo "Spazio utilizzato:"
echo "- SDK: $SPACE_USED"
echo "- AVD: $AVD_SPACE"
echo ""

echo "COMANDI RAPIDI:"
echo "- Lista AVD: \$ANDROID_HOME/emulator/emulator -list-avds"
echo "- Gaming: /home/<USER>/Android/Scripts/startup/start_gaming_emulator.sh"
echo "- Development: /home/<USER>/Android/Scripts/startup/start_dev_emulator.sh"
echo "- Backup AVD: ./backup_avd.sh"
echo ""

echo "Tutti gli AVD sono configurati senza errori e ottimizzati per:"
echo "✓ CPU: i9-12900KF (core allocati per tipo uso)"
echo "✓ GPU: RTX 4080 (accelerazione hardware abilitata)"
echo "✓ Storage: 3.6TB (configurazioni salvate su disco principale)"
echo "✓ RAM: Allocazioni ottimizzate (4GB-8GB per tipo uso)"
echo "✓ KVM: Accelerazione hardware abilitata"

#!/bin/bash
# Crea tutti e 4 gli <PERSON><PERSON> o<PERSON> per i9-12900KF + RTX 4080

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}CREAZIONE COMPLETA 4 AVD OTTIMIZZATI${NC}"
echo "Sistema: i9-12900KF + RTX 4080 + 32GB RAM + 3.6TB"
echo "Data: $(date)"
echo "======================================================="
echo ""

# Termina Android Studio
section "PREPARAZIONE"
pkill -f android-studio 2>/dev/null || true
sleep 3

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
unset _JAVA_OPTIONS
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

log "Ambiente configurato - ANDROID_HOME: $ANDROID_HOME"

# Elimina AVD esistenti
section "PULIZIA AVD ESISTENTI"
if [ -d ~/.android/avd ]; then
    # Backup prima di eliminare
    BACKUP_DIR="$HOME/Android/AVD/backups/complete_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    cp -r ~/.android/avd/* "$BACKUP_DIR/" 2>/dev/null || true
    log "Backup AVD esistenti salvato in: $BACKUP_DIR"
    
    # Elimina tutto
    rm -rf ~/.android/avd/*
    log "AVD esistenti eliminati"
else
    mkdir -p ~/.android/avd
    log "Directory AVD creata"
fi

# Pulisci cache Android Studio
rm -rf ~/.cache/Google/AndroidStudio*/caches/* 2>/dev/null || true
log "Cache Android Studio pulita"

section "CREAZIONE AVD OTTIMIZZATI"

# AVD 1: GAMING PERFORMANCE (Android 14, 8GB RAM)
info "Creazione AVD 1: Gaming Performance..."
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Gaming_Android14_8GB" \
    -k "system-images;android-34;google_apis;x86_64" \
    -d "pixel_7_pro" \
    --force

# Configura file .ini
cat > "$HOME/.android/avd/Gaming_Android14_8GB.ini" << EOF
avd.ini.encoding=UTF-8
path=$HOME/.android/avd/Gaming_Android14_8GB.avd
path.rel=avd/Gaming_Android14_8GB.avd
target=android-34
EOF

# Attendi che la directory .avd sia creata
sleep 2

# Configura config.ini se la directory esiste
if [ -d "$HOME/.android/avd/Gaming_Android14_8GB.avd" ]; then
    cat >> "$HOME/.android/avd/Gaming_Android14_8GB.avd/config.ini" << EOF

# === GAMING PERFORMANCE CONFIGURATION ===
AvdId=Gaming_Android14_8GB
avd.ini.displayname=Gaming Android 14 8GB
PlayStore.enabled=false
tag.id=google_apis
tag.display=Google APIs
hw.ramSize=8192
hw.cpu.ncore=8
vm.heapSize=512
hw.gpu.enabled=yes
hw.gpu.mode=host
hw.keyboard=yes
hw.dPad=no
hw.mainKeys=no
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.proximity=yes
hw.sensors.orientation=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=32G
disk.cachePartition.size=2G
disk.systemPartition.size=4G
hw.lcd.density=512
EOF
    log "Gaming_Android14_8GB configurato (8GB RAM, 8 cores, RTX 4080)"
else
    warn "Directory Gaming_Android14_8GB.avd non creata"
fi

# AVD 2: DEVELOPMENT BALANCED (Android 14, 4GB RAM)
info "Creazione AVD 2: Development Balanced..."
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Dev_Android14_4GB" \
    -k "system-images;android-34;google_apis;x86_64" \
    -d "pixel_6" \
    --force

cat > "$HOME/.android/avd/Dev_Android14_4GB.ini" << EOF
avd.ini.encoding=UTF-8
path=$HOME/.android/avd/Dev_Android14_4GB.avd
path.rel=avd/Dev_Android14_4GB.avd
target=android-34
EOF

sleep 2

if [ -d "$HOME/.android/avd/Dev_Android14_4GB.avd" ]; then
    cat >> "$HOME/.android/avd/Dev_Android14_4GB.avd/config.ini" << EOF

# === DEVELOPMENT CONFIGURATION ===
AvdId=Dev_Android14_4GB
avd.ini.displayname=Dev Android 14 4GB
PlayStore.enabled=false
tag.id=google_apis
tag.display=Google APIs
hw.ramSize=4096
hw.cpu.ncore=4
vm.heapSize=256
hw.gpu.enabled=yes
hw.gpu.mode=auto
hw.keyboard=yes
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=16G
disk.cachePartition.size=1G
disk.systemPartition.size=4G
hw.lcd.density=411
EOF
    log "Dev_Android14_4GB configurato (4GB RAM, 4 cores, GPU auto)"
else
    warn "Directory Dev_Android14_4GB.avd non creata"
fi

# AVD 3: GAMING COMPATIBILITY (Android 13, 6GB RAM)
info "Creazione AVD 3: Gaming Compatibility..."
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Gaming_Android13_6GB" \
    -k "system-images;android-33;google_apis;x86_64" \
    -d "pixel_7" \
    --force

cat > "$HOME/.android/avd/Gaming_Android13_6GB.ini" << EOF
avd.ini.encoding=UTF-8
path=$HOME/.android/avd/Gaming_Android13_6GB.avd
path.rel=avd/Gaming_Android13_6GB.avd
target=android-33
EOF

sleep 2

if [ -d "$HOME/.android/avd/Gaming_Android13_6GB.avd" ]; then
    cat >> "$HOME/.android/avd/Gaming_Android13_6GB.avd/config.ini" << EOF

# === GAMING COMPATIBILITY CONFIGURATION ===
AvdId=Gaming_Android13_6GB
avd.ini.displayname=Gaming Android 13 6GB
PlayStore.enabled=false
tag.id=google_apis
tag.display=Google APIs
hw.ramSize=6144
hw.cpu.ncore=6
vm.heapSize=384
hw.gpu.enabled=yes
hw.gpu.mode=host
hw.keyboard=yes
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=24G
disk.cachePartition.size=1G
disk.systemPartition.size=4G
hw.lcd.density=480
EOF
    log "Gaming_Android13_6GB configurato (6GB RAM, 6 cores, RTX 4080)"
else
    warn "Directory Gaming_Android13_6GB.avd non creata"
fi

# AVD 4: TESTING LATEST (Android 15, 8GB RAM)
info "Creazione AVD 4: Testing Latest..."
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Test_Android15_8GB" \
    -k "system-images;android-35;google_apis;x86_64" \
    -d "pixel_7_pro" \
    --force

cat > "$HOME/.android/avd/Test_Android15_8GB.ini" << EOF
avd.ini.encoding=UTF-8
path=$HOME/.android/avd/Test_Android15_8GB.avd
path.rel=avd/Test_Android15_8GB.avd
target=android-35
EOF

sleep 2

if [ -d "$HOME/.android/avd/Test_Android15_8GB.avd" ]; then
    cat >> "$HOME/.android/avd/Test_Android15_8GB.avd/config.ini" << EOF

# === TESTING LATEST CONFIGURATION ===
AvdId=Test_Android15_8GB
avd.ini.displayname=Test Android 15 8GB
PlayStore.enabled=false
tag.id=google_apis
tag.display=Google APIs
hw.ramSize=8192
hw.cpu.ncore=8
vm.heapSize=512
hw.gpu.enabled=yes
hw.gpu.mode=host
hw.keyboard=yes
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.proximity=yes
hw.sensors.orientation=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=64G
disk.cachePartition.size=4G
disk.systemPartition.size=4G
hw.lcd.density=512
EOF
    log "Test_Android15_8GB configurato (8GB RAM, 8 cores, RTX 4080)"
else
    warn "Directory Test_Android15_8GB.avd non creata"
fi

section "VERIFICA FINALE"

# Test riconoscimento AVD
info "Test riconoscimento AVD..."
EMULATOR_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null)
if [ -n "$EMULATOR_AVDS" ]; then
    log "AVD rilevati dall'emulatore:"
    echo "$EMULATOR_AVDS" | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
    
    AVD_COUNT=$(echo "$EMULATOR_AVDS" | wc -l)
    log "Totale AVD creati: $AVD_COUNT"
else
    error "Nessun AVD rilevato"
fi

# Calcola spazio utilizzato
TOTAL_SIZE=$(du -sh ~/.android/avd 2>/dev/null | cut -f1)
info "Spazio utilizzato AVD: $TOTAL_SIZE"

echo ""
echo "======================================================="
echo -e "${GREEN}CREAZIONE COMPLETA TERMINATA!${NC}"
echo "======================================================="
echo ""
echo "AVD CREATI E OTTIMIZZATI:"
echo ""
echo "1. ${CYAN}Gaming_Android14_8GB${NC}"
echo "   - Android 14 (API 34) + Google APIs"
echo "   - 8GB RAM, 8 CPU cores, GPU RTX 4080 (host mode)"
echo "   - 32GB storage, Pixel 7 Pro, 512 DPI"
echo "   - Ottimizzato per gaming ad alte prestazioni"
echo ""
echo "2. ${CYAN}Dev_Android14_4GB${NC}"
echo "   - Android 14 (API 34) + Google APIs"
echo "   - 4GB RAM, 4 CPU cores, GPU auto"
echo "   - 16GB storage, Pixel 6, 411 DPI"
echo "   - Bilanciato per development"
echo ""
echo "3. ${CYAN}Gaming_Android13_6GB${NC}"
echo "   - Android 13 (API 33) + Google APIs"
echo "   - 6GB RAM, 6 CPU cores, GPU RTX 4080 (host mode)"
echo "   - 24GB storage, Pixel 7, 480 DPI"
echo "   - Gaming con compatibilità estesa"
echo ""
echo "4. ${CYAN}Test_Android15_8GB${NC}"
echo "   - Android 15 (API 35) + Google APIs"
echo "   - 8GB RAM, 8 CPU cores, GPU RTX 4080 (host mode)"
echo "   - 64GB storage, Pixel 7 Pro, 512 DPI"
echo "   - Testing ultime funzionalità Android"
echo ""
echo "CONFIGURAZIONI HARDWARE:"
echo "✓ CPU: i9-12900KF (core dedicati per tipo uso)"
echo "✓ GPU: RTX 4080 (accelerazione hardware abilitata)"
echo "✓ RAM: Allocazioni ottimizzate (4GB-8GB)"
echo "✓ Storage: 3.6TB (configurazioni salvate)"
echo "✓ KVM: Accelerazione hardware attiva"
echo ""
echo "SPAZIO UTILIZZATO: $TOTAL_SIZE"
echo ""
echo -e "${BLUE}Avvio Android Studio per verificare gli AVD...${NC}"

# Avvia Android Studio
android-studio &

sleep 3
echo ""
echo "======================================================="
echo -e "${GREEN}ANDROID STUDIO AVVIATO!${NC}"
echo "======================================================="
echo ""
echo "ISTRUZIONI FINALI:"
echo ""
echo "1. ${YELLOW}Vai su More Actions → Virtual Device Manager${NC}"
echo ""
echo "2. ${YELLOW}Dovresti vedere tutti e 4 gli AVD:${NC}"
echo "   ✓ Gaming_Android14_8GB"
echo "   ✓ Dev_Android14_4GB"
echo "   ✓ Gaming_Android13_6GB"
echo "   ✓ Test_Android15_8GB"
echo ""
echo "3. ${YELLOW}Se vedi 'Medium Phone API 36.0', eliminalo${NC}"
echo ""
echo "4. ${YELLOW}Per testare un AVD:${NC}"
echo "   - Clicca il pulsante Play (▶️) accanto all'AVD"
echo "   - L'emulatore si avvierà in 30-60 secondi"
echo "   - Verifica performance e fluidità"
echo ""
echo "5. ${YELLOW}Se non vedi gli AVD:${NC}"
echo "   - File → Invalidate Caches and Restart"
echo "   - Seleziona 'Invalidate and Restart'"
echo "   - Riapri Virtual Device Manager"
echo ""
echo -e "${GREEN}SISTEMA COMPLETO E PRONTO PER L'USO!${NC} 🎉"
echo ""
echo "Tutti gli emulatori sono ottimizzati per il tuo hardware"
echo "e configurati senza errori per gaming e development."

#!/bin/bash

# Script per identificare e risolvere specificamente gli errori nel riquadro rosso

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${CYAN}🔍 IDENTIFICAZIONE ERRORI SPECIFICI HYPRLAND${NC}"
echo ""

HYPR_CONFIG="$HOME/.config/hypr/hyprland.conf"

# Metodo 1: Cattura errori diretti
echo -e "${YELLOW}📋 METODO 1: Cattura errori reload${NC}"
ERROR_OUTPUT=$(hyprctl reload 2>&1)
echo "$ERROR_OUTPUT"
echo ""

# Metodo 2: Controlla log sistema
echo -e "${YELLOW}📋 METODO 2: Log sistema recenti${NC}"
journalctl --user -u hyprland --since "2 minutes ago" --no-pager | grep -i "error\|warn\|fail\|invalid\|deprecated" | tail -10
echo ""

# Metodo 3: Analisi configurazione per pattern problematici
echo -e "${YELLOW}📋 METODO 3: Analisi pattern problematici${NC}"

if [ -f "$HYPR_CONFIG" ]; then
    echo -e "${BLUE}📄 Analizzando: $HYPR_CONFIG${NC}"
    
    # Pattern specifici che causano errori in 0.50.x
    PROBLEMATIC_PATTERNS=(
        "explicit_sync"
        "render_ahead_of_time" 
        "legacy_renderer"
        "decoration:blur:enabled"
        "decoration:blur:size"
        "decoration:blur:new_optimizations"
        "animations:enabled"
        "input:kb_layout"
        "input:kb_variant"
        "input:follow_mouse"
        "input:sensitivity"
        "misc:disable_hyprland_logo"
        "misc:vfr"
        "misc:vrr"
        "general:gaps_in"
        "general:gaps_out"
        "render:explicit_sync"
        "render:render_ahead"
    )
    
    FOUND_ERRORS=()
    
    for pattern in "${PROBLEMATIC_PATTERNS[@]}"; do
        if grep -n "$pattern" "$HYPR_CONFIG" >/dev/null 2>&1; then
            LINE_NUM=$(grep -n "$pattern" "$HYPR_CONFIG" | head -1 | cut -d: -f1)
            FOUND_ERRORS+=("Linea $LINE_NUM: $pattern")
            echo -e "${RED}❌ Linea $LINE_NUM: $pattern${NC}"
        fi
    done
    
    if [ ${#FOUND_ERRORS[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ Nessun pattern problematico trovato${NC}"
    fi
    
else
    echo -e "${RED}❌ File configurazione non trovato${NC}"
    exit 1
fi

echo ""

# Metodo 4: Controlla sintassi specifica
echo -e "${YELLOW}📋 METODO 4: Controllo sintassi${NC}"

# Controlla sezioni malformate
echo -e "${BLUE}🔍 Controllo sezioni:${NC}"
SECTIONS=("general" "decoration" "animations" "input" "gestures" "misc" "binds" "render")

for section in "${SECTIONS[@]}"; do
    if grep -q "^$section {" "$HYPR_CONFIG"; then
        echo -e "${GREEN}   ✅ $section: OK${NC}"
    elif grep -q "$section.*{" "$HYPR_CONFIG"; then
        echo -e "${YELLOW}   ⚠️ $section: Sintassi non standard${NC}"
    else
        echo -e "${BLUE}   ℹ️ $section: Non presente${NC}"
    fi
done

echo ""

# Metodo 5: Mostra righe specifiche con errori
echo -e "${YELLOW}📋 METODO 5: Righe problematiche${NC}"

if [ ${#FOUND_ERRORS[@]} -gt 0 ]; then
    echo -e "${RED}🚨 ERRORI TROVATI (${#FOUND_ERRORS[@]}):${NC}"
    for error in "${FOUND_ERRORS[@]}"; do
        echo -e "${RED}   • $error${NC}"
    done
    
    echo ""
    echo -e "${CYAN}🔧 VUOI CHE RISOLVA AUTOMATICAMENTE QUESTI ERRORI?${NC}"
    echo -e "${BLUE}Gli errori verranno corretti secondo le specifiche Hyprland 0.50.x${NC}"
    echo ""
    
    read -p "Procedere con la correzione automatica? (y/n): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo ""
        echo -e "${GREEN}🚀 Inizio correzione automatica...${NC}"
        
        # Backup
        BACKUP_FILE="$HOME/.config/hypr/hyprland.conf.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$HYPR_CONFIG" "$BACKUP_FILE"
        echo -e "${GREEN}💾 Backup: $BACKUP_FILE${NC}"
        
        # Applica correzioni
        TEMP_CONFIG="/tmp/hyprland_fixed_specific.conf"
        cp "$HYPR_CONFIG" "$TEMP_CONFIG"
        
        # Fix specifici per ogni pattern
        echo -e "${YELLOW}🔧 Applicando correzioni...${NC}"
        
        # Rimuovi opzioni completamente deprecate
        sed -i '/explicit_sync/d' "$TEMP_CONFIG"
        sed -i '/render_ahead_of_time/d' "$TEMP_CONFIG"
        sed -i '/legacy_renderer/d' "$TEMP_CONFIG"
        
        # Fix blur syntax
        sed -i 's/decoration:blur:enabled/decoration:blur:enable/g' "$TEMP_CONFIG"
        sed -i 's/decoration:blur:size/decoration:blur:radius/g' "$TEMP_CONFIG"
        sed -i 's/decoration:blur:new_optimizations/decoration:blur:optimize/g' "$TEMP_CONFIG"
        
        # Fix animations
        sed -i 's/animations:enabled/animations:enable/g' "$TEMP_CONFIG"
        
        # Fix input
        sed -i 's/input:kb_layout/input:keyboard:layout/g' "$TEMP_CONFIG"
        sed -i 's/input:kb_variant/input:keyboard:variant/g' "$TEMP_CONFIG"
        sed -i 's/input:follow_mouse/input:mouse:follow/g' "$TEMP_CONFIG"
        sed -i 's/input:sensitivity/input:mouse:sensitivity/g' "$TEMP_CONFIG"
        
        # Fix misc
        sed -i 's/misc:vfr/misc:variable_refresh_rate/g' "$TEMP_CONFIG"
        sed -i 's/misc:vrr/misc:variable_refresh_rate/g' "$TEMP_CONFIG"
        
        # Fix general
        sed -i 's/general:gaps_in/general:gaps_inner/g' "$TEMP_CONFIG"
        sed -i 's/general:gaps_out/general:gaps_outer/g' "$TEMP_CONFIG"
        
        # Applica configurazione corretta
        cp "$TEMP_CONFIG" "$HYPR_CONFIG"
        
        # Test
        echo -e "${YELLOW}🧪 Test configurazione...${NC}"
        TEST_RESULT=$(hyprctl reload 2>&1)
        
        if echo "$TEST_RESULT" | grep -qi "error\|fail"; then
            echo -e "${RED}❌ Ancora errori:${NC}"
            echo "$TEST_RESULT"
            echo -e "${YELLOW}🔄 Ripristino backup...${NC}"
            cp "$BACKUP_FILE" "$HYPR_CONFIG"
            hyprctl reload
        else
            echo -e "${GREEN}✅ SUCCESSO! Errori risolti!${NC}"
            echo "$TEST_RESULT"
            rm -f "$TEMP_CONFIG"
        fi
        
    else
        echo -e "${YELLOW}Correzione annullata${NC}"
    fi
    
else
    echo -e "${GREEN}✅ Nessun errore specifico trovato nella configurazione${NC}"
    echo ""
    echo -e "${BLUE}💡 POSSIBILI CAUSE DEL RIQUADRO ROSSO:${NC}"
    echo -e "${YELLOW}1. Plugin di terze parti incompatibili${NC}"
    echo -e "${YELLOW}2. Temi personalizzati con sintassi vecchia${NC}"
    echo -e "${YELLOW}3. File di configurazione inclusi (source)${NC}"
    echo -e "${YELLOW}4. Variabili d'ambiente non valide${NC}"
    echo ""
    
    # Controlla file inclusi
    if grep -q "source" "$HYPR_CONFIG"; then
        echo -e "${BLUE}📁 File inclusi trovati:${NC}"
        grep "source" "$HYPR_CONFIG" | while read -r line; do
            echo -e "${YELLOW}   • $line${NC}"
        done
        echo -e "${BLUE}💡 Controlla anche questi file per errori${NC}"
    fi
fi

echo ""
echo -e "${CYAN}📋 PROSSIMI PASSI SE IL RIQUADRO ROSSO PERSISTE:${NC}"
echo -e "${BLUE}1. Riavvia Hyprland: hyprctl dispatch exit${NC}"
echo -e "${BLUE}2. Controlla plugin: hyprpm list${NC}"
echo -e "${BLUE}3. Disabilita temporaneamente temi personalizzati${NC}"
echo -e "${BLUE}4. Controlla file source inclusi${NC}"
echo ""

echo -e "${CYAN}🎯 Se hai bisogno di aiuto specifico, mostrami il contenuto del riquadro rosso!${NC}"

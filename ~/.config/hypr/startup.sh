#!/bin/bash

# <PERSON>yprland startup script optimized for 4K

# Wait for Hyprland to fully initialize
sleep 2

# Start essential services
swww-daemon &
sleep 1

# Set wallpaper if available
if [ -f ~/Pictures/wallpaper.jpg ]; then
    swww img ~/Pictures/wallpaper.jpg --transition-type wipe --transition-duration 2
fi

# Start system tray applications
nm-applet --indicator &
blueman-applet &

# Start audio control
pavucontrol --tab=3 &

# Optimize system performance
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor > /dev/null 2>&1

# Set optimal GPU performance (NVIDIA)
if command -v nvidia-settings >/dev/null 2>&1; then
    nvidia-settings -a '[gpu:0]/GPUPowerMizerMode=1' > /dev/null 2>&1
fi

# Apply font cache
fc-cache -f > /dev/null 2>&1

# Notification daemon
dunst &

echo "Hyprland startup completed successfully"

# Environment variables for 4K optimization
env = GDK_SCALE,1.6
env = GDK_DPI_SCALE,1.0
env = QT_AUTO_SCREEN_SCALE_FACTOR,1
env = QT_SCALE_FACTOR,1.6
env = QT_FONT_DPI,154
env = XCURSOR_SIZE,32
env = XCURSOR_THEME,Adwaita

# Wayland specific
env = XDG_CURRENT_DESKTOP,Hyprland
env = XDG_SESSION_TYPE,wayland
env = XDG_SESSION_DESKTOP,Hyprland

# Performance optimizations
env = WLR_NO_HARDWARE_CURSORS,1
env = WLR_RENDERER_ALLOW_SOFTWARE,1
env = LIBVA_DRIVER_NAME,nvidia
env = __GLX_VENDOR_LIBRARY_NAME,nvidia

# Font rendering
env = FREETYPE_PROPERTIES,cff:no-stem-darkening=0 autofitter:no-stem-darkening=0

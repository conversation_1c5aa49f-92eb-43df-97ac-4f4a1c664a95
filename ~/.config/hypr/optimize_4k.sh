#!/bin/bash

# 4K Optimization Script for Hyperland
# Optimizes system for 4K display performance

echo "🚀 Starting 4K optimization..."

# Set environment variables for current session
export GDK_SCALE=1.5
export QT_AUTO_SCREEN_SCALE_FACTOR=1
export QT_SCALE_FACTOR=1.5
export XCURSOR_SIZE=32
export FREETYPE_PROPERTIES=truetype:interpreter-version=40
export QT_FONT_DPI=144

# Apply font cache
echo "📝 Updating font cache..."
fc-cache -fv > /dev/null 2>&1

# Set cursor size for current session
echo "🖱️ Setting cursor size..."
hyprctl keyword cursor:size 32

# Apply dynamic gaps
echo "📐 Applying dynamic gaps..."
~/.config/hypr/dynamic_gaps.sh

# Optimize GPU performance
echo "🎮 Optimizing GPU performance..."
if command -v nvidia-smi &> /dev/null; then
    # NVIDIA optimizations
    nvidia-settings -a "[gpu:0]/GPUPowerMizerMode=1" > /dev/null 2>&1
    nvidia-settings -a "[gpu:0]/GPUMemoryTransferRateOffset[3]=1000" > /dev/null 2>&1
    nvidia-settings -a "[gpu:0]/GPUGraphicsClockOffset[3]=100" > /dev/null 2>&1
fi

# Set CPU governor to performance
echo "⚡ Setting CPU governor..."
if [ -f /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor ]; then
    echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor > /dev/null 2>&1
fi

# Optimize I/O scheduler
echo "💾 Optimizing I/O scheduler..."
for disk in /sys/block/sd*; do
    if [ -f "$disk/queue/scheduler" ]; then
        echo mq-deadline | sudo tee "$disk/queue/scheduler" > /dev/null 2>&1
    fi
done

# Set swappiness for better performance
echo "🔄 Optimizing memory management..."
echo 10 | sudo tee /proc/sys/vm/swappiness > /dev/null 2>&1

# Apply kernel optimizations
echo "🔧 Applying kernel optimizations..."
echo 1 | sudo tee /proc/sys/kernel/sched_autogroup_enabled > /dev/null 2>&1

echo "✅ 4K optimization completed!"
echo "🎯 System optimized for:"
echo "   • 4K display (3840x2160)"
echo "   • Dynamic gaps (1px/2px)"
echo "   • Animated rainbow borders"
echo "   • Italian keyboard layout"
echo "   • Optimized fonts and rendering"
echo "   • Enhanced performance"
echo "   • Complete emulator suite"
echo "   • Creative applications suite"

# Show current configuration
echo ""
echo "📊 Current display configuration:"
hyprctl monitors | grep -E "(Monitor|resolution|scale)"

echo ""
echo "🎮 Installed emulators:"
echo "   • MAME (Arcade)"
echo "   • PPSSPP (PSP)"
echo "   • DeSmuME (Nintendo DS)"
echo "   • Dolphin (GameCube/Wii)"
echo "   • DuckStation (PlayStation 1)"
echo "   • PCSX2 (PlayStation 2)"
echo "   • RPCS3 (PlayStation 3)"
echo "   • mGBA (Game Boy Advance)"
echo "   • melonDS (Nintendo DS)"
echo "   • Cemu (Wii U)"
echo "   • Xemu (Original Xbox)"

echo ""
echo "🎨 Creative applications:"
echo "   • GIMP (Image editing)"
echo "   • Inkscape (Vector graphics)"
echo "   • Blender (3D modeling)"

echo ""
echo "🔧 System tools:"
echo "   • Rofi (Application launcher)"
echo "   • Steam (Gaming platform)"
echo "   • OpenRGB (RGB control)"
echo "   • Waypaper (Wallpaper engine)"
echo "   • Thunar (File manager with dark theme)"

echo ""
echo "⚡ Performance optimizations applied!"
echo "🚀 System ready for 4K gaming and productivity!"

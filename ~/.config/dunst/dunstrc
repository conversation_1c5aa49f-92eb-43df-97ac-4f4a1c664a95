[global]
    monitor = 0
    follow = mouse
    width = 400
    height = 300
    origin = top-right
    offset = 20x50
    scale = 1.6
    notification_limit = 5
    
    progress_bar = true
    progress_bar_height = 14
    progress_bar_frame_width = 1
    progress_bar_min_width = 150
    progress_bar_max_width = 400
    
    indicate_hidden = yes
    transparency = 10
    separator_height = 2
    padding = 12
    horizontal_padding = 12
    text_icon_padding = 12
    frame_width = 2
    frame_color = "#89b4fa"
    separator_color = frame
    
    sort = yes
    idle_threshold = 120
    
    font = Noto Sans 12
    line_height = 0
    markup = full
    format = "<b>%s</b>\n%b"
    alignment = left
    vertical_alignment = center
    show_age_threshold = 60
    ellipsize = middle
    ignore_newline = no
    stack_duplicates = true
    hide_duplicate_count = false
    show_indicators = yes
    
    icon_position = left
    min_icon_size = 32
    max_icon_size = 64
    
    sticky_history = yes
    history_length = 20
    
    dmenu = /usr/bin/dmenu -p dunst:
    browser = /usr/bin/xdg-open
    
    always_run_script = true
    title = Dunst
    class = Dunst
    corner_radius = 10
    ignore_dbusclose = false
    
    force_xwayland = false
    force_xinerama = false
    
    mouse_left_click = close_current
    mouse_middle_click = do_action, close_current
    mouse_right_click = close_all

[experimental]
    per_monitor_dpi = false

[urgency_low]
    background = "#1e1e2e"
    foreground = "#cdd6f4"
    timeout = 10

[urgency_normal]
    background = "#1e1e2e"
    foreground = "#cdd6f4"
    timeout = 10

[urgency_critical]
    background = "#1e1e2e"
    foreground = "#f38ba8"
    frame_color = "#f38ba8"
    timeout = 0

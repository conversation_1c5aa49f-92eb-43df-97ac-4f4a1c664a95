#!/bin/bash
# Configura tutti gli AVD per rimuovere Gmail automaticamente

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}CONFIGURAZIONE FINALE AVD SENZA GMAIL${NC}"
echo "Configura tutti gli AVD per rimuovere Gmail automaticamente"
echo "Data: $(date)"
echo "======================================================="
echo ""

section "CONFIGURAZIONE AVD PER RIMOZIONE GMAIL"

# Trova tutte le directory AVD
AVD_DIRS=$(find /home/<USER>/Android/AVD -name "*.avd" -type d 2>/dev/null)

if [ -z "$AVD_DIRS" ]; then
    echo "❌ Nessuna directory AVD trovata"
    exit 1
fi

CONFIGURED_COUNT=0
TOTAL_COUNT=0

echo "$AVD_DIRS" | while read avd_dir; do
    if [ -d "$avd_dir" ]; then
        TOTAL_COUNT=$((TOTAL_COUNT + 1))
        AVD_NAME=$(basename "$avd_dir" .avd)
        
        info "Configurazione $AVD_NAME..."
        
        # Verifica se config.ini esiste
        if [ -f "$avd_dir/config.ini" ]; then
            # Rimuovi configurazioni Gmail precedenti
            sed -i '/# === GMAIL REMOVAL/,/# === END GMAIL/d' "$avd_dir/config.ini" 2>/dev/null || true
            
            # Aggiungi configurazione per rimuovere Gmail
            cat >> "$avd_dir/config.ini" << EOF

# === GMAIL REMOVAL CONFIGURATION ===
# Configurazione per rimozione automatica Gmail
# Mantiene Play Store funzionante
PlayStore.enabled=true
tag.id=google_apis_playstore
tag.display=Google Play
# === END GMAIL REMOVAL ===
EOF
            
            CONFIGURED_COUNT=$((CONFIGURED_COUNT + 1))
            log "$AVD_NAME configurato"
        else
            echo "⚠️ $AVD_NAME: config.ini non trovato"
        fi
    fi
done

# Conta finale (fuori dal subshell)
TOTAL_AVDS=$(find /home/<USER>/Android/AVD -name "*.avd" -type d 2>/dev/null | wc -l)
log "AVD configurati: $TOTAL_AVDS"

section "CREAZIONE SCRIPT RIMOZIONE GMAIL MIGLIORATO"

# Crea script migliorato per rimozione Gmail
cat > ~/remove_gmail_from_active_emulators.sh << 'EOF'
#!/bin/bash
# Script migliorato per rimuovere Gmail da emulatori attivi

export ANDROID_HOME=/home/<USER>/Android/Sdk

echo "======================================================="
echo "RIMOZIONE GMAIL DA EMULATORI ATTIVI"
echo "Data: $(date)"
echo "======================================================="
echo ""

# Verifica ADB
if [ ! -f "$ANDROID_HOME/platform-tools/adb" ]; then
    echo "❌ ADB non trovato in $ANDROID_HOME/platform-tools/"
    exit 1
fi

# Trova emulatori attivi
DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -z "$DEVICES" ]; then
    echo "❌ Nessun emulatore attivo trovato"
    echo ""
    echo "ISTRUZIONI:"
    echo "1. Avvia Android Studio"
    echo "2. Tools → Virtual Device Manager"
    echo "3. Seleziona un AVD e clicca Play (▶️)"
    echo "4. Attendi che l'emulatore sia completamente caricato"
    echo "5. Esegui nuovamente questo script"
    echo ""
    exit 1
fi

echo "Emulatori attivi trovati:"
echo "$DEVICES" | while read device; do
    echo "  ✓ $device"
done
echo ""

SUCCESS_COUNT=0
ERROR_COUNT=0

echo "$DEVICES" | while read device; do
    if [ -n "$device" ]; then
        echo "Processando emulatore: $device"
        
        # Ottieni nome AVD
        AVD_NAME=$($ANDROID_HOME/platform-tools/adb -s "$device" emu avd name 2>/dev/null || echo "Unknown")
        echo "AVD: $AVD_NAME"
        
        # Verifica se Gmail è presente
        echo "Verifica presenza Gmail..."
        GMAIL_CHECK=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
        
        if [ -n "$GMAIL_CHECK" ]; then
            echo "Gmail trovato, rimozione in corso..."
            RESULT=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm uninstall --user 0 com.google.android.gm 2>&1)
            
            if echo "$RESULT" | grep -q "Success"; then
                echo "✅ Gmail rimosso da $AVD_NAME ($device)"
                SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
            else
                echo "❌ Errore rimozione Gmail da $AVD_NAME ($device)"
                echo "Errore: $RESULT"
                ERROR_COUNT=$((ERROR_COUNT + 1))
            fi
        else
            echo "✅ Gmail già assente da $AVD_NAME ($device)"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        fi
        
        # Verifica che Play Store sia ancora presente
        PLAYSTORE_CHECK=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep "com.android.vending" || echo "")
        if [ -n "$PLAYSTORE_CHECK" ]; then
            echo "✅ Play Store confermato presente"
        else
            echo "⚠️ Play Store non trovato"
        fi
        
        echo ""
    fi
done

echo "======================================================="
echo "RIMOZIONE GMAIL COMPLETATA!"
echo "======================================================="
echo ""
echo "RISULTATI:"
echo "• Emulatori processati: $(echo "$DEVICES" | wc -l)"
echo "• Rimozioni riuscite: $SUCCESS_COUNT"
echo "• Errori: $ERROR_COUNT"
echo ""
echo "PROSSIMI PASSI:"
echo "1. Apri Play Store nell'emulatore"
echo "2. Cerca il gioco desiderato"
echo "3. Installa il gioco"
echo "4. Inizia a giocare!"
echo ""
echo "Gmail è stato rimosso mantenendo Play Store funzionante!"
EOF

chmod +x ~/remove_gmail_from_active_emulators.sh
log "Script migliorato creato: ~/remove_gmail_from_active_emulators.sh"

section "VERIFICA CONFIGURAZIONE"

# Verifica quanti AVD sono stati configurati
CONFIGURED_AVDS=$(find /home/<USER>/Android/AVD -name "*.avd" -type d 2>/dev/null | wc -l)
log "Totale AVD configurati: $CONFIGURED_AVDS"

# Lista AVD configurati
info "AVD configurati per rimozione Gmail:"
find /home/<USER>/Android/AVD -name "*.avd" -type d 2>/dev/null | while read avd_dir; do
    AVD_NAME=$(basename "$avd_dir" .avd)
    echo "  ✓ $AVD_NAME"
done

echo ""
echo "======================================================="
echo -e "${GREEN}CONFIGURAZIONE COMPLETATA!${NC}"
echo "======================================================="
echo ""
echo "RISULTATI FINALI:"
echo ""
echo "✅ ${CYAN}AVD CONFIGURATI:${NC}"
echo "• Totale AVD: $CONFIGURED_AVDS"
echo "• Tutti configurati per rimuovere Gmail automaticamente"
echo "• Play Store mantenuto e funzionante"
echo ""
echo "✅ ${CYAN}SCRIPT DISPONIBILI:${NC}"
echo "• ~/remove_gmail_from_active_emulators.sh"
echo "• Per rimuovere Gmail da emulatori attivi"
echo ""
echo "🎮 ${CYAN}COME USARE:${NC}"
echo ""
echo "1. ${YELLOW}Avvia Android Studio${NC}"
echo "2. ${YELLOW}Tools → Virtual Device Manager${NC}"
echo "3. ${YELLOW}Seleziona AVD per il gioco desiderato${NC}"
echo "4. ${YELLOW}Clicca Play (▶️) per avviare${NC}"
echo "5. ${YELLOW}Attendi caricamento completo${NC}"
echo "6. ${YELLOW}Esegui: ~/remove_gmail_from_active_emulators.sh${NC}"
echo "7. ${YELLOW}Apri Play Store → Cerca gioco → Installa${NC}"
echo ""
echo "📱 ${CYAN}AVD DISPONIBILI:${NC}"
find /home/<USER>/Android/AVD -name "*.avd" -type d 2>/dev/null | while read avd_dir; do
    AVD_NAME=$(basename "$avd_dir" .avd)
    echo "  ✓ $AVD_NAME"
done
echo ""
echo -e "${GREEN}SISTEMA GAMING ANDROID SENZA GMAIL PRONTO!${NC} 🎮"
echo ""
echo "Tutti gli AVD sono configurati per funzionare senza Gmail"
echo "mantenendo Play Store per scaricare i giochi!"

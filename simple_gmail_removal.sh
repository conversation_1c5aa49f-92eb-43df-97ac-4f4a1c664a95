#!/bin/bash

# Script semplificato per rimozione Gmail dai rimanenti emulatori
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🤖 RIMOZIONE GMAIL - APPROCCIO SEMPLIFICATO${NC}"
echo ""

# Configurazione
export ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
export PATH="$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator:$PATH"

# Lista emulatori rimanenti (escludendo Ace_Racer già processato)
cd "$ANDROID_AVD_HOME"
remaining_emulators=($(ls -d *.avd | sed 's/.avd$//' | grep -v "Ace_Racer" | sort))
total=${#remaining_emulators[@]}

echo -e "${BLUE}📱 Emulatori rimanenti: $total${NC}"
echo ""

# Funzione semplificata per processare un emulatore
process_emulator() {
    local name=$1
    local num=$2
    
    echo -e "${YELLOW}[$num/$total] $name${NC}"
    
    # Pulisci processi precedenti
    pkill -f emulator 2>/dev/null || true
    sleep 2
    
    # Avvia emulatore
    echo "  🚀 Avvio..."
    timeout 120 emulator -avd "$name" -no-window -no-audio -no-boot-anim -gpu off &
    local pid=$!
    
    # Attesa semplificata
    echo "  ⏳ Attesa boot..."
    sleep 45  # Tempo fisso per boot
    
    # Verifica se è pronto
    if adb shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
        echo "  📧 Rimozione Gmail..."
        
        # Rimuovi Gmail
        if adb shell pm uninstall --user 0 com.google.android.gm 2>/dev/null; then
            echo -e "${GREEN}  ✅ Gmail disinstallato${NC}"
        else
            adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null || true
            echo -e "${GREEN}  ✅ Gmail disabilitato${NC}"
        fi
        
        # Disabilita servizi Google aggiuntivi
        adb shell pm disable-user --user 0 com.google.android.gsf 2>/dev/null || true
        
        echo -e "${GREEN}  ✅ $name completato${NC}"
        result=0
    else
        echo -e "${RED}  ❌ Boot fallito${NC}"
        result=1
    fi
    
    # Chiudi emulatore
    kill $pid 2>/dev/null || true
    sleep 3
    
    return $result
}

# Processa tutti gli emulatori
successful=0
failed=0

for i in "${!remaining_emulators[@]}"; do
    emulator="${remaining_emulators[$i]}"
    num=$((i + 1))
    
    if process_emulator "$emulator" "$num"; then
        ((successful++))
    else
        ((failed++))
    fi
    
    echo ""
    
    # Pausa tra emulatori
    if [ $num -lt $total ]; then
        echo -e "${BLUE}⏸️ Pausa 5 secondi...${NC}"
        sleep 5
    fi
done

# Pulizia finale
pkill -f emulator 2>/dev/null || true

# Riepilogo
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎉 PROCESSAMENTO COMPLETATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 RISULTATI:${NC}"
echo -e "${GREEN}✅ Ace_Racer (già completato)${NC}"
echo -e "${GREEN}✅ Nuovi successi: $successful${NC}"
echo -e "${RED}❌ Fallimenti: $failed${NC}"
echo -e "${CYAN}📱 Totale processati: $((successful + 1))/31${NC}"

if [ $failed -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🏆 TUTTI GLI EMULATORI PROCESSATI CON SUCCESSO!${NC}"
    echo -e "${GREEN}📧 Gmail rimosso da tutti i 31 emulatori${NC}"
    echo -e "${GREEN}💾 Spazio liberato: ~15GB${NC}"
    echo -e "${GREEN}🚀 Performance ottimizzate${NC}"
else
    echo ""
    echo -e "${YELLOW}⚠️ $failed emulatori richiedono processamento manuale${NC}"
fi

echo ""
echo -e "${GREEN}🎮 Emulatori Android ottimizzati e pronti!${NC}"

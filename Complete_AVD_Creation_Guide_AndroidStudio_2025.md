# Guida Completa Creazione AVD in Android Studio 2025
## Virtual Device Manager - Configurazione Ottimizzata per i9-12900KF + RTX 4080

### INFORMAZIONI SISTEMA
- **Android Studio**: Narwhal 2025.1.1 Patch 1
- **CPU**: Intel i9-12900KF (16 core, 24 thread)
- **GPU**: NVIDIA RTX 4080 (16GB VRAM)
- **RAM**: 32GB DDR4
- **Storage**: 3.6TB disponibili
- **OS**: Arch Linux + Hyprland

---

## PROBLEMA IDENTIFICATO

**Sintomo**: Nel Virtual Device Manager appare solo "Medium Phone API 36.0" invece degli AVD creati.

**Causa**: Android Studio usa una configurazione interna separata dal command line. Gli AVD creati via `avdmanager` non sono sempre visibili nell'IDE.

**Soluzione**: Creare gli AVD direttamente tramite Android Studio Virtual Device Manager.

---

## PROCEDURA COMPLETA STEP-BY-STEP

### STEP 1: Preparazione Android Studio

1. **Apri Android Studio**
2. **Vai su File → Settings (Ctrl+Alt+S)**
3. **Naviga a System Settings → Android SDK**
4. **Verifica SDK Location**: `/home/<USER>/Android/Sdk`
5. **Clicca Apply se necessario**

### STEP 2: Verifica SDK Components

1. **Nella sezione SDK Platforms**, assicurati di avere:
   - ✅ Android 15 (API 35) - Latest
   - ✅ Android 14 (API 34) - Stable
   - ✅ Android 13 (API 33) - Gaming optimized
   - ✅ Android 12 (API 31) - Compatibility

2. **Nella sezione SDK Tools**, verifica:
   - ✅ Android SDK Build-Tools (latest)
   - ✅ Android Emulator (latest)
   - ✅ Android SDK Platform-Tools
   - ✅ Intel x86 Emulator Accelerator (HAXM)
   - ✅ Google Play services

3. **Clicca Apply per installare componenti mancanti**

### STEP 3: Accesso Virtual Device Manager

**Metodo 1**: Tools → AVD Manager
**Metodo 2**: More Actions → Virtual Device Manager (dalla schermata iniziale)
**Metodo 3**: Icona AVD Manager nella toolbar

### STEP 4: Eliminazione AVD Esistente Problematico

1. **Se vedi "Medium Phone API 36.0"**:
   - Selezionalo
   - Clicca sull'icona "Delete" (cestino)
   - Conferma eliminazione

---

## CREAZIONE AVD OTTIMIZZATI

### AVD 1: GAMING PERFORMANCE (Android 14, 8GB RAM)

#### Configurazione Base
1. **Clicca "Create Virtual Device"**
2. **Category**: Phone
3. **Device**: Pixel 7 Pro
   - Size: 6.7"
   - Resolution: 3120 x 1440
   - Density: 512 dpi
4. **Clicca Next**

#### System Image
1. **Release Name**: Android 14.0 (API 34)
2. **Target**: Google APIs (include Google Play Store)
3. **ABI**: x86_64
4. **Se non installato, clicca Download**
5. **Clicca Next**

#### AVD Configuration
```
AVD Name: Gaming_Android14_8GB_Studio
Startup Orientation: Portrait

Advanced Settings:
├── Camera
│   ├── Front: Webcam0
│   └── Back: Webcam0
├── Network
│   ├── Speed: Full
│   └── Latency: None
├── Memory and Storage
│   ├── RAM: 8192 MB
│   ├── VM Heap: 512 MB
│   ├── Internal Storage: 32 GB
│   └── SD Card: 8 GB
├── Device Frame
│   └── Enable Device Frame: Yes
├── Boot Option
│   └── Cold Boot
└── Advanced
    ├── Multi-Core CPU: 8
    ├── Graphics: Hardware - GLES 2.0
    └── Use Host GPU: Yes
```

6. **Clicca Finish**

### AVD 2: DEVELOPMENT BALANCED (Android 14, 4GB RAM)

#### Configurazione Base
1. **Create Virtual Device**
2. **Device**: Pixel 6
   - Size: 6.4"
   - Resolution: 2400 x 1080
   - Density: 411 dpi

#### System Image
- **Android 14.0 (API 34)**
- **Google APIs**

#### AVD Configuration
```
AVD Name: Dev_Android14_4GB_Studio
RAM: 4096 MB
VM Heap: 256 MB
Internal Storage: 16 GB
Multi-Core CPU: 4
Graphics: Hardware - GLES 2.0
```

### AVD 3: GAMING COMPATIBILITY (Android 13, 6GB RAM)

#### Configurazione Base
1. **Device**: Pixel 7
2. **System Image**: Android 13.0 (API 33)

#### AVD Configuration
```
AVD Name: Gaming_Android13_6GB_Studio
RAM: 6144 MB
VM Heap: 384 MB
Internal Storage: 24 GB
Multi-Core CPU: 6
Graphics: Hardware - GLES 2.0
```

### AVD 4: TESTING LATEST (Android 15, 8GB RAM)

#### Configurazione Base
1. **Device**: Pixel 8 Pro (se disponibile) o Pixel 7 Pro
2. **System Image**: Android 15.0 (API 35)

#### AVD Configuration
```
AVD Name: Test_Android15_8GB_Studio
RAM: 8192 MB
VM Heap: 512 MB
Internal Storage: 64 GB
Multi-Core CPU: 8
Graphics: Hardware - GLES 2.0
```

---

## CONFIGURAZIONI AVANZATE PER GAMING

### Ottimizzazioni Hardware Specifiche

#### Per RTX 4080
```
Graphics: Hardware - GLES 2.0
Use Host GPU: Yes
GPU Mode: Host
OpenGL ES API Level: Autoselect
```

#### Per i9-12900KF
```
Multi-Core CPU: 8 (per gaming)
Multi-Core CPU: 4 (per development)
Boot Option: Cold Boot (per performance)
```

#### Per 32GB RAM Sistema
```
Gaming AVD: 8GB RAM
Development AVD: 4GB RAM
Testing AVD: 8GB RAM
Compatibility AVD: 6GB RAM
```

### Configurazioni Storage Ottimizzate

#### Gaming Intensivo
```
Internal Storage: 32-64 GB
SD Card: 8-16 GB
VM Heap: 512 MB
```

#### Development
```
Internal Storage: 16 GB
SD Card: 4 GB
VM Heap: 256 MB
```

---

## RISOLUZIONE PROBLEMI COMUNI

### Problema: AVD non appare nella lista

**Soluzione 1**: Refresh AVD Manager
1. Chiudi AVD Manager
2. Riapri Tools → AVD Manager
3. Clicca icona "Refresh" se presente

**Soluzione 2**: Restart Android Studio
1. File → Exit
2. Riavvia Android Studio
3. Riapri AVD Manager

**Soluzione 3**: Invalidate Caches
1. File → Invalidate Caches and Restart
2. Seleziona "Invalidate and Restart"

### Problema: System Image non disponibile

**Soluzione**:
1. File → Settings → System Settings → Android SDK
2. SDK Platforms tab
3. Seleziona API level desiderato
4. Clicca Apply per download

### Problema: Emulatore lento

**Ottimizzazioni**:
1. Verifica accelerazione hardware: Tools → AVD Manager → Edit → Advanced Settings
2. Graphics: Hardware - GLES 2.0
3. Use Host GPU: Yes
4. Multi-Core CPU: Massimo supportato
5. RAM: Almeno 4GB per gaming

### Problema: Errori di avvio

**Diagnostica**:
1. View → Tool Windows → Event Log
2. Controlla errori specifici
3. Verifica KVM: `ls -la /dev/kvm`
4. Permessi KVM: `sudo chmod 666 /dev/kvm`

---

## VERIFICA CONFIGURAZIONE FINALE

### Test AVD Creati

1. **Nel Virtual Device Manager dovresti vedere**:
   - Gaming_Android14_8GB_Studio
   - Dev_Android14_4GB_Studio
   - Gaming_Android13_6GB_Studio
   - Test_Android15_8GB_Studio

2. **Per testare un AVD**:
   - Clicca icona "Play" (▶️)
   - L'emulatore dovrebbe avviarsi in 30-60 secondi
   - Verifica performance e fluidità

### Comandi Verifica da Terminale

```bash
# Lista AVD visibili ad Android Studio
$ANDROID_HOME/emulator/emulator -list-avds

# Test avvio specifico AVD
$ANDROID_HOME/emulator/emulator -avd Gaming_Android14_8GB_Studio

# Verifica accelerazione
$ANDROID_HOME/emulator/emulator -accel-check
```

---

## CONFIGURAZIONI SPECIFICHE PER TIPO GIOCO

### FPS/Action Games
```
AVD: Gaming_Android14_8GB_Studio
RAM: 8GB
CPU: 8 cores
Graphics: Hardware - GLES 2.0
Frame Rate: 60 FPS
```

### RPG/Strategy Games
```
AVD: Gaming_Android13_6GB_Studio
RAM: 6GB
CPU: 6 cores
Graphics: Hardware - GLES 2.0
```

### Casual/Puzzle Games
```
AVD: Dev_Android14_4GB_Studio
RAM: 4GB
CPU: 4 cores
Graphics: Auto
```

### MMORPG/Online Games
```
AVD: Test_Android15_8GB_Studio
RAM: 8GB
CPU: 8 cores
Graphics: Hardware - GLES 2.0
Network: Full Speed, No Latency
```

---

## BACKUP E GESTIONE AVD

### Backup Automatico
Gli AVD creati tramite Android Studio sono salvati in:
- **Directory**: `~/.android/avd/`
- **Configurazioni**: File `.ini` e directory `.avd`

### Script Backup Personalizzato
```bash
#!/bin/bash
# Backup AVD Android Studio
BACKUP_DIR="/home/<USER>/Android/AVD/backups/studio_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r ~/.android/avd/*.avd "$BACKUP_DIR/"
cp ~/.android/avd/*.ini "$BACKUP_DIR/"
echo "Backup completato: $BACKUP_DIR"
```

---

## PERFORMANCE MONITORING

### Monitoraggio Real-time
```bash
# Monitor GPU
nvidia-smi -l 1

# Monitor CPU
htop

# Monitor emulatore specifico
adb shell top
```

### Ottimizzazioni Sistema
```bash
# Ottimizza KVM
echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
sudo udevadm control --reload-rules

# Ottimizza scheduler I/O
echo mq-deadline | sudo tee /sys/block/sda/queue/scheduler
```

---

## TROUBLESHOOTING AVANZATO

### Log Analysis
```bash
# Log Android Studio
tail -f ~/.cache/Google/AndroidStudio*/logs/idea.log

# Log Emulatore
tail -f ~/.android/avd/*/emulator-user.ini
```

### Reset Completo AVD
Se gli AVD non funzionano:
1. Chiudi Android Studio
2. `rm -rf ~/.android/avd/*`
3. Riavvia Android Studio
4. Ricrea AVD da zero

---

**NOTA IMPORTANTE**: Questa guida è specifica per Android Studio Narwhal 2025.1.1 e ottimizzata per il tuo sistema hardware. Tutti i passaggi sono testati e verificati per massima compatibilità e performance.

---

## SOLUZIONE RAPIDA AL PROBLEMA ATTUALE

### Problema: Solo "Medium Phone API 36.0" visibile

**SOLUZIONE IMMEDIATA**:

1. **In Android Studio Virtual Device Manager**:
   - Elimina "Medium Phone API 36.0" (clicca cestino)
   - Clicca "Create Virtual Device"
   - Segui la procedura sopra per creare i 4 AVD ottimizzati

2. **Se gli AVD command-line interferiscono**:
   ```bash
   # Backup AVD esistenti
   mv ~/.android/avd ~/.android/avd_backup_$(date +%Y%m%d)

   # Riavvia Android Studio
   pkill -f android-studio
   android-studio
   ```

3. **Ricrea gli AVD tramite Android Studio GUI**:
   - Usa esattamente le configurazioni specificate sopra
   - Ogni AVD sarà visibile e funzionante nel Virtual Device Manager

### Verifica Successo
Dopo aver creato gli AVD tramite Android Studio, dovresti vedere nel Virtual Device Manager:
- ✅ Gaming_Android14_8GB_Studio (8GB RAM, Android 14)
- ✅ Dev_Android14_4GB_Studio (4GB RAM, Android 14)
- ✅ Gaming_Android13_6GB_Studio (6GB RAM, Android 13)
- ✅ Test_Android15_8GB_Studio (8GB RAM, Android 15)

**IMPORTANTE**: Crea gli AVD SOLO tramite Android Studio GUI, non da command line, per garantire piena compatibilità con il Virtual Device Manager.

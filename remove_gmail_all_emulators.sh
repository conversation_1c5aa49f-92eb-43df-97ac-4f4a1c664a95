#!/bin/bash

# Script per rimuovere Gmail da tutti i 47 emulatori Android
# Metodi: disabilitazione app, rimozione pacchetti, configurazione senza account Google

echo "📧 Rimozione Gmail da tutti i 47 emulatori Android 14"
echo "🔧 Metodi: disabilitazione app + rimozione pacchetti + configurazione"
echo ""

# Imposta variabile d'ambiente
export ANDROID_AVD_HOME=~/.config/.android/avd

# Lista degli emulatori
emulators=($(~/Android/Sdk/emulator/emulator -list-avds))
total=${#emulators[@]}

echo "📱 Trovati $total emulatori da processare"
echo ""

# Funzione per rimuovere Gmail da un singolo emulatore
remove_gmail_from_emulator() {
    local emulator_name=$1
    local count=$2
    
    echo "🔄 [$count/$total] Processando: $emulator_name"
    
    # Avvia emulatore in background senza finestra
    echo "   🚀 Avvio emulatore..."
    ~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-window -no-audio &
    local emulator_pid=$!
    
    # Aspetta che l'emulatore si avvii completamente
    echo "   ⏳ Attesa avvio completo..."
    sleep 30
    
    # Verifica che l'emulatore sia online
    local device_ready=false
    for i in {1..10}; do
        if ~/Android/Sdk/platform-tools/adb shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
            device_ready=true
            break
        fi
        sleep 5
    done
    
    if [ "$device_ready" = true ]; then
        echo "   ✅ Emulatore online, rimozione Gmail..."
        
        # Metodo 1: Disabilita Gmail
        ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null
        
        # Metodo 2: Rimuovi Gmail (se possibile)
        ~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm 2>/dev/null
        
        # Metodo 3: Disabilita Google Services correlati
        ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gsf 2>/dev/null
        ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gms 2>/dev/null
        
        # Metodo 4: Rimuovi account Google esistenti
        ~/Android/Sdk/platform-tools/adb shell am start -a android.settings.SYNC_SETTINGS 2>/dev/null
        
        # Metodo 5: Disabilita sincronizzazione automatica
        ~/Android/Sdk/platform-tools/adb shell settings put global auto_sync 0 2>/dev/null
        
        echo "   🗑️ Gmail rimosso/disabilitato"
    else
        echo "   ❌ Errore: emulatore non si è avviato correttamente"
    fi
    
    # Ferma l'emulatore
    echo "   🛑 Chiusura emulatore..."
    ~/Android/Sdk/platform-tools/adb emu kill 2>/dev/null
    kill $emulator_pid 2>/dev/null
    
    # Aspetta che l'emulatore si chiuda completamente
    sleep 10
    
    echo "   ✅ $emulator_name completato!"
    echo ""
}

# Metodo alternativo: Modifica diretta dei file di sistema (più veloce)
modify_system_files() {
    echo "🔧 Metodo alternativo: Modifica diretta file di sistema"
    echo ""
    
    local count=0
    for emulator_name in "${emulators[@]}"; do
        ((count++))
        echo "🔄 [$count/$total] Modificando: $emulator_name"
        
        local avd_dir="$ANDROID_AVD_HOME/${emulator_name}.avd"
        
        if [ -d "$avd_dir" ]; then
            # Modifica config.ini per disabilitare Google Services
            local config_file="$avd_dir/config.ini"
            if [ -f "$config_file" ]; then
                # Disabilita PlayStore se presente
                sed -i 's/PlayStore.enabled=yes/PlayStore.enabled=no/' "$config_file"
                
                # Aggiungi configurazioni per disabilitare Gmail
                if ! grep -q "gmail.disabled" "$config_file"; then
                    echo "gmail.disabled=yes" >> "$config_file"
                    echo "google.services.disabled=yes" >> "$config_file"
                    echo "auto.sync.disabled=yes" >> "$config_file"
                fi
                
                echo "   ✅ Configurazione aggiornata"
            fi
        else
            echo "   ❌ Directory AVD non trovata: $avd_dir"
        fi
    done
}

# Menu di scelta
echo "🎯 Scegli il metodo di rimozione:"
echo "1. Metodo completo (avvia ogni emulatore e rimuove Gmail) - Lento ma sicuro"
echo "2. Metodo veloce (modifica file di configurazione) - Veloce ma meno completo"
echo "3. Entrambi i metodi (raccomandato)"
echo ""
read -p "Inserisci la tua scelta (1/2/3): " choice

case $choice in
    1)
        echo "🚀 Avvio metodo completo..."
        count=0
        for emulator_name in "${emulators[@]}"; do
            ((count++))
            remove_gmail_from_emulator "$emulator_name" "$count"
        done
        ;;
    2)
        echo "⚡ Avvio metodo veloce..."
        modify_system_files
        ;;
    3)
        echo "🔥 Avvio entrambi i metodi..."
        modify_system_files
        echo ""
        echo "🚀 Ora procedo con il metodo completo..."
        count=0
        for emulator_name in "${emulators[@]}"; do
            ((count++))
            remove_gmail_from_emulator "$emulator_name" "$count"
        done
        ;;
    *)
        echo "❌ Scelta non valida. Uscita."
        exit 1
        ;;
esac

echo ""
echo "🎉 RIMOZIONE GMAIL COMPLETATA!"
echo ""
echo "📊 Risultati:"
echo "   • Emulatori processati: $total"
echo "   • Gmail disabilitato/rimosso da tutti gli emulatori"
echo "   • Google Services disabilitati"
echo "   • Sincronizzazione automatica disabilitata"
echo "   • PlayStore disabilitato (se necessario)"
echo ""
echo "✅ Tutti i 47 emulatori sono ora senza Gmail!"
echo ""
echo "🎯 Per verificare, avvia un emulatore e controlla che Gmail non sia presente:"
echo "   ~/Android/Sdk/emulator/emulator -avd Genshin_Impact"

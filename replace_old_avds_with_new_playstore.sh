#!/bin/bash
# Sostituisce tutti gli AVD vecchi con quelli nuovi con Play Store

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}SOSTITUZIONE COMPLETA AVD CON PLAY STORE${NC}"
echo "Rimuove vecchi AVD e crea 31 AVD definitivi con Play Store"
echo "Data: $(date)"
echo "======================================================="
echo ""

# Termina processi
pkill -f emulator 2>/dev/null || true
sleep 3

export ANDROID_HOME=/home/<USER>/Android/Sdk

section "BACKUP E RIMOZIONE AVD ESISTENTI"

# Backup completo
BACKUP_DIR="$HOME/Android/AVD/backups/complete_replacement_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
if [ -d ~/.android/avd ]; then
    cp -r ~/.android/avd/* "$BACKUP_DIR/" 2>/dev/null || true
    log "Backup completo salvato in: $BACKUP_DIR"
    
    # Rimuovi tutti gli AVD esistenti
    rm -rf ~/.android/avd/*
    log "Tutti gli AVD esistenti rimossi"
else
    mkdir -p ~/.android/avd
fi

section "CREAZIONE 31 AVD DEFINITIVI CON PLAY STORE"

# Funzione per creare AVD definitivi
create_final_avd() {
    local name="$1"
    local device="$2"
    local ram="$3"
    local cores="$4"
    local storage="$5"
    local gpu="$6"
    local api="$7"
    
    info "Creazione definitiva: $name"
    
    echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
        -n "$name" \
        -k "system-images;$api;google_apis_playstore;x86_64" \
        -d "$device" \
        --force
    
    # Configura performance ottimali
    if [ -d ~/.android/avd/$name.avd ]; then
        cat >> ~/.android/avd/$name.avd/config.ini << EOF

# === PLAY STORE GAMING OPTIMIZED FINAL ===
PlayStore.enabled=true
tag.id=google_apis_playstore
tag.display=Google Play
hw.ramSize=$ram
hw.cpu.ncore=$cores
vm.heapSize=$((ram/16))
hw.gpu.enabled=yes
hw.gpu.mode=$gpu
hw.keyboard=yes
hw.dPad=no
hw.mainKeys=no
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.proximity=yes
hw.sensors.orientation=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=${storage}G
disk.cachePartition.size=2G
disk.systemPartition.size=4G
hw.lcd.density=480
hw.lcd.width=2400
hw.lcd.height=1080
fastboot.forceColdBoot=yes
hw.device.manufacturer=Google
hw.device.name=$device
EOF
        log "$name creato (Play Store, $ram MB RAM, $cores cores, ${storage}GB)"
    fi
}

# GACHA/RPG GAMES (20 AVD) - High Performance
info "Creazione 20 AVD Gacha/RPG definitivi..."

create_final_avd "Aether_Gazer" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_final_avd "Ash_Echoes" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_final_avd "Brown_Dust_2" "pixel_7" "6144" "6" "24" "host" "android-34"
create_final_avd "Danchro" "pixel_7" "6144" "6" "24" "host" "android-34"
create_final_avd "Genshin_Impact" "pixel_7_pro" "8192" "8" "64" "host" "android-34"
create_final_avd "Honkai_Star_Rail" "pixel_7_pro" "8192" "8" "48" "host" "android-34"
create_final_avd "Honkai_Impact_3rd" "pixel_7_pro" "8192" "8" "48" "host" "android-34"
create_final_avd "Zenless_Zone_Zero" "pixel_7_pro" "8192" "8" "48" "host" "android-34"
create_final_avd "Infinity_Nikki" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_final_avd "Nikke" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_final_avd "Ni_no_Kuni_Cross_Worlds" "pixel_7" "6144" "6" "24" "host" "android-34"
create_final_avd "Etheria_Restart" "pixel_7" "6144" "6" "24" "host" "android-34"
create_final_avd "Figure_Fantasy" "pixel_7" "6144" "6" "24" "host" "android-34"
create_final_avd "Epic_Seven" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_final_avd "Reverse_1999" "pixel_7" "6144" "6" "24" "host" "android-34"
create_final_avd "Solo_Leveling_Arise" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_final_avd "Seven_Deadly_Sins_Grand_Cross" "pixel_7" "6144" "6" "24" "host" "android-34"
create_final_avd "Punishing_Gray_Raven" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_final_avd "Wuthering_Waves" "pixel_7_pro" "8192" "8" "48" "host" "android-34"
create_final_avd "Astra" "pixel_7" "6144" "6" "24" "host" "android-34"

# ACTION/SHOOTER GAMES (5 AVD) - Performance Focus
info "Creazione 5 AVD Action/Shooter definitivi..."

create_final_avd "Blood_Strike" "pixel_7_pro" "8192" "8" "32" "host" "android-33"
create_final_avd "Metal_Slug_Awakening" "pixel_7" "6144" "6" "24" "host" "android-34"
create_final_avd "Phantom_Blade_Executioners" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_final_avd "Black_Beacon" "pixel_7" "6144" "6" "24" "host" "android-34"
create_final_avd "Snowbreak_Containment_Zone" "pixel_7_pro" "8192" "8" "32" "host" "android-34"

# CASUAL/PUZZLE GAMES (4 AVD) - Balanced
info "Creazione 4 AVD Casual/Puzzle definitivi..."

create_final_avd "Cookie_Run_Kingdom" "pixel_6" "4096" "4" "16" "auto" "android-34"
create_final_avd "Cookie_Run_Ovenbreak" "pixel_6" "4096" "4" "16" "auto" "android-34"
create_final_avd "Cat_Fantasy" "pixel_6" "4096" "4" "16" "auto" "android-34"
create_final_avd "One_Human" "pixel_6" "4096" "4" "16" "auto" "android-34"

# RACING GAMES (1 AVD) - High Performance
info "Creazione 1 AVD Racing definitivo..."

create_final_avd "Ace_Racer" "pixel_7_pro" "8192" "8" "32" "host" "android-34"

# SPECIAL GAMES (1 AVD)
info "Creazione 1 AVD Special definitivo..."

create_final_avd "Fairlight84" "pixel_7" "6144" "6" "24" "host" "android-34"

section "VERIFICA FINALE"

# Conta AVD totali
TOTAL_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds | wc -l)
log "Totale AVD definitivi creati: $TOTAL_AVDS"

# Lista tutti gli AVD definitivi
info "Lista completa AVD definitivi:"
$ANDROID_HOME/emulator/emulator -list-avds | sort | while read avd; do
    if [ -n "$avd" ]; then
        echo "  ✓ $avd"
    fi
done

# Calcola spazio utilizzato
TOTAL_SIZE=$(du -sh ~/.android/avd 2>/dev/null | cut -f1)
info "Spazio utilizzato AVD: $TOTAL_SIZE"

echo ""
echo "======================================================="
echo -e "${GREEN}31 AVD DEFINITIVI CON PLAY STORE COMPLETATI!${NC}"
echo "======================================================="
echo ""
echo "SOSTITUZIONE COMPLETATA:"
echo ""
echo "🗑️ ${CYAN}RIMOSSI:${NC}"
echo "• Tutti gli AVD vecchi (backup salvato)"
echo "• AVD di test con suffisso '_Test'"
echo "• Configurazioni obsolete"
echo ""
echo "✅ ${CYAN}CREATI 31 AVD DEFINITIVI:${NC}"
echo "• Nomi puliti senza suffissi"
echo "• Play Store abilitato su tutti"
echo "• Configurazioni gaming ottimizzate"
echo "• Performance massime RTX 4080"
echo ""
echo "📱 ${CYAN}MAPPING GIOCHI → AVD:${NC}"
echo "• Genshin Impact → Genshin_Impact"
echo "• Blood Strike → Blood_Strike"
echo "• Epic Seven → Epic_Seven"
echo "• Cookie Run Kingdom → Cookie_Run_Kingdom"
echo "• Ace Racer → Ace_Racer"
echo "• Honkai Star Rail → Honkai_Star_Rail"
echo "• Wuthering Waves → Wuthering_Waves"
echo "• E tutti gli altri 24 giochi!"
echo ""
echo "🎮 ${CYAN}UTILIZZO:${NC}"
echo ""
echo "1. ${YELLOW}Android Studio è già aperto${NC}"
echo "2. ${YELLOW}Tools → Virtual Device Manager${NC}"
echo "3. ${YELLOW}Seleziona AVD con nome del gioco${NC}"
echo "4. ${YELLOW}Clicca Play (▶️) per avviare${NC}"
echo "5. ${YELLOW}Apri Play Store → Cerca gioco → Installa${NC}"
echo "6. ${YELLOW}Rimuovi Gmail: ./remove_gmail_from_emulators.sh${NC}"
echo ""
echo "BACKUP VECCHI AVD: $BACKUP_DIR"
echo "SPAZIO UTILIZZATO: $TOTAL_SIZE"
echo ""
echo -e "${GREEN}SISTEMA GAMING ANDROID DEFINITIVO PRONTO!${NC} 🎮"
echo ""
echo "Ora hai 31 emulatori con nomi puliti, Play Store"
echo "e configurazioni ottimali per ogni gioco specifico!"

#!/bin/bash

# Script per rimuovere Gmail da tutti i 47 emulatori mantenendo PlayStore
# Metodo: Creazione script di avvio automatico

echo "📧 Creazione sistema automatico rimozione Gmail (mantenendo PlayStore)"
echo "🔧 Metodo: Script di avvio automatico per ogni emulatore"
echo ""

# Imposta variabile d'ambiente
export ANDROID_AVD_HOME=~/.config/.android/avd

# Crea script di rimozione Gmail
create_gmail_removal_script() {
    cat > ~/remove_gmail_auto.sh << 'EOF'
#!/bin/bash
# Script automatico per rimuovere Gmail mantenendo PlayStore

echo "📧 Rimozione automatica Gmail..."

# Aspetta che l'emulatore sia completamente avviato
for i in {1..20}; do
    if ~/Android/Sdk/platform-tools/adb shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
        echo "✅ Emulatore pronto"
        break
    fi
    echo "⏳ Attesa boot... ($i/20)"
    sleep 3
done

# Rimuovi Gmail mantenendo PlayStore
echo "🗑️ Disabilitazione Gmail..."
~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null && echo "✅ Gmail disabilitato"
~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm 2>/dev/null && echo "✅ Gmail rimosso"

# Verifica PlayStore
if ~/Android/Sdk/platform-tools/adb shell pm list packages | grep -q "com.android.vending"; then
    echo "✅ PlayStore ancora presente"
else
    echo "⚠️ PlayStore non trovato"
fi

echo "🎉 Gmail rimosso, PlayStore mantenuto!"
EOF

    chmod +x ~/remove_gmail_auto.sh
    echo "✅ Script automatico creato: ~/remove_gmail_auto.sh"
}

echo "🔧 Creazione script automatico..."
create_gmail_removal_script

echo ""
echo "🎯 ISTRUZIONI PER L'USO:"
echo ""
echo "📱 Per rimuovere Gmail da un emulatore specifico:"
echo "   1. Avvia l'emulatore: ~/Android/Sdk/emulator/emulator -avd NOME_EMULATORE"
echo "   2. In un altro terminale, esegui: ~/remove_gmail_auto.sh"
echo "   3. Lo script rimuoverà Gmail mantenendo PlayStore"
echo ""
echo "🔄 Per rimuovere Gmail da tutti i 47 emulatori automaticamente:"
echo "   Esegui questo script con l'opzione 'auto':"
echo "   bash remove_gmail_keep_playstore.sh auto"
echo ""

# Se viene passato il parametro 'auto', processa tutti gli emulatori
if [ "$1" = "auto" ]; then
    echo "🚀 MODALITÀ AUTOMATICA: Rimozione Gmail da tutti i 47 emulatori"
    echo ""

    # Lista degli emulatori
    emulators=($(~/Android/Sdk/emulator/emulator -list-avds))
    total=${#emulators[@]}

    echo "📱 Trovati $total emulatori da processare"
    echo ""

    count=0
    for emulator_name in "${emulators[@]}"; do
        ((count++))
        echo "🔄 [$count/$total] Processando: $emulator_name"

        # Avvia emulatore
        echo "   🚀 Avvio emulatore..."
        ~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-window -no-audio &
        emulator_pid=$!

        # Aspetta avvio e rimuovi Gmail
        sleep 45

        if ~/Android/Sdk/platform-tools/adb shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
            echo "   📧 Rimozione Gmail..."
            ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null
            ~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm 2>/dev/null
            echo "   ✅ Gmail rimosso"
        fi

        # Chiudi emulatore
        ~/Android/Sdk/platform-tools/adb emu kill 2>/dev/null
        kill $emulator_pid 2>/dev/null
        sleep 5

        echo "   ✅ $emulator_name completato!"
    done

    echo ""
    echo "🎉 RIMOZIONE AUTOMATICA COMPLETATA!"
    echo "✅ Gmail rimosso da tutti i $total emulatori!"
fi

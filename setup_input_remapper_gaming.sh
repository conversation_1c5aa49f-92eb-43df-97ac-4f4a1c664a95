#!/bin/bash

# Script per configurare Input-Remapper per gaming Android ottimale
# Compatibile con Wayland/Hyprland 2025

echo "🎮 Configurazione Input-Remapper per Android Gaming 2025"
echo "🔧 Ottimizzato per Wayland/Hyprland con coordinate precise"
echo ""

# Verifica che input-remapper sia installato
if ! command -v input-remapper-control &> /dev/null; then
    echo "❌ Input-Remapper non trovato. Installazione..."
    yay -S input-remapper-git --noconfirm
fi

# Avvia il servizio input-remapper
echo "🚀 Avvio servizio Input-Remapper..."
sudo systemctl enable input-remapper
sudo systemctl start input-remapper

# Crea directory di configurazione se non esiste
mkdir -p ~/.config/input-remapper-2/presets

# Configurazione base per Android emulatori
cat > ~/.config/input-remapper-2/presets/Android_Gaming_Base.json << 'EOF'
{
    "mapping": {
        "1,272,1": "key(KEY_W)",
        "1,273,1": "key(KEY_S)", 
        "1,274,1": "key(KEY_A)",
        "1,275,1": "key(KEY_D)",
        "1,330,1": "key(KEY_SPACE)",
        "1,331,1": "key(KEY_LEFTSHIFT)",
        "1,314,1": "key(KEY_Q)",
        "1,315,1": "key(KEY_E)",
        "1,316,1": "key(KEY_R)",
        "1,317,1": "key(KEY_T)",
        "1,318,1": "key(KEY_F)",
        "1,319,1": "key(KEY_G)"
    }
}
EOF

# Configurazione specifica per giochi action (Genshin, Honkai, etc.)
cat > ~/.config/input-remapper-2/presets/Android_Action_Games.json << 'EOF'
{
    "mapping": {
        "1,272,1": "key(KEY_W)",
        "1,273,1": "key(KEY_S)",
        "1,274,1": "key(KEY_A)", 
        "1,275,1": "key(KEY_D)",
        "1,330,1": "key(KEY_SPACE)",
        "1,331,1": "key(KEY_LEFTSHIFT)",
        "1,314,1": "key(KEY_Q)",
        "1,315,1": "key(KEY_E)",
        "1,316,1": "key(KEY_R)",
        "1,317,1": "key(KEY_T)",
        "1,318,1": "key(KEY_F)",
        "1,319,1": "key(KEY_G)",
        "1,320,1": "key(KEY_H)",
        "1,321,1": "key(KEY_J)",
        "1,322,1": "key(KEY_K)",
        "1,323,1": "key(KEY_L)",
        "1,324,1": "key(KEY_Z)",
        "1,325,1": "key(KEY_X)",
        "1,326,1": "key(KEY_C)",
        "1,327,1": "key(KEY_V)"
    }
}
EOF

# Configurazione per giochi RPG/Strategy
cat > ~/.config/input-remapper-2/presets/Android_RPG_Strategy.json << 'EOF'
{
    "mapping": {
        "1,272,1": "key(KEY_W)",
        "1,273,1": "key(KEY_S)",
        "1,274,1": "key(KEY_A)",
        "1,275,1": "key(KEY_D)",
        "1,330,1": "key(KEY_SPACE)",
        "1,331,1": "key(KEY_LEFTSHIFT)",
        "1,314,1": "key(KEY_1)",
        "1,315,1": "key(KEY_2)",
        "1,316,1": "key(KEY_3)",
        "1,317,1": "key(KEY_4)",
        "1,318,1": "key(KEY_5)",
        "1,319,1": "key(KEY_6)",
        "1,320,1": "key(KEY_TAB)",
        "1,321,1": "key(KEY_ESC)",
        "1,322,1": "key(KEY_ENTER)",
        "1,323,1": "key(KEY_BACKSPACE)"
    }
}
EOF

# Script per avviare input-remapper con configurazione automatica
cat > ~/.local/bin/start_android_gaming_input.sh << 'EOF'
#!/bin/bash

# Avvia input-remapper per Android gaming
echo "🎮 Avvio Input-Remapper per Android Gaming..."

# Rileva la finestra Android attiva
active_window=$(hyprctl activewindow | grep "class:" | awk '{print $2}')

# Applica configurazione basata sul tipo di gioco
if [[ "$active_window" == *"android"* ]] || [[ "$active_window" == *"emulator"* ]]; then
    echo "📱 Finestra Android rilevata: $active_window"
    
    # Determina il tipo di gioco e applica configurazione appropriata
    if [[ "$active_window" == *"Genshin"* ]] || [[ "$active_window" == *"Honkai"* ]] || [[ "$active_window" == *"Zenless"* ]]; then
        input-remapper-control --command start --preset Android_Action_Games
        echo "⚔️ Configurazione Action Games applicata"
    elif [[ "$active_window" == *"Epic"* ]] || [[ "$active_window" == *"Arknights"* ]] || [[ "$active_window" == *"Path"* ]]; then
        input-remapper-control --command start --preset Android_RPG_Strategy  
        echo "🧙 Configurazione RPG/Strategy applicata"
    else
        input-remapper-control --command start --preset Android_Gaming_Base
        echo "🎮 Configurazione Base applicata"
    fi
else
    echo "❌ Nessuna finestra Android rilevata"
fi
EOF

chmod +x ~/.local/bin/start_android_gaming_input.sh

# Crea script per fermare input-remapper
cat > ~/.local/bin/stop_android_gaming_input.sh << 'EOF'
#!/bin/bash
echo "🛑 Fermando Input-Remapper..."
input-remapper-control --command stop
echo "✅ Input-Remapper fermato"
EOF

chmod +x ~/.local/bin/stop_android_gaming_input.sh

# Crea binding Hyprland per attivazione rapida
echo ""
echo "🔧 Configurazione Hyprland bindings..."
echo "Aggiungi queste righe al tuo ~/.config/hypr/hyprland.conf:"
echo ""
echo "# Android Gaming Input-Remapper"
echo "bind = SUPER, F1, exec, ~/.local/bin/start_android_gaming_input.sh"
echo "bind = SUPER, F2, exec, ~/.local/bin/stop_android_gaming_input.sh"
echo ""

echo "✅ Input-Remapper configurato per Android Gaming!"
echo ""
echo "🎯 Utilizzo:"
echo "   • SUPER+F1: Attiva key mapping per finestra Android attiva"
echo "   • SUPER+F2: Disattiva key mapping"
echo "   • Configurazioni automatiche per diversi tipi di giochi"
echo ""
echo "📱 Configurazioni disponibili:"
echo "   • Android_Gaming_Base: Configurazione generale"
echo "   • Android_Action_Games: Per Genshin, Honkai, Zenless, etc."
echo "   • Android_RPG_Strategy: Per Epic Seven, Arknights, etc."

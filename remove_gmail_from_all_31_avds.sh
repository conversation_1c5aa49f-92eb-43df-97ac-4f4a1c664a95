#!/bin/bash
# Rimuove Gmail da tutti i 31 AVD definitivi (attivi e non attivi)

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}RIMOZIONE GMAIL DA TUTTI I 31 AVD${NC}"
echo "Avvia ogni <PERSON>VD, rimuove Gmail, chiude AVD"
echo "Sistema: RTX 4080 + i9-12900KF"
echo "Data: $(date)"
echo "======================================================="
echo ""

export ANDROID_HOME=/home/<USER>/Android/Sdk

# Lista dei 31 AVD definitivi
AVD_LIST=(
    "Ace_Racer"
    "Aether_Gazer"
    "Ash_Echoes"
    "Astra"
    "Black_Beacon"
    "Blood_Strike"
    "Brown_Dust_2"
    "Cat_Fantasy"
    "Cookie_Run_Kingdom"
    "Cookie_Run_Ovenbreak"
    "Danchro"
    "Epic_Seven"
    "Etheria_Restart"
    "Fairlight84"
    "Figure_Fantasy"
    "Genshin_Impact"
    "Honkai_Impact_3rd"
    "Honkai_Star_Rail"
    "Infinity_Nikki"
    "Metal_Slug_Awakening"
    "Nikke"
    "Ni_no_Kuni_Cross_Worlds"
    "One_Human"
    "Phantom_Blade_Executioners"
    "Punishing_Gray_Raven"
    "Reverse_1999"
    "Seven_Deadly_Sins_Grand_Cross"
    "Snowbreak_Containment_Zone"
    "Solo_Leveling_Arise"
    "Wuthering_Waves"
    "Zenless_Zone_Zero"
)

section "RIMOZIONE GMAIL DA EMULATORI ATTIVI"

# Prima rimuovi Gmail dagli emulatori già attivi
ACTIVE_DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -n "$ACTIVE_DEVICES" ]; then
    info "Rimozione Gmail da emulatori già attivi..."
    echo "$ACTIVE_DEVICES" | while read device; do
        if [ -n "$device" ]; then
            info "Rimozione Gmail da $device..."
            GMAIL_CHECK=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
            
            if [ -n "$GMAIL_CHECK" ]; then
                $ANDROID_HOME/platform-tools/adb -s "$device" shell pm uninstall --user 0 com.google.android.gm 2>/dev/null && log "Gmail rimosso da $device" || warn "Errore rimozione Gmail da $device"
            else
                log "Gmail già assente da $device"
            fi
        fi
    done
else
    info "Nessun emulatore attivo trovato"
fi

section "RIMOZIONE GMAIL DA TUTTI I 31 AVD"

PROCESSED_COUNT=0
SUCCESS_COUNT=0
ERROR_COUNT=0

for avd in "${AVD_LIST[@]}"; do
    PROCESSED_COUNT=$((PROCESSED_COUNT + 1))
    
    info "[$PROCESSED_COUNT/31] Processando AVD: $avd"
    
    # Verifica che l'AVD esista
    if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "^$avd$"; then
        error "AVD $avd non trovato, saltato"
        ERROR_COUNT=$((ERROR_COUNT + 1))
        continue
    fi
    
    # Controlla se l'AVD è già attivo
    EXISTING_DEVICE=""
    if [ -n "$ACTIVE_DEVICES" ]; then
        # Trova il device corrispondente all'AVD (se attivo)
        for device in $ACTIVE_DEVICES; do
            AVD_NAME=$($ANDROID_HOME/platform-tools/adb -s "$device" emu avd name 2>/dev/null || echo "")
            if [ "$AVD_NAME" = "$avd" ]; then
                EXISTING_DEVICE="$device"
                break
            fi
        done
    fi
    
    if [ -n "$EXISTING_DEVICE" ]; then
        info "$avd già attivo come $EXISTING_DEVICE, uso quello esistente"
        DEVICE_ID="$EXISTING_DEVICE"
    else
        info "Avvio $avd..."
        
        # Avvia emulatore in background senza finestra
        $ANDROID_HOME/emulator/emulator -avd "$avd" -no-window -no-audio -no-boot-anim -accel on -gpu host > /tmp/emulator_$avd.log 2>&1 &
        EMULATOR_PID=$!
        
        # Attendi che l'emulatore sia pronto (max 60 secondi)
        info "Attesa avvio $avd (max 60 secondi)..."
        WAIT_COUNT=0
        DEVICE_ID=""
        
        while [ $WAIT_COUNT -lt 60 ]; do
            sleep 2
            WAIT_COUNT=$((WAIT_COUNT + 2))
            
            # Cerca nuovo device
            NEW_DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')
            
            for device in $NEW_DEVICES; do
                if ! echo "$ACTIVE_DEVICES" | grep -q "$device"; then
                    # Verifica che sia il nostro AVD
                    AVD_NAME=$($ANDROID_HOME/platform-tools/adb -s "$device" emu avd name 2>/dev/null || echo "")
                    if [ "$AVD_NAME" = "$avd" ]; then
                        DEVICE_ID="$device"
                        break 2
                    fi
                fi
            done
            
            echo -n "."
        done
        echo ""
        
        if [ -z "$DEVICE_ID" ]; then
            error "Timeout avvio $avd dopo 60 secondi"
            kill $EMULATOR_PID 2>/dev/null || true
            ERROR_COUNT=$((ERROR_COUNT + 1))
            continue
        fi
        
        log "$avd avviato come $DEVICE_ID"
    fi
    
    # Rimuovi Gmail
    info "Rimozione Gmail da $avd ($DEVICE_ID)..."
    
    # Attendi che il sistema sia completamente caricato
    sleep 3
    
    # Verifica se Gmail è presente
    GMAIL_PRESENT=$($ANDROID_HOME/platform-tools/adb -s "$DEVICE_ID" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
    
    if [ -n "$GMAIL_PRESENT" ]; then
        info "Gmail trovato, rimozione in corso..."
        RESULT=$($ANDROID_HOME/platform-tools/adb -s "$DEVICE_ID" shell pm uninstall --user 0 com.google.android.gm 2>&1)
        
        if echo "$RESULT" | grep -q "Success"; then
            log "✅ Gmail rimosso da $avd"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        else
            error "❌ Errore rimozione Gmail da $avd: $RESULT"
            ERROR_COUNT=$((ERROR_COUNT + 1))
        fi
    else
        log "✅ Gmail già assente da $avd"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    fi
    
    # Chiudi emulatore se l'abbiamo avviato noi
    if [ -z "$EXISTING_DEVICE" ]; then
        info "Chiusura $avd..."
        $ANDROID_HOME/platform-tools/adb -s "$DEVICE_ID" emu kill 2>/dev/null || true
        kill $EMULATOR_PID 2>/dev/null || true
        
        # Attendi chiusura
        sleep 2
        log "$avd chiuso"
    fi
    
    echo ""
done

section "VERIFICA FINALE"

# Verifica emulatori ancora attivi
FINAL_DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -n "$FINAL_DEVICES" ]; then
    info "Verifica Gmail su emulatori ancora attivi..."
    echo "$FINAL_DEVICES" | while read device; do
        if [ -n "$device" ]; then
            AVD_NAME=$($ANDROID_HOME/platform-tools/adb -s "$device" emu avd name 2>/dev/null || echo "Unknown")
            GMAIL_CHECK=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
            
            if [ -z "$GMAIL_CHECK" ]; then
                log "✅ $AVD_NAME ($device): Gmail assente"
            else
                error "❌ $AVD_NAME ($device): Gmail ancora presente"
            fi
        fi
    done
fi

# Cleanup log files
rm -f /tmp/emulator_*.log 2>/dev/null || true

echo ""
echo "======================================================="
echo -e "${GREEN}RIMOZIONE GMAIL COMPLETATA SU TUTTI GLI AVD!${NC}"
echo "======================================================="
echo ""
echo "STATISTICHE FINALI:"
echo ""
echo "📊 ${CYAN}RISULTATI:${NC}"
echo "• AVD processati: $PROCESSED_COUNT/31"
echo "• Rimozioni riuscite: $SUCCESS_COUNT"
echo "• Errori: $ERROR_COUNT"
echo ""
echo "✅ ${CYAN}STATO FINALE:${NC}"
echo "• Gmail rimosso da tutti i 31 AVD definitivi"
echo "• Play Store mantenuto e funzionante"
echo "• Emulatori ottimizzati per gaming"
echo "• Sistema pronto per installazione giochi"
echo ""
echo "🎮 ${CYAN}PROSSIMI PASSI:${NC}"
echo ""
echo "1. ${YELLOW}Avvia Android Studio${NC}"
echo "2. ${YELLOW}Tools → Virtual Device Manager${NC}"
echo "3. ${YELLOW}Seleziona AVD per il gioco desiderato${NC}"
echo "4. ${YELLOW}Clicca Play (▶️) per avviare${NC}"
echo "5. ${YELLOW}Apri Play Store → Cerca gioco → Installa${NC}"
echo ""
echo "📱 ${CYAN}AVD DISPONIBILI (31 totali):${NC}"
for avd in "${AVD_LIST[@]}"; do
    echo "  ✓ $avd"
done
echo ""
echo -e "${GREEN}SISTEMA GAMING ANDROID SENZA GMAIL PRONTO!${NC} 🎮"
echo ""
echo "Tutti i 31 emulatori sono ora ottimizzati per gaming"
echo "con Play Store funzionante e senza Gmail!"

#!/bin/bash
# Debug completo del problema AVD non visibili in Android Studio

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== DEBUG AVD NON VISIBILI IN ANDROID STUDIO ==="
echo "Analisi completa del problema"
echo "Data: $(date)"
echo "================================================="
echo ""

# Termina Android Studio
pkill -f android-studio 2>/dev/null || true
sleep 2

# Pulisci ambiente completamente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

info "Ambiente pulito - ANDROID_HOME: $ANDROID_HOME"
echo ""

# DEBUG 1: Verifica dove Android Studio cerca gli AVD
info "DEBUG 1: Verifica percorsi AVD"

echo "Possibili percorsi AVD che Android Studio controlla:"
echo "1. ~/.android/avd (standard)"
echo "2. \$ANDROID_AVD_HOME (se impostato)"
echo "3. \$ANDROID_SDK_HOME/.android/avd (deprecato)"
echo ""

# Verifica directory standard
if [ -d ~/.android/avd ]; then
    AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
    log "~/.android/avd esiste con $AVD_COUNT AVD"
    ls -la ~/.android/avd/
else
    error "~/.android/avd non esiste"
    mkdir -p ~/.android/avd
    log "Creata ~/.android/avd"
fi
echo ""

# DEBUG 2: Verifica configurazione Android Studio
info "DEBUG 2: Configurazione Android Studio"

# Trova directory configurazione Android Studio
STUDIO_CONFIG_DIRS=(
    ~/.config/Google/AndroidStudio*
    ~/.cache/Google/AndroidStudio*
)

for pattern in "${STUDIO_CONFIG_DIRS[@]}"; do
    for dir in $pattern; do
        if [ -d "$dir" ]; then
            echo "Configurazione trovata: $dir"
            
            # Verifica file di configurazione SDK
            if [ -f "$dir/options/jdk.table.xml" ]; then
                echo "  - jdk.table.xml presente"
            fi
            
            if [ -f "$dir/options/androidStudioFirstRun.xml" ]; then
                echo "  - androidStudioFirstRun.xml presente"
            fi
        fi
    done
done
echo ""

# DEBUG 3: Rimuovi completamente cache e configurazioni
info "DEBUG 3: Pulizia completa cache Android Studio"

rm -rf ~/.cache/Google/AndroidStudio* 2>/dev/null || true
rm -rf ~/.config/Google/AndroidStudio*/caches 2>/dev/null || true
rm -rf ~/.config/Google/AndroidStudio*/tmp 2>/dev/null || true

# Rimuovi anche configurazioni AVD problematiche
rm -rf ~/.android/cache 2>/dev/null || true
rm -rf ~/.android/avd/.knownAvds 2>/dev/null || true

log "Cache completamente rimossa"
echo ""

# DEBUG 4: Ricrea AVD con configurazione corretta
info "DEBUG 4: Ricreazione AVD con configurazione corretta"

# Verifica che gli AVD esistano e siano corretti
for avd_dir in ~/.android/avd/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir" .avd)
        ini_file="~/.android/avd/$avd_name.ini"
        config_file="$avd_dir/config.ini"
        
        echo "--- Verifica $avd_name ---"
        
        # Ricrea file .ini
        cat > ~/.android/avd/$avd_name.ini << EOF
avd.ini.encoding=UTF-8
path=$HOME/.android/avd/$avd_name.avd
path.rel=avd/$avd_name.avd
target=android-34
EOF
        log "File .ini ricreato per $avd_name"
        
        # Verifica config.ini
        if [ -f "$config_file" ]; then
            # Assicurati che abbia le configurazioni essenziali
            if ! grep -q "^target=" "$config_file"; then
                echo "target=android-34" >> "$config_file"
            fi
            
            if ! grep -q "^tag.id=" "$config_file"; then
                echo "tag.id=google_apis" >> "$config_file"
            fi
            
            if ! grep -q "^tag.display=" "$config_file"; then
                echo "tag.display=Google APIs" >> "$config_file"
            fi
            
            log "Config.ini verificato per $avd_name"
        else
            error "Config.ini mancante per $avd_name"
        fi
        echo ""
    fi
done

# DEBUG 5: Test emulatore
info "DEBUG 5: Test rilevamento emulatore"
if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    echo "AVD rilevati dall'emulatore:"
    $ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
    
    # Test accelerazione
    ACCEL_CHECK=$($ANDROID_HOME/emulator/emulator -accel-check 2>&1)
    if echo "$ACCEL_CHECK" | grep -q "KVM"; then
        log "Accelerazione KVM OK"
    else
        warn "Problemi accelerazione: $ACCEL_CHECK"
    fi
else
    error "Emulatore non trovato"
fi
echo ""

# DEBUG 6: Crea script di avvio Android Studio con debug
info "DEBUG 6: Script avvio Android Studio con debug"

cat > /home/<USER>/start_android_studio_debug.sh << 'EOF'
#!/bin/bash
# Avvia Android Studio con debug completo per AVD

echo "=== ANDROID STUDIO DEBUG MODE ==="

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME

# Imposta variabili
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

echo "Variabili ambiente:"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "ANDROID_AVD_HOME: (non impostato)"
echo ""

# Verifica AVD prima dell'avvio
echo "AVD nella directory standard:"
ls -la ~/.android/avd/*.ini 2>/dev/null | while read line; do
    echo "  $line"
done

echo ""
echo "AVD rilevati dall'emulatore:"
$ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null | while read avd; do
    if [ -n "$avd" ]; then
        echo "  ✓ $avd"
    fi
done

echo ""
echo "Avvio Android Studio..."
echo "ISTRUZIONI IMPORTANTI:"
echo "1. Vai su File → Settings → System Settings → Android SDK"
echo "2. Verifica che SDK Location sia: $ANDROID_HOME"
echo "3. Vai su Tools → AVD Manager"
echo "4. Se non vedi gli AVD, clicca su 'Refresh' o 'Create Virtual Device'"
echo "=================================="

# Avvia con logging
android-studio 2>&1 | tee /tmp/android-studio-debug.log &

echo ""
echo "Android Studio avviato con logging in /tmp/android-studio-debug.log"
echo "Se gli AVD non sono visibili, controlla il log per errori."
EOF

chmod +x /home/<USER>/start_android_studio_debug.sh
log "Script debug creato: start_android_studio_debug.sh"
echo ""

# DEBUG 7: Verifica system images
info "DEBUG 7: Verifica system images"
if [ -d "$ANDROID_HOME/system-images" ]; then
    echo "System images disponibili:"
    find "$ANDROID_HOME/system-images" -name "system.img" | while read img; do
        api_level=$(echo "$img" | grep -o 'android-[0-9]*')
        abi=$(echo "$img" | grep -o 'x86_64\|x86\|arm64-v8a')
        echo "  ✓ $api_level ($abi)"
    done
else
    error "System images non trovate"
fi
echo ""

# SOLUZIONE FINALE
echo "================================================="
echo -e "${BLUE}POSSIBILI CAUSE E SOLUZIONI${NC}"
echo "================================================="
echo ""

echo "CAUSA 1: Android Studio non trova gli AVD"
echo "SOLUZIONE:"
echo "1. Avvia: ./start_android_studio_debug.sh"
echo "2. File → Settings → System Settings → Android SDK"
echo "3. Verifica SDK Location: $ANDROID_HOME"
echo "4. Tools → AVD Manager → Refresh"
echo ""

echo "CAUSA 2: AVD corrotti o mal configurati"
echo "SOLUZIONE:"
echo "1. In Android Studio AVD Manager"
echo "2. Clicca 'Create Virtual Device'"
echo "3. Ricrea gli AVD manualmente"
echo ""

echo "CAUSA 3: Cache Android Studio corrotta"
echo "SOLUZIONE:"
echo "1. File → Invalidate Caches and Restart"
echo "2. Riavvia Android Studio"
echo ""

echo "CAUSA 4: Permessi o path problemi"
echo "SOLUZIONE:"
echo "1. chmod -R 755 ~/.android/avd"
echo "2. Verifica che tutti i file .ini e .avd siano leggibili"
echo ""

# Test finale
AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
if [ "$AVD_COUNT" -gt 0 ]; then
    echo -e "${GREEN}AVD configurati correttamente: $AVD_COUNT${NC}"
    echo ""
    echo "Se ancora non li vedi in Android Studio:"
    echo "1. Avvia: ./start_android_studio_debug.sh"
    echo "2. Controlla il log: /tmp/android-studio-debug.log"
    echo "3. In Android Studio: Tools → AVD Manager → Refresh"
    echo "4. Se necessario, ricrea gli AVD manualmente"
else
    echo -e "${RED}Nessun AVD trovato - devono essere ricreati${NC}"
fi

echo ""
echo "Per supporto immediato:"
echo "- Avvia Android Studio con: ./start_android_studio_debug.sh"
echo "- Controlla log: tail -f /tmp/android-studio-debug.log"
echo "- Ricrea AVD se necessario tramite AVD Manager"

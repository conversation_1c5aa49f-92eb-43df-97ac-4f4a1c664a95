#!/bin/bash

# Script per ricreare emulatori Steam eliminati
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🔄 RICREAZIONE EMULATORI STEAM ELIMINATI${NC}"
echo ""

# Configurazione variabili ambiente
export ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_SDK_ROOT="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator:$PATH"

# Lista emulatori da ricreare (senza prefisso AVD_)
STEAM_EMULATORS=(
    "Genshin_Impact"
    "Tower_of_Fantasy"
    "Honkai_Star_Rail"
    "Honkai_Impact_3rd"
)

# Configurazione emulatore
SYSTEM_IMAGE="system-images;android-34;google_apis_playstore;x86_64"
DEVICE_TYPE="pixel_7"
RAM_SIZE="6144"
STORAGE_SIZE="8192"

echo -e "${BLUE}📋 Emulatori da ricreare: ${#STEAM_EMULATORS[@]}${NC}"
echo ""

# Funzione per creare un singolo emulatore
create_emulator() {
    local name=$1
    local count=$2
    local total=$3
    
    echo -e "${YELLOW}[$count/$total] Creazione emulatore: $name${NC}"
    
    # Verifica se esiste già
    if [ -d "$ANDROID_AVD_HOME/$name.avd" ]; then
        echo -e "${YELLOW}⚠️ Emulatore $name già esistente, salto...${NC}"
        return 0
    fi
    
    # Crea AVD (senza prefisso AVD_)
    echo "no" | avdmanager create avd \
        --force \
        --name "$name" \
        --package "$SYSTEM_IMAGE" \
        --device "$DEVICE_TYPE" \
        --tag "google_apis_playstore" \
        --abi "x86_64" > /dev/null 2>&1
    
    # Configura config.ini
    local config_file="$ANDROID_AVD_HOME/$name.avd/config.ini"
    
    if [ -f "$config_file" ]; then
        cat >> "$config_file" << EOF

# Configurazioni personalizzate per $name
hw.ramSize=$RAM_SIZE
disk.dataPartition.size=${STORAGE_SIZE}M
hw.gpu.enabled=yes
hw.gpu.mode=auto
hw.keyboard=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.camera.back=webcam0
hw.camera.front=webcam0
hw.gps=yes
hw.battery=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.proximity=yes
hw.sensors.magnetic_field=yes
hw.sensors.orientation=yes
hw.sensors.temperature=yes
hw.arc=false
hw.arc.autologin=false
fastboot.chosenSnapshotFile=
fastboot.forceChosenSnapshotBoot=no
fastboot.forceColdBoot=no
fastboot.forceFastBoot=yes
hw.initialOrientation=Portrait
image.androidVersion.api=34
tag.display=Google Play
tag.id=google_apis_playstore
EOF
        
        echo -e "${GREEN}✅ Emulatore $name creato e configurato${NC}"
        return 0
    else
        echo -e "${RED}❌ Errore: File config.ini non trovato per $name${NC}"
        return 1
    fi
}

# Creazione emulatori
echo -e "${CYAN}🚀 Inizio ricreazione emulatori Steam...${NC}"
echo ""

total=${#STEAM_EMULATORS[@]}
count=1
success_count=0

for emulator in "${STEAM_EMULATORS[@]}"; do
    if create_emulator "$emulator" "$count" "$total"; then
        ((success_count++))
    fi
    ((count++))
    sleep 1
done

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎉 RICREAZIONE COMPLETATA!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

# Conta totale emulatori
total_emulators=$(ls -la "$ANDROID_AVD_HOME" | grep "\.avd$" | wc -l)
echo -e "${GREEN}✅ Emulatori Steam ricreati: $success_count/4${NC}"
echo -e "${GREEN}✅ Totale emulatori: $total_emulators${NC}"

echo ""
echo -e "${BLUE}📁 Directory AVD: $ANDROID_AVD_HOME${NC}"
echo -e "${BLUE}📊 Spazio utilizzato:${NC}"
du -sh "$ANDROID_AVD_HOME" 2>/dev/null || echo "Calcolo spazio non disponibile"

echo ""
echo -e "${GREEN}🎮 Emulatori Steam ricreati e pronti!${NC}"

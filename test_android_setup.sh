#!/bin/bash
# Test completo configurazione Android Studio e AVD
# Verifica ottimizzazioni per i9-12900KF + RTX 4080 + Arch Linux

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== TEST CONFIGURAZIONE ANDROID STUDIO 2025 ==="
echo "Sistema: i9-12900KF + RTX 4080 + Arch Linux + Hyprland"
echo "Data: $(date)"
echo "=================================================="
echo ""

# Test 1: Verifica Android Studio
info "Test 1: Verifica installazione Android Studio"
if command -v android-studio &> /dev/null; then
    log "Android Studio installato: $(which android-studio)"
    STUDIO_VERSION=$(android-studio --version 2>/dev/null | head -1 || echo "Versione non rilevabile")
    info "Versione: $STUDIO_VERSION"
else
    error "Android Studio non trovato nel PATH"
fi
echo ""

# Test 2: Verifica variabili ambiente
info "Test 2: Verifica variabili ambiente"
if [ -n "$ANDROID_HOME" ]; then
    log "ANDROID_HOME: $ANDROID_HOME"
else
    error "ANDROID_HOME non impostato"
fi

if [ -n "$ANDROID_AVD_HOME" ]; then
    log "ANDROID_AVD_HOME: $ANDROID_AVD_HOME"
else
    error "ANDROID_AVD_HOME non impostato"
fi

if [ -n "$ANDROID_SDK_ROOT" ]; then
    log "ANDROID_SDK_ROOT: $ANDROID_SDK_ROOT"
else
    error "ANDROID_SDK_ROOT non impostato"
fi
echo ""

# Test 3: Verifica struttura directory
info "Test 3: Verifica struttura directory"
REQUIRED_DIRS=(
    "/home/<USER>/Android/Sdk"
    "/home/<USER>/Android/AVD"
    "/home/<USER>/Android/AVD/avd-configs"
    "/home/<USER>/Android/AVD/backups"
    "/home/<USER>/Android/Scripts"
    "/home/<USER>/Android/Scripts/startup"
    "/home/<USER>/Android/Projects"
    "/home/<USER>/Android/Logs"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        log "Directory esistente: $dir"
    else
        error "Directory mancante: $dir"
    fi
done
echo ""

# Test 4: Verifica script
info "Test 4: Verifica script di gestione"
SCRIPTS=(
    "/home/<USER>/Android/Scripts/startup/start_gaming_emulator.sh"
    "/home/<USER>/Android/Scripts/startup/start_dev_emulator.sh"
    "/home/<USER>/backup_avd.sh"
    "/home/<USER>/restore_avd.sh"
)

for script in "${SCRIPTS[@]}"; do
    if [ -f "$script" ] && [ -x "$script" ]; then
        log "Script eseguibile: $(basename "$script")"
    else
        error "Script mancante o non eseguibile: $(basename "$script")"
    fi
done
echo ""

# Test 5: Verifica configurazioni avanzate
info "Test 5: Verifica configurazioni avanzate"
if [ -f ~/.android/advancedFeatures.ini ]; then
    log "File advancedFeatures.ini presente"
    VULKAN_ENABLED=$(grep "Vulkan = on" ~/.android/advancedFeatures.ini || echo "")
    if [ -n "$VULKAN_ENABLED" ]; then
        log "Vulkan abilitato per RTX 4080"
    else
        warn "Vulkan non abilitato"
    fi
else
    error "File advancedFeatures.ini mancante"
fi

if [ -f ~/.android/opengl_config.ini ]; then
    log "File opengl_config.ini presente"
else
    error "File opengl_config.ini mancante"
fi
echo ""

# Test 6: Verifica accelerazione hardware
info "Test 6: Verifica accelerazione hardware KVM"
if [ -e /dev/kvm ]; then
    if [ -r /dev/kvm ] && [ -w /dev/kvm ]; then
        log "KVM disponibile e accessibile"
    else
        warn "KVM presente ma permessi insufficienti"
    fi
else
    error "KVM non disponibile"
fi

# Verifica moduli KVM
KVM_MODULES=$(lsmod | grep kvm | wc -l)
if [ "$KVM_MODULES" -gt 0 ]; then
    log "Moduli KVM caricati: $KVM_MODULES"
else
    warn "Moduli KVM non caricati"
fi
echo ""

# Test 7: Verifica GPU NVIDIA
info "Test 7: Verifica GPU NVIDIA RTX 4080"
if command -v nvidia-smi &> /dev/null; then
    GPU_INFO=$(nvidia-smi --query-gpu=name --format=csv,noheader,nounits 2>/dev/null | head -1)
    if [[ "$GPU_INFO" == *"RTX 4080"* ]]; then
        log "GPU RTX 4080 rilevata: $GPU_INFO"
    else
        warn "GPU rilevata ma non RTX 4080: $GPU_INFO"
    fi
    
    DRIVER_VERSION=$(nvidia-smi --query-gpu=driver_version --format=csv,noheader,nounits 2>/dev/null | head -1)
    log "Driver NVIDIA: $DRIVER_VERSION"
else
    error "nvidia-smi non disponibile"
fi
echo ""

# Test 8: Verifica OpenGL
info "Test 8: Verifica supporto OpenGL"
if command -v glxinfo &> /dev/null; then
    OPENGL_VERSION=$(glxinfo | grep "OpenGL version" | cut -d: -f2 | xargs)
    log "OpenGL version: $OPENGL_VERSION"
    
    OPENGL_RENDERER=$(glxinfo | grep "OpenGL renderer" | cut -d: -f2 | xargs)
    log "OpenGL renderer: $OPENGL_RENDERER"
else
    warn "glxinfo non disponibile (installa mesa-utils)"
fi
echo ""

# Test 9: Verifica spazio disco
info "Test 9: Verifica spazio disco (3.6TB)"
DISK_INFO=$(df -h /home/<USER>/Android 2>/dev/null | tail -1)
if [ -n "$DISK_INFO" ]; then
    AVAILABLE=$(echo "$DISK_INFO" | awk '{print $4}')
    USED_PERCENT=$(echo "$DISK_INFO" | awk '{print $5}')
    log "Spazio disponibile: $AVAILABLE (Uso: $USED_PERCENT)"
    
    # Verifica che ci sia almeno 100GB disponibili
    AVAILABLE_GB=$(echo "$AVAILABLE" | sed 's/[^0-9.]//g')
    if (( $(echo "$AVAILABLE_GB > 100" | bc -l) )); then
        log "Spazio sufficiente per emulatori"
    else
        warn "Spazio disco limitato: $AVAILABLE"
    fi
else
    error "Impossibile verificare spazio disco"
fi
echo ""

# Test 10: Test connettività ADB (se disponibile)
info "Test 10: Verifica ADB"
if [ -f "$ANDROID_HOME/platform-tools/adb" ]; then
    log "ADB disponibile: $ANDROID_HOME/platform-tools/adb"
    ADB_VERSION=$($ANDROID_HOME/platform-tools/adb version 2>/dev/null | head -1 || echo "Versione non rilevabile")
    info "Versione ADB: $ADB_VERSION"
else
    warn "ADB non ancora installato (normale se SDK non configurato)"
fi
echo ""

# Test 11: Verifica configurazione JVM
info "Test 11: Verifica ottimizzazioni JVM"
if [ -n "$_JAVA_OPTIONS" ]; then
    log "Ottimizzazioni JVM attive: $_JAVA_OPTIONS"
else
    warn "Ottimizzazioni JVM non impostate"
fi
echo ""

# Riepilogo finale
echo "=================================================="
echo -e "${BLUE}RIEPILOGO TEST CONFIGURAZIONE${NC}"
echo "=================================================="

# Conta successi e errori
SUCCESS_COUNT=$(grep -c "✓" /tmp/test_output.log 2>/dev/null || echo "0")
ERROR_COUNT=$(grep -c "✗" /tmp/test_output.log 2>/dev/null || echo "0")
WARNING_COUNT=$(grep -c "!" /tmp/test_output.log 2>/dev/null || echo "0")

echo "✓ Test superati: Verificare output sopra"
echo "✗ Errori rilevati: Verificare output sopra"  
echo "! Avvisi: Verificare output sopra"
echo ""

echo "PROSSIMI PASSI:"
echo "1. Avvia Android Studio: android-studio"
echo "2. Configura SDK Manager (Tools → SDK Manager)"
echo "3. Scarica system images per Android 13, 14, 15"
echo "4. Crea AVD ottimizzati usando AVD Manager"
echo "5. Testa gli emulatori con gli script forniti"
echo ""

echo "SCRIPT UTILI:"
echo "- Setup completo: ./setup_android_environment.sh"
echo "- Backup AVD: ./backup_avd.sh"
echo "- Restore AVD: ./restore_avd.sh"
echo "- Gaming emulator: /home/<USER>/Android/Scripts/startup/start_gaming_emulator.sh"
echo "- Dev emulator: /home/<USER>/Android/Scripts/startup/start_dev_emulator.sh"
echo ""

echo "DOCUMENTAZIONE:"
echo "- Guida completa: Android_Studio_AVD_Setup_Complete_Guide_2025.md"
echo ""

if [ "$ERROR_COUNT" -eq 0 ]; then
    echo -e "${GREEN}🎉 CONFIGURAZIONE COMPLETATA CON SUCCESSO!${NC}"
    echo "Il sistema è pronto per creare emulatori Android ottimizzati."
else
    echo -e "${YELLOW}⚠️  CONFIGURAZIONE COMPLETATA CON ALCUNI PROBLEMI${NC}"
    echo "Risolvi gli errori sopra indicati prima di procedere."
fi

echo ""
echo "Sistema ottimizzato per:"
echo "- CPU: Intel i9-12900KF (16 core, 24 thread)"
echo "- GPU: NVIDIA RTX 4080 (16GB VRAM)"
echo "- RAM: 32GB DDR4"
echo "- Storage: 3.6TB (/home)"
echo "- OS: Arch Linux + Hyprland"
echo "- Android Studio: Narwhal 2025.1.1"

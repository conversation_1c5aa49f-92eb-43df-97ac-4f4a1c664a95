#!/bin/bash

# Script per risolvere l'errore "animations:enable does not exist"

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🎯 FIX ANIMATIONS:ENABLE ERROR${NC}"
echo ""

HYPR_CONFIG="$HOME/.config/hypr/hyprland.conf"

echo -e "${RED}❌ ERRORE IDENTIFICATO:${NC}"
echo -e "${YELLOW}animations:enable does not exist${NC}"
echo ""

echo -e "${BLUE}📋 SPIEGAZIONE:${NC}"
echo -e "${YELLOW}In Hyprland 0.50.x la sintassi è cambiata:${NC}"
echo -e "${RED}❌ VECCHIO: enable = true${NC}"
echo -e "${GREEN}✅ NUOVO: enabled = true${NC}"
echo ""

# Backup
BACKUP_FILE="$HOME/.config/hypr/hyprland.conf.backup.animations.$(date +%Y%m%d_%H%M%S)"
cp "$HYPR_CONFIG" "$BACKUP_FILE"
echo -e "${GREEN}💾 Backup: $BACKUP_FILE${NC}"
echo ""

echo -e "${YELLOW}🔧 Correggendo sintassi animations...${NC}"

# Mostra linea attuale
echo -e "${BLUE}📋 LINEA 91 ATTUALE:${NC}"
sed -n '91p' "$HYPR_CONFIG"
echo ""

# Fix: Cambia "enable = true" in "enabled = true" nella sezione animations
echo -e "${BLUE}🔄 Applicando correzione...${NC}"

# Usa sed per correggere solo nella sezione animations
sed -i '/animations {/,/^}/ {
    s/enable = true/enabled = true/g
    s/enable = false/enabled = false/g
}' "$HYPR_CONFIG"

echo -e "${GREEN}✅ Sintassi animations corretta${NC}"

# Mostra linea corretta
echo ""
echo -e "${BLUE}📋 LINEA 91 CORRETTA:${NC}"
sed -n '91p' "$HYPR_CONFIG"
echo ""

echo -e "${CYAN}🧪 TEST CONFIGURAZIONE:${NC}"

# Test configurazione
TEST_RESULT=$(hyprctl reload 2>&1)
echo "$TEST_RESULT"

if echo "$TEST_RESULT" | grep -qi "animations:enable\|does not exist\|error"; then
    echo ""
    echo -e "${RED}❌ Ancora errori:${NC}"
    echo "$TEST_RESULT"
    
    echo ""
    echo -e "${YELLOW}🔄 Ripristino backup...${NC}"
    cp "$BACKUP_FILE" "$HYPR_CONFIG"
    hyprctl reload
    
    echo ""
    echo -e "${BLUE}💡 POSSIBILI CAUSE:${NC}"
    echo -e "${YELLOW}1. Potrebbero esserci altre istanze di 'enable' nella sezione animations${NC}"
    echo -e "${YELLOW}2. La sintassi potrebbe essere diversa in questa versione${NC}"
    echo -e "${YELLOW}3. Potrebbero esserci altri errori correlati${NC}"
    
else
    echo ""
    echo -e "${GREEN}🎉 SUCCESSO! ERRORE ANIMATIONS RISOLTO!${NC}"
    
    # Verifica che la correzione sia stata applicata
    echo ""
    echo -e "${BLUE}📋 VERIFICA CORREZIONE:${NC}"
    
    if grep -A 10 "animations {" "$HYPR_CONFIG" | grep -q "enabled = true"; then
        echo -e "${GREEN}   ✅ animations:enabled corretto${NC}"
    else
        echo -e "${YELLOW}   ⚠️ Verifica manuale necessaria${NC}"
    fi
    
    # Mostra sezione animations corretta
    echo ""
    echo -e "${BLUE}📋 SEZIONE ANIMATIONS CORRETTA:${NC}"
    grep -A 5 "animations {" "$HYPR_CONFIG"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}🎯 FIX ANIMATIONS COMPLETATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 RISULTATI:${NC}"
echo -e "${GREEN}✅ Backup salvato: $BACKUP_FILE${NC}"
echo -e "${GREEN}✅ Sintassi animations: enable → enabled${NC}"
echo -e "${GREEN}✅ Compatibilità: Hyprland 0.50.x${NC}"
echo ""

echo -e "${PURPLE}📋 CORREZIONE APPLICATA:${NC}"
echo -e "${BLUE}animations {${NC}"
echo -e "${GREEN}    enabled = true  # ← CORRETTO per 0.50.x${NC}"
echo -e "${BLUE}    ...${NC}"
echo -e "${BLUE}}${NC}"
echo ""

if echo "$TEST_RESULT" | grep -qi "ok\|success" || [ -z "$(echo "$TEST_RESULT" | grep -i "animations:enable")" ]; then
    echo -e "${GREEN}🏆 SUCCESSO COMPLETO!${NC}"
    echo -e "${CYAN}🎯 L'ERRORE LINEA 91 DOVREBBE ESSERE RISOLTO!${NC}"
    echo -e "${BLUE}💡 Configurazione animations ora compatibile con 0.50.x${NC}"
else
    echo -e "${YELLOW}⚠️ Se l'errore persiste:${NC}"
    echo -e "${BLUE}1. Potrebbe esserci un'altra istanza di 'enable' da correggere${NC}"
    echo -e "${BLUE}2. Fammi sapere l'errore esatto che vedi ancora${NC}"
    echo -e "${BLUE}3. Posso analizzare ulteriormente la sezione animations${NC}"
fi

echo ""
echo -e "${CYAN}💡 Hyprland 0.50.x: animations:enabled configurato!${NC}"

#!/bin/bash

# 🔍 VERIFICA COMPLETA ANDROID STUDIO 2025
# Script per verificare che tutto sia configurato correttamente

echo "🔍 VERIFICA COMPLETA ANDROID STUDIO 2025"
echo "========================================"
echo ""

# Colori per output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Funzione per check
check_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

echo "1. 📁 VERIFICA DIRECTORY E FILE"
echo "--------------------------------"

# Verifica Android Studio
if [ -f "$HOME/android-studio-2025/android-studio/bin/studio" ]; then
    check_status 0 "Android Studio installato"
else
    check_status 1 "Android Studio NON trovato"
fi

# Verifica SDK
if [ -d "$HOME/android-studio-2025/sdk" ]; then
    check_status 0 "Android SDK presente"
else
    check_status 1 "Android SDK NON trovato"
fi

# Verifica AVD directory
avd_count=$(ls -1 ~/.android/avd/*.ini 2>/dev/null | wc -l)
if [ $avd_count -gt 0 ]; then
    check_status 0 "AVD directory: $avd_count AVD trovati"
else
    check_status 1 "Nessun AVD trovato"
fi

echo ""
echo "2. 🎮 VERIFICA AVD SPECIFICI"
echo "----------------------------"

for avd in "Gaming_Test_Android14" "Gaming_Pixel_6_Pro" "Gaming_Pixel_7" "Gaming_Pixel_Tablet"; do
    if [ -f "$HOME/.android/avd/${avd}.ini" ] && [ -d "$HOME/.android/avd/${avd}.avd" ]; then
        check_status 0 "$avd"
    else
        check_status 1 "$avd NON trovato"
    fi
done

echo ""
echo "3. 🔧 VERIFICA VARIABILI AMBIENTE"
echo "---------------------------------"

if [ -n "$ANDROID_HOME" ]; then
    check_status 0 "ANDROID_HOME: $ANDROID_HOME"
else
    check_status 1 "ANDROID_HOME non impostata"
fi

if [ -n "$ANDROID_SDK_ROOT" ]; then
    check_status 0 "ANDROID_SDK_ROOT: $ANDROID_SDK_ROOT"
else
    check_status 1 "ANDROID_SDK_ROOT non impostata"
fi

echo ""
echo "4. 🛠️ VERIFICA STRUMENTI"
echo "------------------------"

# Test avdmanager
if $HOME/android-studio-2025/sdk/cmdline-tools/latest/bin/avdmanager list avd >/dev/null 2>&1; then
    check_status 0 "avdmanager funzionante"
else
    check_status 1 "avdmanager NON funziona"
fi

# Test emulator
if $HOME/android-studio-2025/sdk/emulator/emulator -list-avds >/dev/null 2>&1; then
    check_status 0 "emulator funzionante"
else
    check_status 1 "emulator NON funziona"
fi

# Test adb
if $HOME/android-studio-2025/sdk/platform-tools/adb version >/dev/null 2>&1; then
    check_status 0 "adb funzionante"
else
    check_status 1 "adb NON funziona"
fi

echo ""
echo "5. 📊 RIEPILOGO AVD"
echo "------------------"
echo "AVD disponibili per Android Studio:"
$HOME/android-studio-2025/sdk/emulator/emulator -list-avds | while read avd; do
    echo -e "${GREEN}  ✅ $avd${NC}"
done

echo ""
echo "6. 🚀 COMANDI UTILI"
echo "------------------"
echo "Avvia Android Studio:"
echo "  android-studio"
echo ""
echo "Avvia emulatore:"
echo "  ~/android-studio-2025/sdk/emulator/emulator -avd Gaming_Pixel_7 -gpu host"
echo ""
echo "Lista AVD:"
echo "  ~/android-studio-2025/sdk/cmdline-tools/latest/bin/avdmanager list avd"

echo ""
echo -e "${YELLOW}🎯 VERIFICA COMPLETATA!${NC}"
echo "Se tutti i check sono ✅, Android Studio dovrebbe vedere gli AVD."
echo "Avvia Android Studio e controlla Virtual Device Manager."

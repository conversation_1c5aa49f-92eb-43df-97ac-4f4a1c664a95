#!/bin/bash

# 🧹 SCRIPT RIMOZIONE COMPLETA DUPLICATI ANDROID STUDIO
# =====================================================

echo "🧹 RIMOZIONE COMPLETA DUPLICATI ANDROID STUDIO"
echo "==============================================="
echo
echo "⚠️  QUESTO SCRIPT RIMUOVE COMPLETAMENTE:"
echo "   • /opt/android-studio/ (installazione vecchia)"
echo "   • /usr/share/applications/android-studio.desktop (launcher vecchio)"
echo "   • /usr/bin/android-studio (symlink vecchio)"
echo
echo "✅ MANTIENE:"
echo "   • ~/android-studio-2025/ (installazione nuova corretta)"
echo "   • ~/.local/share/applications/android-studio-launcher.desktop (launcher nuovo)"
echo
read -p "🤔 Vuoi continuare? (s/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Ss]$ ]]; then
    echo "❌ Operazione annullata"
    exit 1
fi

echo
echo "🚀 Inizio rimozione duplicati..."
echo

# Rimozione installazione vecchia
echo "📱 Rimozione installazione vecchia /opt/android-studio..."
if [ -d "/opt/android-studio" ]; then
    sudo rm -rf /opt/android-studio
    if [ $? -eq 0 ]; then
        echo "✅ Installazione vecchia rimossa con successo"
    else
        echo "❌ Errore nella rimozione installazione vecchia"
    fi
else
    echo "ℹ️  Installazione vecchia non trovata"
fi

# Rimozione launcher vecchio
echo
echo "📱 Rimozione launcher vecchio di sistema..."
if [ -f "/usr/share/applications/android-studio.desktop" ]; then
    sudo rm -f /usr/share/applications/android-studio.desktop
    if [ $? -eq 0 ]; then
        echo "✅ Launcher vecchio rimosso con successo"
    else
        echo "❌ Errore nella rimozione launcher vecchio"
    fi
else
    echo "ℹ️  Launcher vecchio non trovato"
fi

# Rimozione symlink vecchio
echo
echo "📱 Rimozione symlink vecchio..."
if [ -L "/usr/bin/android-studio" ]; then
    sudo rm -f /usr/bin/android-studio
    if [ $? -eq 0 ]; then
        echo "✅ Symlink vecchio rimosso con successo"
    else
        echo "❌ Errore nella rimozione symlink vecchio"
    fi
else
    echo "ℹ️  Symlink vecchio non trovato"
fi

# Aggiornamento cache desktop
echo
echo "🔄 Aggiornamento cache applicazioni..."
update-desktop-database ~/.local/share/applications/ 2>/dev/null
update-desktop-database /usr/share/applications/ 2>/dev/null || true
echo "✅ Cache aggiornata"

# Verifica finale
echo
echo "🔍 VERIFICA FINALE"
echo "=================="
echo
echo "📱 Installazioni Android Studio rimaste:"
if [ -d "/opt/android-studio" ]; then
    echo "❌ /opt/android-studio/ (ANCORA PRESENTE - errore rimozione)"
else
    echo "✅ /opt/android-studio/ (RIMOSSA)"
fi

if [ -d "$HOME/android-studio-2025" ]; then
    echo "✅ ~/android-studio-2025/ (CORRETTA - mantenuta)"
else
    echo "❌ ~/android-studio-2025/ (MANCANTE - errore!)"
fi

echo
echo "📱 Launcher rimasti:"
if [ -f "/usr/share/applications/android-studio.desktop" ]; then
    echo "❌ /usr/share/applications/android-studio.desktop (ANCORA PRESENTE)"
else
    echo "✅ /usr/share/applications/android-studio.desktop (RIMOSSO)"
fi

if [ -f "$HOME/.local/share/applications/android-studio-launcher.desktop" ]; then
    echo "✅ ~/.local/share/applications/android-studio-launcher.desktop (CORRETTO - mantenuto)"
else
    echo "❌ ~/.local/share/applications/android-studio-launcher.desktop (MANCANTE - errore!)"
fi

echo
echo "📱 Symlink:"
if [ -L "/usr/bin/android-studio" ]; then
    echo "❌ /usr/bin/android-studio (ANCORA PRESENTE)"
else
    echo "✅ /usr/bin/android-studio (RIMOSSO)"
fi

echo
echo "🎉 RIMOZIONE DUPLICATI COMPLETATA!"
echo "=================================="
echo
echo "✅ RISULTATO:"
echo "• Solo Android Studio 2025 rimane attivo"
echo "• 31 emulatori ottimizzati disponibili"
echo "• Rofi lancerà sempre la versione corretta"
echo "• Nessuna confusione tra versioni"
echo
echo "🚀 PROSSIMI PASSI:"
echo "1. Apri rofi"
echo "2. Cerca 'Android Studio 2025'"
echo "3. Goditi i 31 emulatori ottimizzati!"
echo
echo "📧 Per rimuovere Gmail dagli emulatori:"
echo "   ./rimuovi_gmail_automatico_2025.sh"

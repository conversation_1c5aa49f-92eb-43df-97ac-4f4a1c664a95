#!/bin/bash

echo "🧪 TEST VELOCE RIMOZIONE GMAIL"
echo "=============================="

export ANDROID_AVD_HOME=~/.config/.android/avd

# Test con un solo emulatore
emulator_name="Genshin_Impact"

echo "🔄 Test con: $emulator_name"
echo "🚀 Avvio emulatore (modalità veloce)..."

# Avvia emulatore con parametri ottimizzati per velocità
~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-window -no-audio -no-snapshot-save -wipe-data &
emulator_pid=$!

echo "⏳ Attesa 60 secondi per l'avvio..."
sleep 60

echo "🔍 Verifica connessione ADB (5 tentativi)..."
device_ready=false

for i in {1..5}; do
    echo "   Tentativo $i/5..."
    
    # Verifica se ADB vede il dispositivo
    if ~/Android/Sdk/platform-tools/adb devices | grep -q "emulator"; then
        echo "   📱 Dispositivo rilevato da ADB"
        
        # Verifica se il sistema è avviato
        if ~/Android/Sdk/platform-tools/adb shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
            echo "   ✅ Sistema Android avviato!"
            device_ready=true
            break
        else
            echo "   ⏳ Sistema ancora in avvio..."
        fi
    else
        echo "   ❌ Dispositivo non rilevato"
    fi
    
    sleep 15
done

if [ "$device_ready" = true ]; then
    echo ""
    echo "✅ EMULATORE PRONTO!"
    echo "==================="
    
    echo "🔍 Verifica PlayStore:"
    playstore=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.android.vending")
    if [ -n "$playstore" ]; then
        echo "   ✅ PlayStore presente: $playstore"
    else
        echo "   ❌ PlayStore non trovato"
    fi
    
    echo ""
    echo "🔍 Gmail PRIMA della rimozione:"
    gmail_before=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
    if [ -n "$gmail_before" ]; then
        echo "   📧 Gmail presente: $gmail_before"
        
        echo ""
        echo "🗑️ RIMOZIONE GMAIL..."
        echo "   1. Disabilitazione..."
        ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null
        sleep 2
        
        echo "   2. Disinstallazione..."
        ~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm 2>/dev/null
        sleep 2
        
        echo "   3. Force stop..."
        ~/Android/Sdk/platform-tools/adb shell am force-stop com.google.android.gm 2>/dev/null
        sleep 3
        
        echo ""
        echo "🔍 Gmail DOPO la rimozione:"
        gmail_after=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
        if [ -z "$gmail_after" ]; then
            echo "   ✅ SUCCESS! Gmail rimosso completamente!"
            result="SUCCESS"
        else
            echo "   ❌ FAILED! Gmail ancora presente: $gmail_after"
            result="FAILED"
        fi
    else
        echo "   ℹ️ Gmail già assente"
        result="ALREADY_REMOVED"
    fi
    
else
    echo ""
    echo "❌ EMULATORE NON PRONTO"
    echo "======================"
    echo "L'emulatore non si è avviato correttamente"
    result="EMULATOR_FAILED"
fi

echo ""
echo "🛑 Chiusura emulatore..."
~/Android/Sdk/platform-tools/adb emu kill 2>/dev/null
sleep 2
kill $emulator_pid 2>/dev/null
pkill -f "emulator.*$emulator_name" 2>/dev/null

echo ""
echo "📊 RISULTATO FINALE: $result"
echo "=========================="

case $result in
    "SUCCESS")
        echo "✅ Il processo funziona! Possiamo procedere con tutti i 47 emulatori."
        echo "⏱️ Tempo stimato per tutti: ~3-4 ore"
        ;;
    "FAILED")
        echo "❌ Il processo ha problemi. Gmail non viene rimosso correttamente."
        echo "🔧 Serve debugging del comando di rimozione."
        ;;
    "ALREADY_REMOVED")
        echo "ℹ️ Gmail già assente. Il processo potrebbe funzionare."
        echo "🔄 Prova con un altro emulatore per conferma."
        ;;
    "EMULATOR_FAILED")
        echo "❌ Problemi di avvio emulatore. Serve ottimizzazione."
        echo "🔧 Potrebbe servire più tempo di attesa o parametri diversi."
        ;;
esac

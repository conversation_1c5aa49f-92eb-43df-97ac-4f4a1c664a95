#!/bin/bash

echo "🚀 CREAZIONE EMULATORI PULITI 2025 - SENZA GMAIL"
echo "=================================================="
echo "🎯 Strategia: Google APIs System Image + Play Store manuale"
echo "✅ Risultato: NESSUN Gmail, Play Store funzionante"
echo "⚡ Ottimizzato per: Android 14 + Gaming"
echo ""

# Configurazione
export ANDROID_AVD_HOME=~/.config/.android/avd
export ANDROID_SDK_ROOT=~/Android/Sdk

# Lista emulatori (47 giochi)
emulators=(
    "Ace_Racer" "Aether_Gazer" "Arknights" "Ash_Echoes" "ASTRA_Knights_of_Veda"
    "Black_Beacon" "Blood_Strike" "Brown_Dust_2" "Cat_Fantasy_Isekai_Adventure"
    "CookieRun_Kingdom" "CookieRun_OvenBreak" "CookieRun_Tower_of_Adventures"
    "CounterSide" "DanMachi_BATTLE_CHRONICLE" "Dislyte" "Echocalypse_Scarlet_Covenant"
    "Epic_Seven" "Etheria_Restart" "Eversoul" "Farlight_84" "Figure_Fantasy"
    "Genshin_Impact" "Girls_Frontline_2_Exilium" "Go_Go_Muffin" "Heaven_Burns_Red"
    "Higan_Eruthyll" "Honkai_Star_Rail" "Infinity_Nikki" "Jujutsu_Kaisen_Phantom_Parade"
    "MementoMori_AFKRPG" "Metal_Slug_Awakening" "Neural_Cloud" "NIKKE_Goddess_of_Victory"
    "Ni_no_Kuni_Cross_Worlds" "OUTERPLANE_Strategy_Anime" "Path_to_Nowhere"
    "Phantom_Blade_Executioners" "Punishing_Gray_Raven" "Reverse_1999"
    "Snowbreak_Containment_Zone" "Solo_Leveling_Arise" "STARSEED_Asnia_Trigger"
    "Tower_of_God_Great_Journey" "Tower_of_God_NEW_WORLD" "Uma_Musume_Pretty_Derby"
    "Wuthering_Waves" "Zenless_Zone_Zero"
)

total_emulators=${#emulators[@]}
success=0
failed=0

echo "📱 Emulatori da creare: $total_emulators"
echo "🔧 System Image: Google APIs (Android 14)"
echo "📦 Caratteristiche:"
echo "   ❌ Gmail: ASSENTE"
echo "   ✅ Google Play Services: PRESENTE"
echo "   ⚡ Play Store: Installazione manuale"
echo "   🎮 Ottimizzato per gaming"
echo ""

# Verifica system image Google APIs
echo "🔍 Verifica system image Google APIs..."
if [ ! -d "~/Android/Sdk/system-images/android-34/google_apis/x86_64" ]; then
    echo "⚠️  System image Google APIs non trovato. Installazione..."
    ~/Android/Sdk/cmdline-tools/latest/bin/sdkmanager "system-images;android-34;google_apis;x86_64"
    if [ $? -ne 0 ]; then
        echo "❌ Errore installazione system image Google APIs"
        exit 1
    fi
fi

echo "✅ System image Google APIs verificato"
echo ""

# Pulizia emulatori esistenti
echo "🧹 Rimozione emulatori esistenti..."
for emulator_name in "${emulators[@]}"; do
    if [ -d "${emulator_name}.avd" ]; then
        echo "🗑️  Rimozione: $emulator_name"
        rm -rf "${emulator_name}.avd"
        rm -f "${emulator_name}.ini"
    fi
done

echo "✅ Pulizia completata"
echo ""

# Creazione emulatori con Google APIs
echo "🚀 INIZIO CREAZIONE EMULATORI PULITI"
echo "===================================="

start_time=$(date +%s)

for i in "${!emulators[@]}"; do
    emulator_name="${emulators[$i]}"
    emulator_num=$((i + 1))

    echo ""
    echo "🎮 [$emulator_num/$total_emulators] Creazione: $emulator_name"
    echo "⏰ $(date +%H:%M:%S)"

    # Creazione AVD con Google APIs (SENZA Gmail)
    echo "no" | ~/Android/Sdk/cmdline-tools/latest/bin/avdmanager create avd \
        -n "$emulator_name" \
        -k "system-images;android-34;google_apis;x86_64" \
        -d "pixel_4" \
        -f

    if [ $? -eq 0 ]; then
        echo "✅ AVD creato: $emulator_name"

        # Configurazione ottimizzata per gaming
        config_file="$ANDROID_AVD_HOME/${emulator_name}.avd/config.ini"

        if [ -f "$config_file" ]; then
            # Backup configurazione originale
            cp "$config_file" "${config_file}.backup"

            # Configurazioni gaming ottimizzate
            cat >> "$config_file" << EOF

# === CONFIGURAZIONI GAMING OTTIMIZZATE 2025 ===
# RAM e Performance
hw.ramSize=6144
vm.heapSize=512

# CPU e GPU
hw.cpu.ncore=4
hw.gpu.enabled=yes
hw.gpu.mode=swiftshader_indirect

# Display ottimizzato
hw.lcd.width=1440
hw.lcd.height=3120
hw.lcd.density=560

# Audio e Input
hw.audioInput=yes
hw.audioOutput=yes
hw.keyboard=yes
hw.dPad=no
hw.trackBall=no

# Sensori gaming
hw.accelerometer=yes
hw.gyroscope=yes
hw.gps=yes

# Storage ottimizzato
disk.dataPartition.size=8G
hw.sdCard=yes
sdcard.size=2G

# Network
hw.wifi=yes
hw.camera.back=none
hw.camera.front=none

# Performance
hw.mainKeys=no
hw.arc=false
fastboot.chosenSnapshotFile=
fastboot.forceChosenSnapshotBoot=no
fastboot.forceColdBoot=no
fastboot.forceFastBoot=yes

# Display name pulito
avd.ini.displayname=$emulator_name

# Google APIs (SENZA Gmail)
PlayStore.enabled=no
image.sysdir.1=system-images/android-34/google_apis/x86_64/
tag.display=Google APIs
tag.id=google_apis
EOF

            echo "✅ Configurazione gaming applicata"
            ((success++))
        else
            echo "⚠️  File config.ini non trovato"
            ((failed++))
        fi
    else
        echo "❌ Errore creazione AVD: $emulator_name"
        ((failed++))
    fi

    # Progress
    progress=$((emulator_num * 100 / total_emulators))
    echo "📊 Progresso: $progress% ($success successi, $failed errori)"
done

# Statistiche finali
end_time=$(date +%s)
duration=$((end_time - start_time))
minutes=$((duration / 60))
seconds=$((duration % 60))

echo ""
echo "🎉 CREAZIONE COMPLETATA!"
echo "========================"
echo "✅ Successi: $success/$total_emulators"
echo "❌ Errori: $failed/$total_emulators"
echo "⏱️  Tempo totale: ${minutes}m ${seconds}s"
echo ""
echo "📋 CARATTERISTICHE EMULATORI CREATI:"
echo "   🎯 System Image: Google APIs (Android 14)"
echo "   ❌ Gmail: COMPLETAMENTE ASSENTE"
echo "   ✅ Google Play Services: PRESENTE"
echo "   🎮 RAM: 6GB, CPU: 4 core, GPU: Ottimizzata"
echo "   📱 Risoluzione: 1440x3120 (gaming ottimale)"
echo ""
echo "🔄 PROSSIMO PASSO: Installazione manuale Play Store"
echo "   Comando: bash install_playstore_manual.sh"
echo ""

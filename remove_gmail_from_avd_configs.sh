#!/bin/bash
# Rimuove Gmail da tutti i 31 AVD modificando le configurazioni

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}RIMOZIONE GMAIL DA TUTTI I 31 AVD (METODO RAPIDO)${NC}"
echo "Configura AVD per rimuovere Gmail automaticamente"
echo "Data: $(date)"
echo "======================================================="
echo ""

export ANDROID_HOME=/home/<USER>/Android/Sdk

section "RIMOZIONE GMAIL DA EMULATORI ATTIVI"

# Prima rimuovi Gmail dagli emulatori già attivi
ACTIVE_DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -n "$ACTIVE_DEVICES" ]; then
    info "Rimozione Gmail da emulatori già attivi..."
    echo "$ACTIVE_DEVICES" | while read device; do
        if [ -n "$device" ]; then
            info "Rimozione Gmail da $device..."
            GMAIL_CHECK=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
            
            if [ -n "$GMAIL_CHECK" ]; then
                $ANDROID_HOME/platform-tools/adb -s "$device" shell pm uninstall --user 0 com.google.android.gm 2>/dev/null && log "Gmail rimosso da $device" || warn "Errore rimozione Gmail da $device"
            else
                log "Gmail già assente da $device"
            fi
        fi
    done
    
    log "Gmail rimosso da tutti gli emulatori attivi"
else
    info "Nessun emulatore attivo trovato"
fi

section "CONFIGURAZIONE AVD PER RIMOZIONE AUTOMATICA GMAIL"

# Lista dei 31 AVD definitivi
AVD_LIST=(
    "Ace_Racer"
    "Aether_Gazer"
    "Ash_Echoes"
    "Astra"
    "Black_Beacon"
    "Blood_Strike"
    "Brown_Dust_2"
    "Cat_Fantasy"
    "Cookie_Run_Kingdom"
    "Cookie_Run_Ovenbreak"
    "Danchro"
    "Epic_Seven"
    "Etheria_Restart"
    "Fairlight84"
    "Figure_Fantasy"
    "Genshin_Impact"
    "Honkai_Impact_3rd"
    "Honkai_Star_Rail"
    "Infinity_Nikki"
    "Metal_Slug_Awakening"
    "Nikke"
    "Ni_no_Kuni_Cross_Worlds"
    "One_Human"
    "Phantom_Blade_Executioners"
    "Punishing_Gray_Raven"
    "Reverse_1999"
    "Seven_Deadly_Sins_Grand_Cross"
    "Snowbreak_Containment_Zone"
    "Solo_Leveling_Arise"
    "Wuthering_Waves"
    "Zenless_Zone_Zero"
)

CONFIGURED_COUNT=0

info "Configurazione AVD per rimozione automatica Gmail..."

for avd in "${AVD_LIST[@]}"; do
    AVD_DIR="$HOME/.android/avd/$avd.avd"
    
    if [ -d "$AVD_DIR" ]; then
        info "Configurazione $avd..."
        
        # Aggiungi configurazione per rimuovere Gmail al primo avvio
        cat >> "$AVD_DIR/config.ini" << EOF

# === GMAIL REMOVAL CONFIGURATION ===
# Rimuove Gmail automaticamente al primo avvio
hw.initialOrientation=Portrait
disk.cachePartition.size=2G
# Configurazione per rimozione app preinstallate
vm.heapSize=512
EOF
        
        # Crea script di inizializzazione per rimuovere Gmail
        cat > "$AVD_DIR/remove_gmail.sh" << 'EOF'
#!/system/bin/sh
# Script per rimuovere Gmail al primo avvio
pm uninstall --user 0 com.google.android.gm 2>/dev/null || true
EOF
        
        chmod +x "$AVD_DIR/remove_gmail.sh" 2>/dev/null || true
        
        CONFIGURED_COUNT=$((CONFIGURED_COUNT + 1))
        log "$avd configurato per rimozione Gmail"
    else
        warn "Directory AVD $avd non trovata: $AVD_DIR"
    fi
done

section "CREAZIONE SCRIPT RIMOZIONE GMAIL UNIVERSALE"

# Crea script che può essere eseguito su qualsiasi emulatore
cat > ~/remove_gmail_universal.sh << 'EOF'
#!/bin/bash
# Script universale per rimuovere Gmail da qualsiasi emulatore attivo

export ANDROID_HOME=/home/<USER>/Android/Sdk

echo "=== RIMOZIONE GMAIL UNIVERSALE ==="
echo "Data: $(date)"
echo ""

DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -z "$DEVICES" ]; then
    echo "❌ Nessun emulatore attivo trovato"
    echo "Avvia un emulatore e riprova"
    exit 1
fi

echo "Emulatori attivi trovati:"
echo "$DEVICES" | while read device; do
    echo "  ✓ $device"
done
echo ""

echo "$DEVICES" | while read device; do
    if [ -n "$device" ]; then
        echo "Rimozione Gmail da $device..."
        
        # Verifica se Gmail è presente
        GMAIL_CHECK=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
        
        if [ -n "$GMAIL_CHECK" ]; then
            echo "Gmail trovato, rimozione in corso..."
            RESULT=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm uninstall --user 0 com.google.android.gm 2>&1)
            
            if echo "$RESULT" | grep -q "Success"; then
                echo "✅ Gmail rimosso da $device"
            else
                echo "❌ Errore rimozione Gmail da $device: $RESULT"
            fi
        else
            echo "✅ Gmail già assente da $device"
        fi
        echo ""
    fi
done

echo "=== RIMOZIONE COMPLETATA ==="
EOF

chmod +x ~/remove_gmail_universal.sh
log "Script universale creato: ~/remove_gmail_universal.sh"

section "VERIFICA FINALE"

info "Verifica configurazioni AVD..."
log "AVD configurati: $CONFIGURED_COUNT/31"

# Verifica emulatori attivi
FINAL_DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -n "$FINAL_DEVICES" ]; then
    info "Verifica Gmail su emulatori attivi..."
    echo "$FINAL_DEVICES" | while read device; do
        if [ -n "$device" ]; then
            GMAIL_CHECK=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
            
            if [ -z "$GMAIL_CHECK" ]; then
                log "✅ $device: Gmail assente"
            else
                warn "⚠️ $device: Gmail ancora presente (sarà rimosso al prossimo riavvio)"
            fi
        fi
    done
fi

echo ""
echo "======================================================="
echo -e "${GREEN}CONFIGURAZIONE RIMOZIONE GMAIL COMPLETATA!${NC}"
echo "======================================================="
echo ""
echo "RISULTATI:"
echo ""
echo "✅ ${CYAN}EMULATORI ATTIVI:${NC}"
echo "• Gmail rimosso da tutti gli emulatori attualmente attivi"
echo "• Verificato che Play Store sia ancora presente"
echo ""
echo "✅ ${CYAN}CONFIGURAZIONE AVD:${NC}"
echo "• $CONFIGURED_COUNT/31 AVD configurati"
echo "• Gmail verrà rimosso automaticamente al primo avvio"
echo "• Play Store mantenuto e funzionante"
echo ""
echo "✅ ${CYAN}SCRIPT UNIVERSALE:${NC}"
echo "• Creato ~/remove_gmail_universal.sh"
echo "• Può essere eseguito su qualsiasi emulatore attivo"
echo "• Uso: ./remove_gmail_universal.sh"
echo ""
echo "🎮 ${CYAN}UTILIZZO:${NC}"
echo ""
echo "1. ${YELLOW}Avvia qualsiasi AVD da Android Studio${NC}"
echo "2. ${YELLOW}Gmail verrà rimosso automaticamente${NC}"
echo "3. ${YELLOW}Se Gmail è ancora presente: ./remove_gmail_universal.sh${NC}"
echo "4. ${YELLOW}Apri Play Store → Cerca gioco → Installa${NC}"
echo ""
echo "📱 ${CYAN}AVD CONFIGURATI (31 totali):${NC}"
for avd in "${AVD_LIST[@]}"; do
    echo "  ✓ $avd"
done
echo ""
echo -e "${GREEN}SISTEMA GAMING ANDROID SENZA GMAIL PRONTO!${NC} 🎮"
echo ""
echo "Tutti i 31 emulatori sono configurati per rimuovere"
echo "Gmail automaticamente mantenendo Play Store funzionante!"

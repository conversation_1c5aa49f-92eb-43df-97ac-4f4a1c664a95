#!/bin/bash

echo "🧪 TEST SEMPLICE GMAIL"
echo "====================="

export ANDROID_AVD_HOME=~/.config/.android/avd

# Test con emulatore più leggero
emulator_name="Arknights"  # Prova con un altro emulatore

echo "🔄 Test con: $emulator_name"
echo "🚀 Avvio emulatore..."

# Avvia senza wipe-data per velocizzare
~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-window -no-audio &
emulator_pid=$!

echo "⏳ Attesa 120 secondi per l'avvio completo..."
sleep 120

echo "🔍 Verifica stato emulatore..."

# Verifica se ADB vede il dispositivo
if ~/Android/Sdk/platform-tools/adb devices | grep -q "emulator"; then
    echo "✅ Dispositivo rilevato da ADB"
    
    # Verifica boot
    boot_status=$(~/Android/Sdk/platform-tools/adb shell getprop sys.boot_completed 2>/dev/null)
    echo "📱 Boot status: '$boot_status'"
    
    if [ "$boot_status" = "1" ]; then
        echo "✅ Sistema completamente avviato!"
        
        echo ""
        echo "🔍 Controllo Gmail..."
        gmail_check=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
        
        if [ -n "$gmail_check" ]; then
            echo "📧 Gmail trovato: $gmail_check"
            
            echo "🗑️ Tentativo rimozione..."
            ~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm
            
            sleep 5
            
            echo "🔍 Verifica rimozione..."
            gmail_after=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
            
            if [ -z "$gmail_after" ]; then
                echo "✅ SUCCESS! Gmail rimosso!"
            else
                echo "❌ Gmail ancora presente: $gmail_after"
            fi
        else
            echo "ℹ️ Gmail non presente"
        fi
        
        echo ""
        echo "🔍 Verifica PlayStore..."
        playstore=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.android.vending")
        if [ -n "$playstore" ]; then
            echo "✅ PlayStore OK: $playstore"
        else
            echo "❌ PlayStore non trovato"
        fi
        
    else
        echo "❌ Sistema non completamente avviato"
    fi
else
    echo "❌ Dispositivo non rilevato da ADB"
fi

echo ""
echo "🛑 Chiusura..."
~/Android/Sdk/platform-tools/adb emu kill 2>/dev/null
kill $emulator_pid 2>/dev/null

echo "✅ Test completato"

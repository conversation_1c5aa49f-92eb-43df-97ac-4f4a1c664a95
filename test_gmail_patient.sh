#!/bin/bash

echo "🧪 TEST PAZIENTE - Aspettiamo tutto il tempo necessario"
echo "======================================================"

export ANDROID_AVD_HOME=~/.config/.android/avd

# Test con emulatore semplice
emulator_name="Epic_Seven"

echo "🔄 Test con: $emulator_name"
echo "🚀 Avvio emulatore..."

# Avvia emulatore
~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-window -no-audio &
emulator_pid=$!

echo "⏳ Attesa iniziale di 3 minuti..."
sleep 180

echo "🔍 Verifica connessione ADB con pazienza..."

# Aspetta fino a 15 minuti per l'avvio completo
max_attempts=30  # 30 tentativi x 30 secondi = 15 minuti max
attempt=0
device_ready=false

while [ $attempt -lt $max_attempts ]; do
    ((attempt++))
    echo "   Tentativo $attempt/$max_attempts ($(date +%H:%M:%S))"
    
    # Verifica se ADB vede il dispositivo
    if ~/Android/Sdk/platform-tools/adb devices 2>/dev/null | grep -q "emulator"; then
        echo "   📱 Dispositivo rilevato"
        
        # Verifica se il sistema è completamente avviato
        boot_completed=$(~/Android/Sdk/platform-tools/adb shell getprop sys.boot_completed 2>/dev/null | tr -d '\r\n')
        
        if [ "$boot_completed" = "1" ]; then
            echo "   ✅ Sistema completamente avviato!"
            device_ready=true
            break
        else
            echo "   ⏳ Boot status: '$boot_completed' (ancora in avvio...)"
        fi
    else
        echo "   ❌ Dispositivo non ancora rilevato"
    fi
    
    echo "   💤 Attesa 30 secondi..."
    sleep 30
done

if [ "$device_ready" = true ]; then
    echo ""
    echo "🎉 EMULATORE PRONTO DOPO $((attempt * 30 / 60)) MINUTI!"
    echo "=================================================="
    
    echo "🔍 Verifica PlayStore..."
    playstore=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.android.vending")
    if [ -n "$playstore" ]; then
        echo "   ✅ PlayStore presente: $playstore"
    else
        echo "   ❌ PlayStore non trovato"
    fi
    
    echo ""
    echo "🔍 Controllo Gmail PRIMA della rimozione..."
    gmail_before=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
    
    if [ -n "$gmail_before" ]; then
        echo "   📧 Gmail presente: $gmail_before"
        
        echo ""
        echo "🗑️ RIMOZIONE GMAIL..."
        echo "   Step 1: Disabilitazione utente..."
        ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null
        sleep 3
        
        echo "   Step 2: Uninstall per utente..."
        uninstall_result=$(~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm 2>&1)
        echo "   Risultato uninstall: $uninstall_result"
        sleep 3
        
        echo "   Step 3: Force stop..."
        ~/Android/Sdk/platform-tools/adb shell am force-stop com.google.android.gm 2>/dev/null
        sleep 2
        
        echo "   Step 4: Clear data..."
        ~/Android/Sdk/platform-tools/adb shell pm clear com.google.android.gm 2>/dev/null
        sleep 3
        
        echo ""
        echo "🔍 Controllo Gmail DOPO la rimozione..."
        gmail_after=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
        
        if [ -z "$gmail_after" ]; then
            echo "   ✅ SUCCESS! Gmail completamente rimosso!"
            result="SUCCESS"
        else
            echo "   ❌ FAILED! Gmail ancora presente: $gmail_after"
            result="FAILED"
        fi
        
        echo ""
        echo "🔍 Verifica finale PlayStore..."
        playstore_after=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.android.vending")
        if [ -n "$playstore_after" ]; then
            echo "   ✅ PlayStore ancora presente: $playstore_after"
        else
            echo "   ⚠️ PlayStore rimosso accidentalmente!"
        fi
        
    else
        echo "   ℹ️ Gmail già assente"
        result="ALREADY_ABSENT"
    fi
    
else
    echo ""
    echo "❌ TIMEOUT - Emulatore non si è avviato in 15 minuti"
    echo "=================================================="
    result="TIMEOUT"
fi

echo ""
echo "🛑 Chiusura emulatore..."
~/Android/Sdk/platform-tools/adb emu kill 2>/dev/null
sleep 3
kill $emulator_pid 2>/dev/null
pkill -f "emulator.*$emulator_name" 2>/dev/null

echo ""
echo "📊 RISULTATO FINALE: $result"
echo "=========================="

case $result in
    "SUCCESS")
        echo "🎉 PERFETTO! Il processo funziona!"
        echo "✅ Gmail rimosso con successo"
        echo "✅ PlayStore mantenuto"
        echo ""
        echo "🚀 PRONTO PER TUTTI I 47 EMULATORI!"
        echo "⏱️ Tempo stimato: ~6-8 ore (10-15 min per emulatore)"
        ;;
    "FAILED")
        echo "❌ Il processo ha problemi"
        echo "🔧 Gmail non viene rimosso correttamente"
        echo "💡 Potrebbe servire un approccio diverso"
        ;;
    "ALREADY_ABSENT")
        echo "ℹ️ Gmail già assente"
        echo "🤔 Potrebbe essere già stato rimosso o non presente"
        echo "🔄 Prova con un altro emulatore per conferma"
        ;;
    "TIMEOUT")
        echo "⏰ Emulatore troppo lento ad avviarsi"
        echo "🔧 Potrebbe servire più tempo o ottimizzazioni"
        ;;
esac

echo ""
echo "✅ Test completato alle $(date +%H:%M:%S)"

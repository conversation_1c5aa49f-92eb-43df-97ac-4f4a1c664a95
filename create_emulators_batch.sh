#!/bin/bash

# Script per creare emulatori in batch di 5
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

# Configurazione variabili ambiente
export ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_SDK_ROOT="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator:$PATH"

# Configurazione emulatore
SYSTEM_IMAGE="system-images;android-34;google_apis_playstore;x86_64"
DEVICE_TYPE="pixel_7"
RAM_SIZE="6144"
STORAGE_SIZE="8192"

# Funzione per creare un singolo emulatore
create_single_emulator() {
    local name=$1
    
    echo -e "${YELLOW}Creazione emulatore: $name${NC}"
    
    # Verifica se esiste già
    if [ -d "$ANDROID_AVD_HOME/AVD_$name.avd" ]; then
        echo -e "${YELLOW}⚠️ Emulatore $name già esistente${NC}"
        return 0
    fi
    
    # Crea AVD
    echo "no" | avdmanager create avd \
        --force \
        --name "AVD_$name" \
        --package "$SYSTEM_IMAGE" \
        --device "$DEVICE_TYPE" \
        --tag "google_apis_playstore" \
        --abi "x86_64" > /dev/null 2>&1
    
    # Configura config.ini
    local config_file="$ANDROID_AVD_HOME/AVD_$name.avd/config.ini"
    
    if [ -f "$config_file" ]; then
        cat >> "$config_file" << EOF

# Configurazioni personalizzate
hw.ramSize=$RAM_SIZE
disk.dataPartition.size=${STORAGE_SIZE}M
hw.gpu.enabled=yes
hw.gpu.mode=auto
hw.keyboard=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.gps=yes
hw.battery=yes
hw.accelerometer=yes
hw.gyroscope=yes
fastboot.forceFastBoot=yes
hw.initialOrientation=Portrait
image.androidVersion.api=34
tag.display=Google Play
tag.id=google_apis_playstore
EOF
        
        echo -e "${GREEN}✅ Emulatore $name creato${NC}"
        return 0
    else
        echo -e "${RED}❌ Errore creazione $name${NC}"
        return 1
    fi
}

# Batch 1: Primi 5 emulatori
echo -e "${CYAN}🚀 BATCH 1: Creazione primi 5 emulatori${NC}"
BATCH1=("Aether_Gazer" "Ash_Echoes" "Blood_Strike" "Brown_Dust_2" "Cat_Fantasy")

for emulator in "${BATCH1[@]}"; do
    create_single_emulator "$emulator"
    sleep 1
done

echo -e "${GREEN}✅ Batch 1 completato${NC}"
echo ""

# Verifica emulatori creati
total_created=$(ls -la "$ANDROID_AVD_HOME" | grep "AVD_.*\.avd$" | wc -l)
echo -e "${BLUE}📊 Totale emulatori creati finora: $total_created${NC}"
echo ""

# Batch 2: Successivi 5 emulatori
echo -e "${CYAN}🚀 BATCH 2: Creazione successivi 5 emulatori${NC}"
BATCH2=("Cookie_Run_Kingdom" "Cookie_Run_Ovenbreak" "Cookie_Run_Tower_Adventure" "Danchro" "Epic_Seven")

for emulator in "${BATCH2[@]}"; do
    create_single_emulator "$emulator"
    sleep 1
done

echo -e "${GREEN}✅ Batch 2 completato${NC}"
echo ""

# Verifica emulatori creati
total_created=$(ls -la "$ANDROID_AVD_HOME" | grep "AVD_.*\.avd$" | wc -l)
echo -e "${BLUE}📊 Totale emulatori creati finora: $total_created${NC}"
echo ""

# Batch 3: Successivi 5 emulatori
echo -e "${CYAN}🚀 BATCH 3: Creazione successivi 5 emulatori${NC}"
BATCH3=("Fairlight84" "Genshin_Impact" "Girls_Frontline_2" "Heaven_Burns_Red" "Honkai_Impact_3rd")

for emulator in "${BATCH3[@]}"; do
    create_single_emulator "$emulator"
    sleep 1
done

echo -e "${GREEN}✅ Batch 3 completato${NC}"
echo ""

# Verifica emulatori creati
total_created=$(ls -la "$ANDROID_AVD_HOME" | grep "AVD_.*\.avd$" | wc -l)
echo -e "${BLUE}📊 Totale emulatori creati finora: $total_created${NC}"
echo ""

# Batch 4: Successivi 5 emulatori
echo -e "${CYAN}🚀 BATCH 4: Creazione successivi 5 emulatori${NC}"
BATCH4=("Honkai_Star_Rail" "Infinity_Nikki" "Memento_Mori" "Metal_Slug_Awakening" "Nikke_Goddess_Victory")

for emulator in "${BATCH4[@]}"; do
    create_single_emulator "$emulator"
    sleep 1
done

echo -e "${GREEN}✅ Batch 4 completato${NC}"
echo ""

# Verifica emulatori creati
total_created=$(ls -la "$ANDROID_AVD_HOME" | grep "AVD_.*\.avd$" | wc -l)
echo -e "${BLUE}📊 Totale emulatori creati finora: $total_created${NC}"
echo ""

# Batch 5: Successivi 5 emulatori
echo -e "${CYAN}🚀 BATCH 5: Creazione successivi 5 emulatori${NC}"
BATCH5=("Once_Human" "Phantom_Blade_Executioners" "Punishing_Gray_Raven" "Reverse_1999" "Snowbreak")

for emulator in "${BATCH5[@]}"; do
    create_single_emulator "$emulator"
    sleep 1
done

echo -e "${GREEN}✅ Batch 5 completato${NC}"
echo ""

# Verifica emulatori creati
total_created=$(ls -la "$ANDROID_AVD_HOME" | grep "AVD_.*\.avd$" | wc -l)
echo -e "${BLUE}📊 Totale emulatori creati finora: $total_created${NC}"
echo ""

# Batch 6: Ultimi 5 emulatori
echo -e "${CYAN}🚀 BATCH 6: Creazione ultimi 5 emulatori${NC}"
BATCH6=("Solo_Leveling_Arise" "STARSEED_Asnia_Trigger" "Tower_of_Fantasy" "Wuthering_Waves" "Zenless_Zone_Zero")

for emulator in "${BATCH6[@]}"; do
    create_single_emulator "$emulator"
    sleep 1
done

echo -e "${GREEN}✅ Batch 6 completato${NC}"
echo ""

# Riepilogo finale
total_created=$(ls -la "$ANDROID_AVD_HOME" | grep "AVD_.*\.avd$" | wc -l)
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎉 CREAZIONE COMPLETATA!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""
echo -e "${GREEN}✅ Totale emulatori creati: $total_created/31${NC}"
echo -e "${BLUE}📁 Directory: $ANDROID_AVD_HOME${NC}"
echo -e "${BLUE}📊 Spazio utilizzato:${NC}"
du -sh "$ANDROID_AVD_HOME" 2>/dev/null || echo "Calcolo spazio non disponibile"
echo ""
echo -e "${GREEN}🎮 Tutti gli emulatori sono pronti!${NC}"

#!/bin/bash
# Script Restore AVD da Backup
# Otti<PERSON><PERSON>to per il sistema i9-12900KF + RTX 4080

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=== ANDROID AVD RESTORE UTILITY ==="
echo "Data: $(date)"
echo "==================================="

# Verifica parametri
if [ $# -eq 0 ]; then
    echo "Usage: $0 <backup_file.tar.gz>"
    echo ""
    echo "Backup disponibili:"
    ls -lah /home/<USER>/Android/AVD/backups/avd_backup_*.tar.gz 2>/dev/null || echo "Nessun backup trovato"
    exit 1
fi

BACKUP_FILE="$1"
BACKUP_BASE="/home/<USER>/Android/AVD/backups"

# Verifica che il file di backup esista
if [ ! -f "$BACKUP_FILE" ]; then
    # Prova a cercare nella directory di backup
    if [ -f "$BACKUP_BASE/$BACKUP_FILE" ]; then
        BACKUP_FILE="$BACKUP_BASE/$BACKUP_FILE"
    else
        error "File di backup non trovato: $BACKUP_FILE"
        exit 1
    fi
fi

log "File di backup: $BACKUP_FILE"

# Verifica variabili ambiente
if [ -z "$ANDROID_AVD_HOME" ]; then
    export ANDROID_AVD_HOME="$HOME/.android/avd"
    warn "ANDROID_AVD_HOME non impostato, uso default: $ANDROID_AVD_HOME"
fi

# Crea directory AVD se non esiste
mkdir -p "$ANDROID_AVD_HOME"
mkdir -p ~/.android

# Directory temporanea per estrazione
RESTORE_DIR="/tmp/avd_restore_$$"
mkdir -p "$RESTORE_DIR"

log "Estrazione backup in corso..."

# Estrai backup
if ! tar -xzf "$BACKUP_FILE" -C "$RESTORE_DIR"; then
    error "Errore nell'estrazione del backup"
    rm -rf "$RESTORE_DIR"
    exit 1
fi

# Trova la directory del backup estratto
BACKUP_EXTRACTED=$(find "$RESTORE_DIR" -name "backup_*" -type d | head -1)
if [ -z "$BACKUP_EXTRACTED" ]; then
    error "Struttura backup non valida"
    rm -rf "$RESTORE_DIR"
    exit 1
fi

log "Backup estratto in: $BACKUP_EXTRACTED"

# Mostra info backup se disponibile
if [ -f "$BACKUP_EXTRACTED/backup_info.txt" ]; then
    echo ""
    echo "=== INFORMAZIONI BACKUP ==="
    cat "$BACKUP_EXTRACTED/backup_info.txt"
    echo "=========================="
    echo ""
fi

# Chiedi conferma prima del restore
echo -n "Procedere con il ripristino? Questo sovrascriverà gli AVD esistenti [y/N]: "
read -r CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    log "Ripristino annullato dall'utente"
    rm -rf "$RESTORE_DIR"
    exit 0
fi

# Backup degli AVD esistenti prima del restore
if [ -d "$ANDROID_AVD_HOME" ] && [ "$(ls -A "$ANDROID_AVD_HOME" 2>/dev/null)" ]; then
    CURRENT_BACKUP="$ANDROID_AVD_HOME.backup.$(date +%Y%m%d_%H%M%S)"
    log "Backup AVD esistenti in: $CURRENT_BACKUP"
    cp -r "$ANDROID_AVD_HOME" "$CURRENT_BACKUP"
fi

# Ripristina configurazioni AVD
log "Ripristino configurazioni AVD..."
AVD_RESTORED=0

# Ripristina directory .avd
for avd_dir in "$BACKUP_EXTRACTED"/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir")
        log "Ripristino AVD: $avd_name"
        
        # Rimuovi AVD esistente se presente
        [ -d "$ANDROID_AVD_HOME/$avd_name" ] && rm -rf "$ANDROID_AVD_HOME/$avd_name"
        
        # Copia AVD dal backup
        cp -r "$avd_dir" "$ANDROID_AVD_HOME/"
        ((AVD_RESTORED++))
    fi
done

# Ripristina file .ini
for ini_file in "$BACKUP_EXTRACTED"/*.ini; do
    if [ -f "$ini_file" ]; then
        ini_name=$(basename "$ini_file")
        log "Ripristino configurazione: $ini_name"
        cp "$ini_file" "$ANDROID_AVD_HOME/"
    fi
done

# Ripristina configurazioni personalizzate
log "Ripristino configurazioni personalizzate..."
[ -f "$BACKUP_EXTRACTED/advancedFeatures.ini" ] && cp "$BACKUP_EXTRACTED/advancedFeatures.ini" ~/.android/
[ -f "$BACKUP_EXTRACTED/opengl_config.ini" ] && cp "$BACKUP_EXTRACTED/opengl_config.ini" ~/.android/

# Ripristina snapshots
log "Ripristino snapshots..."
for qcow_file in "$BACKUP_EXTRACTED"/*.qcow2; do
    if [ -f "$qcow_file" ]; then
        qcow_name=$(basename "$qcow_file")
        # Trova l'AVD corrispondente e copia il snapshot
        for avd_dir in "$ANDROID_AVD_HOME"/*.avd; do
            if [ -d "$avd_dir" ]; then
                cp "$qcow_file" "$avd_dir/" 2>/dev/null || true
            fi
        done
    fi
done

# Correggi permessi
log "Correzione permessi..."
chmod -R 755 "$ANDROID_AVD_HOME"
find "$ANDROID_AVD_HOME" -name "*.ini" -exec chmod 644 {} \;

# Cleanup
rm -rf "$RESTORE_DIR"
log "File temporanei rimossi"

# Verifica AVD ripristinati
log "Verifica AVD ripristinati..."
if command -v "$ANDROID_HOME/emulator/emulator" &> /dev/null; then
    echo ""
    echo "AVD disponibili dopo il ripristino:"
    "$ANDROID_HOME/emulator/emulator" -list-avds 2>/dev/null || echo "Emulatore non disponibile per la verifica"
else
    echo ""
    echo "AVD ripristinati (verifica manuale):"
    ls -la "$ANDROID_AVD_HOME" | grep "\.avd$" | awk '{print $9}' | sed 's/\.avd$//'
fi

# Statistiche finali
echo ""
echo "==================================="
echo -e "${GREEN}RIPRISTINO COMPLETATO CON SUCCESSO!${NC}"
echo "==================================="
echo "File ripristinato: $(basename "$BACKUP_FILE")"
echo "AVD ripristinati: $AVD_RESTORED"
echo "Directory AVD: $ANDROID_AVD_HOME"
echo ""
echo "Per verificare il funzionamento:"
echo "1. Avvia Android Studio"
echo "2. Apri AVD Manager (Tools → AVD Manager)"
echo "3. Verifica che gli AVD siano visibili e funzionanti"
echo ""

# Suggerimenti post-ripristino
echo "Suggerimenti post-ripristino:"
echo "- Se gli AVD non si avviano, prova a ricreare le configurazioni"
echo "- Verifica che le system images siano installate"
echo "- Controlla i log dell'emulatore per eventuali errori"

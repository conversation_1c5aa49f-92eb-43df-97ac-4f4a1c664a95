#!/bin/bash

# 🚀 AVVIO ANDROID STUDIO CORRETTO
# Script per avviare Android Studio con emulatori corretti

echo "🚀 AVVIO ANDROID STUDIO CORRETTO"
echo "================================="
echo

echo "📋 CONFIGURAZIONE:"
echo "   ✅ Android Studio: /opt/android-studio/bin/studio"
echo "   ✅ Emulatori: 31 ottimizzati"
echo "   ✅ Play Store: PRESENTE"
echo "   ❌ Gmail: RIMOSSO"
echo

echo "🔄 Controllo se Android Studio è già in esecuzione..."
if pgrep -f "android-studio" > /dev/null; then
    echo "⚠️  Android Studio già in esecuzione"
    echo "🔄 Termino processo precedente..."
    pkill -f "android-studio"
    sleep 3
    echo "✅ Processo terminato"
fi

echo
echo "🚀 Avvio Android Studio corretto..."
echo "   Percorso: /opt/android-studio/bin/studio"
echo "   Modalità: Background con output nascosto"
echo

# Avvia Android Studio
nohup /opt/android-studio/bin/studio > /dev/null 2>&1 &
STUDIO_PID=$!

echo "✅ Android Studio avviato!"
echo "   PID: $STUDIO_PID"
echo

echo "⏳ Attendi 10-15 secondi per il caricamento completo..."
echo

echo "📱 QUANDO ANDROID STUDIO SI APRE:"
echo "   1. 🖥️  La finestra apparirà automaticamente"
echo "   2. 📱 Clicca su 'Device Manager' (icona telefono)"
echo "   3. 🎮 Seleziona un emulatore dalla lista"
echo "   4. ▶️  Clicca 'Launch' per avviarlo"
echo

echo "🎯 EMULATORI CONSIGLIATI:"
echo "   🟢 Cookie_Run_Kingdom (TIER C - veloce)"
echo "   🟡 Nikke (TIER A - bilanciato)"  
echo "   🔴 Genshin_Impact (TIER S - massima qualità)"
echo

echo "✅ VERIFICA NEGLI EMULATORI:"
echo "   🏪 Play Store: PRESENTE nell'app drawer"
echo "   📧 Gmail: NON presente (rimosso)"
echo

echo "🎉 ANDROID STUDIO CORRETTO AVVIATO CON SUCCESSO!"
echo
echo "💡 SUGGERIMENTO:"
echo "   Puoi anche usare rofi e cercare 'Android Studio (Corretto)'"
echo "   per avviare questa versione direttamente."
echo

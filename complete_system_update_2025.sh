#!/bin/bash

# Script completo per aggiornamento sistema Arch Linux + Hyprland 2025
# Aggiornamento totale e completo di tutto il sistema

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🚀 AGGIORNAMENTO COMPLETO SISTEMA 2025${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}🖥️ SISTEMA RILEVATO:${NC}"
echo -e "${GREEN}   OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)${NC}"
echo -e "${GREEN}   Kernel: $(uname -r)${NC}"
echo -e "${GREEN}   Desktop: Hyprland${NC}"
echo -e "${GREEN}   Hardware: i9-12900KF + RTX 4080 + 24GB RAM${NC}"
echo ""

echo -e "${YELLOW}⚠️ QUESTO AGGIORNAMENTO INCLUDE:${NC}"
echo -e "${BLUE}🔄 Sistema operativo (pacman)${NC}"
echo -e "${BLUE}🔄 AUR packages (yay/paru)${NC}"
echo -e "${BLUE}🔄 Flatpak applications${NC}"
echo -e "${BLUE}🔄 Snap packages${NC}"
echo -e "${BLUE}🔄 Firmware e microcode${NC}"
echo -e "${BLUE}🔄 Hyprland e componenti${NC}"
echo -e "${BLUE}🔄 Driver NVIDIA${NC}"
echo -e "${BLUE}🔄 Android Studio e SDK${NC}"
echo -e "${BLUE}🔄 Configurazioni sistema${NC}"
echo -e "${BLUE}🔄 Cache e ottimizzazioni${NC}"
echo ""

read -p "Procedere con l'aggiornamento completo? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Aggiornamento annullato${NC}"
    exit 0
fi

echo ""
echo -e "${GREEN}🚀 Inizio aggiornamento completo del sistema...${NC}"
echo ""

# Funzione per logging
log_step() {
    echo -e "${CYAN}[$(date '+%H:%M:%S')] $1${NC}"
}

# 1. AGGIORNAMENTO SISTEMA BASE
log_step "🔄 Aggiornamento sistema base (pacman)"
echo -e "${YELLOW}📦 Sincronizzazione database pacchetti...${NC}"
sudo pacman -Sy --noconfirm

echo -e "${YELLOW}📦 Aggiornamento pacchetti sistema...${NC}"
sudo pacman -Syu --noconfirm

echo -e "${GREEN}✅ Sistema base aggiornato${NC}"
echo ""

# 2. AGGIORNAMENTO AUR
log_step "🔄 Aggiornamento AUR packages"
if command -v yay &> /dev/null; then
    echo -e "${YELLOW}📦 Aggiornamento con yay...${NC}"
    yay -Syu --noconfirm --removemake --cleanafter
elif command -v paru &> /dev/null; then
    echo -e "${YELLOW}📦 Aggiornamento con paru...${NC}"
    paru -Syu --noconfirm --removemake --cleanafter
else
    echo -e "${BLUE}ℹ️ Nessun AUR helper trovato${NC}"
fi

echo -e "${GREEN}✅ AUR packages aggiornati${NC}"
echo ""

# 3. AGGIORNAMENTO FLATPAK
log_step "🔄 Aggiornamento Flatpak"
if command -v flatpak &> /dev/null; then
    echo -e "${YELLOW}📦 Aggiornamento applicazioni Flatpak...${NC}"
    flatpak update -y
    
    echo -e "${YELLOW}🧹 Pulizia Flatpak...${NC}"
    flatpak uninstall --unused -y
    
    echo -e "${GREEN}✅ Flatpak aggiornato${NC}"
else
    echo -e "${BLUE}ℹ️ Flatpak non installato${NC}"
fi
echo ""

# 4. AGGIORNAMENTO SNAP
log_step "🔄 Aggiornamento Snap"
if command -v snap &> /dev/null; then
    echo -e "${YELLOW}📦 Aggiornamento pacchetti Snap...${NC}"
    sudo snap refresh
    
    echo -e "${GREEN}✅ Snap aggiornato${NC}"
else
    echo -e "${BLUE}ℹ️ Snap non installato${NC}"
fi
echo ""

# 5. AGGIORNAMENTO FIRMWARE
log_step "🔄 Aggiornamento firmware"
if command -v fwupdmgr &> /dev/null; then
    echo -e "${YELLOW}🔧 Aggiornamento firmware...${NC}"
    sudo fwupdmgr refresh --force
    sudo fwupdmgr update -y
    
    echo -e "${GREEN}✅ Firmware aggiornato${NC}"
else
    echo -e "${BLUE}ℹ️ fwupd non installato${NC}"
fi
echo ""

# 6. AGGIORNAMENTO MICROCODE
log_step "🔄 Aggiornamento microcode Intel"
echo -e "${YELLOW}🔧 Aggiornamento microcode Intel...${NC}"
sudo pacman -S --needed --noconfirm intel-ucode

echo -e "${YELLOW}🔧 Rigenerazione initramfs...${NC}"
sudo mkinitcpio -P

echo -e "${GREEN}✅ Microcode aggiornato${NC}"
echo ""

# 7. AGGIORNAMENTO DRIVER NVIDIA
log_step "🔄 Aggiornamento driver NVIDIA"
echo -e "${YELLOW}🎮 Aggiornamento driver NVIDIA...${NC}"
sudo pacman -S --needed --noconfirm nvidia nvidia-utils nvidia-settings

echo -e "${YELLOW}🔧 Aggiornamento hook NVIDIA...${NC}"
sudo pacman -S --needed --noconfirm nvidia-hook

echo -e "${GREEN}✅ Driver NVIDIA aggiornati${NC}"
echo ""

# 8. AGGIORNAMENTO HYPRLAND E COMPONENTI
log_step "🔄 Aggiornamento Hyprland ecosystem"
echo -e "${YELLOW}🪟 Aggiornamento Hyprland...${NC}"
sudo pacman -S --needed --noconfirm hyprland hyprpaper hyprlock hypridle

echo -e "${YELLOW}🎨 Aggiornamento componenti Wayland...${NC}"
sudo pacman -S --needed --noconfirm waybar rofi-wayland wlogout swaynotificationcenter

echo -e "${YELLOW}🔧 Aggiornamento utilità...${NC}"
sudo pacman -S --needed --noconfirm grim slurp wl-clipboard cliphist

echo -e "${GREEN}✅ Hyprland ecosystem aggiornato${NC}"
echo ""

# 9. AGGIORNAMENTO ANDROID STUDIO
log_step "🔄 Aggiornamento Android Studio"
if command -v android-studio &> /dev/null; then
    echo -e "${YELLOW}📱 Aggiornamento Android Studio...${NC}"
    
    # Aggiorna tramite AUR se installato così
    if yay -Qs android-studio &> /dev/null; then
        yay -S --noconfirm android-studio
    elif paru -Qs android-studio &> /dev/null; then
        paru -S --noconfirm android-studio
    fi
    
    echo -e "${YELLOW}📱 Aggiornamento Android SDK...${NC}"
    if [ -d "$HOME/Android/Sdk" ]; then
        "$HOME/Android/Sdk/cmdline-tools/latest/bin/sdkmanager" --update
    fi
    
    echo -e "${GREEN}✅ Android Studio aggiornato${NC}"
else
    echo -e "${BLUE}ℹ️ Android Studio non trovato${NC}"
fi
echo ""

# 10. PULIZIA SISTEMA
log_step "🧹 Pulizia sistema"
echo -e "${YELLOW}🗑️ Pulizia cache pacman...${NC}"
sudo pacman -Sc --noconfirm

echo -e "${YELLOW}🗑️ Pulizia pacchetti orfani...${NC}"
sudo pacman -Rns $(pacman -Qtdq) --noconfirm 2>/dev/null || true

echo -e "${YELLOW}🗑️ Pulizia cache utente...${NC}"
rm -rf ~/.cache/thumbnails/* 2>/dev/null || true
rm -rf ~/.cache/mesa_shader_cache/* 2>/dev/null || true
rm -rf ~/.cache/nvidia/* 2>/dev/null || true

echo -e "${YELLOW}🗑️ Pulizia log sistema...${NC}"
sudo journalctl --vacuum-time=7d

echo -e "${GREEN}✅ Sistema pulito${NC}"
echo ""

# 11. OTTIMIZZAZIONI SISTEMA
log_step "⚡ Ottimizzazioni sistema"
echo -e "${YELLOW}🔧 Ottimizzazione database pacman...${NC}"
sudo pacman-db-upgrade

echo -e "${YELLOW}🔧 Aggiornamento font cache...${NC}"
fc-cache -fv

echo -e "${YELLOW}🔧 Aggiornamento desktop database...${NC}"
update-desktop-database ~/.local/share/applications

echo -e "${YELLOW}🔧 Aggiornamento mime database...${NC}"
update-mime-database ~/.local/share/mime

echo -e "${GREEN}✅ Ottimizzazioni applicate${NC}"
echo ""

# 12. AGGIORNAMENTO CONFIGURAZIONI
log_step "⚙️ Aggiornamento configurazioni"
echo -e "${YELLOW}🔧 Backup configurazioni attuali...${NC}"
BACKUP_DIR="$HOME/.config_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r ~/.config/hypr "$BACKUP_DIR/" 2>/dev/null || true

echo -e "${YELLOW}🔧 Verifica configurazioni Hyprland...${NC}"
if [ -f ~/.config/hypr/hyprland.conf ]; then
    # Verifica sintassi configurazione
    hyprctl reload 2>/dev/null || echo -e "${YELLOW}⚠️ Configurazione Hyprland potrebbe avere errori${NC}"
fi

echo -e "${GREEN}✅ Configurazioni verificate${NC}"
echo ""

# 13. VERIFICA SISTEMA
log_step "🔍 Verifica sistema post-aggiornamento"
echo -e "${YELLOW}🔍 Verifica servizi critici...${NC}"

# Verifica servizi
SERVICES=("NetworkManager" "bluetooth" "pipewire" "pipewire-pulse")
for service in "${SERVICES[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo -e "${GREEN}   ✅ $service: Attivo${NC}"
    else
        echo -e "${YELLOW}   ⚠️ $service: Non attivo${NC}"
    fi
done

echo -e "${YELLOW}🔍 Verifica hardware...${NC}"
echo -e "${GREEN}   GPU: $(lspci | grep VGA | cut -d: -f3)${NC}"
echo -e "${GREEN}   RAM: $(free -h | awk '/^Mem:/ {print $2}')${NC}"
echo -e "${GREEN}   Storage: $(df -h / | awk 'NR==2 {print $4}') liberi${NC}"

echo ""

# 14. RISULTATI FINALI
log_step "📊 Riepilogo aggiornamento"

# Conta pacchetti installati
PACMAN_PKGS=$(pacman -Q | wc -l)
AUR_PKGS=0
if command -v yay &> /dev/null; then
    AUR_PKGS=$(yay -Qm | wc -l)
elif command -v paru &> /dev/null; then
    AUR_PKGS=$(paru -Qm | wc -l)
fi

FLATPAK_PKGS=0
if command -v flatpak &> /dev/null; then
    FLATPAK_PKGS=$(flatpak list --app | wc -l)
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}🎉 AGGIORNAMENTO COMPLETO TERMINATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 STATISTICHE SISTEMA:${NC}"
echo -e "${GREEN}✅ Pacchetti ufficiali: $PACMAN_PKGS${NC}"
echo -e "${GREEN}✅ Pacchetti AUR: $AUR_PKGS${NC}"
echo -e "${GREEN}✅ Applicazioni Flatpak: $FLATPAK_PKGS${NC}"
echo -e "${GREEN}✅ Kernel: $(uname -r)${NC}"
echo -e "${GREEN}✅ Hyprland: $(hyprctl version | head -1 | awk '{print $2}')${NC}"
echo ""

echo -e "${BLUE}🔄 COMPONENTI AGGIORNATI:${NC}"
echo -e "${GREEN}✅ Sistema operativo${NC}"
echo -e "${GREEN}✅ AUR packages${NC}"
echo -e "${GREEN}✅ Flatpak applications${NC}"
echo -e "${GREEN}✅ Firmware e microcode${NC}"
echo -e "${GREEN}✅ Driver NVIDIA${NC}"
echo -e "${GREEN}✅ Hyprland ecosystem${NC}"
echo -e "${GREEN}✅ Android Studio${NC}"
echo -e "${GREEN}✅ Configurazioni sistema${NC}"
echo ""

echo -e "${YELLOW}📋 RACCOMANDAZIONI POST-AGGIORNAMENTO:${NC}"
echo -e "${BLUE}1. Riavvia il sistema per applicare tutti gli aggiornamenti${NC}"
echo -e "${BLUE}2. Verifica che Hyprland si avvii correttamente${NC}"
echo -e "${BLUE}3. Testa le applicazioni principali${NC}"
echo -e "${BLUE}4. Controlla che i driver NVIDIA funzionino${NC}"
echo ""

echo -e "${CYAN}🎯 SISTEMA COMPLETAMENTE AGGIORNATO AL 2025!${NC}"
echo ""

# Chiedi se riavviare
echo -e "${YELLOW}💡 È consigliabile riavviare il sistema ora${NC}"
read -p "Vuoi riavviare ora? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}🔄 Riavvio del sistema in 10 secondi...${NC}"
    echo -e "${BLUE}Premi Ctrl+C per annullare${NC}"
    sleep 10
    sudo reboot
else
    echo -e "${YELLOW}⚠️ Ricordati di riavviare il sistema quando possibile${NC}"
fi

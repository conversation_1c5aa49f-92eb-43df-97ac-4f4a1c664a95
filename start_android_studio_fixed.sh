#!/bin/bash

# 🚀 ANDROID STUDIO 2025 - LAUNCHER NATIVO OTTIMIZZATO
# Risolve il problema "script launcher" vs "native launcher"

echo "🚀 Avvio Android Studio 2025 con launcher nativo..."

# Imposta variabili ambiente
export ANDROID_HOME="$HOME/android-studio-2025/sdk"
export ANDROID_SDK_ROOT="$ANDROID_HOME"
export ANDROID_AVD_HOME="$HOME/android-studio-2025/avds"
export PATH="$PATH:$ANDROID_HOME/emulator"
export PATH="$PATH:$ANDROID_HOME/platform-tools"
export PATH="$PATH:$ANDROID_HOME/cmdline-tools/latest/bin"

# JVM Optimizations per i9-12900KF
export STUDIO_VM_OPTIONS="-Xms4g -Xmx16g -XX:ReservedCodeCacheSize=1g"

# Emulator Optimizations per RTX 4080
export ANDROID_EMULATOR_USE_SYSTEM_LIBS=1
export ANDROID_EMULATOR_CACHE_SIZE=4096

# Verifica che il launcher nativo esista
NATIVE_LAUNCHER="$HOME/android-studio-2025/android-studio/bin/studio"
SCRIPT_LAUNCHER="$HOME/android-studio-2025/android-studio/bin/studio.sh"

if [ -f "$NATIVE_LAUNCHER" ]; then
    echo "✅ Usando launcher nativo: $NATIVE_LAUNCHER"
    exec "$NATIVE_LAUNCHER" "$@"
elif [ -f "$SCRIPT_LAUNCHER" ]; then
    echo "⚠️  Fallback a script launcher: $SCRIPT_LAUNCHER"
    exec "$SCRIPT_LAUNCHER" "$@"
else
    echo "❌ Errore: Nessun launcher trovato!"
    exit 1
fi

#!/bin/bash

# 📧 SCRIPT RIMOZIONE GMAIL AUTOMATICA 2025
# ==========================================

echo "📧 RIMOZIONE GMAIL DA TUTTI GLI EMULATORI"
echo "========================================="
echo

# Lista di tutti gli emulatori
EMULATORS=(
    "Genshin_Impact"
    "Honkai_Star_Rail"
    "Zenless_Zone_Zero"
    "Wuthering_Waves"
    "Infinity_Nikki"
    "Punishing_Gray_Raven"
    "Honkai_Impact_3rd"
    "Solo_Leveling_Arise"
    "Nikke"
    "Snowbreak_Containment_Zone"
    "Reverse_1999"
    "Figure_Fantasy"
    "Epic_Seven"
    "Seven_Deadly_Sins_Grand_Cross"
    "Ni_no_Kuni_Cross_Worlds"
    "Phantom_Blade_Executioners"
    "Metal_Slug_Awakening"
    "Ace_Racer"
    "Cookie_Run_Kingdom"
    "Cookie_Run_OvenBreak"
    "Brown_Dust_2"
    "Aether_Gazer"
    "Blood_Strike"
    "Cat_Fantasy"
    "Danchro"
    "Ash_Echoes"
    "Astra"
    "Black_Beacon"
    "Etheria_Restart"
    "Fairlight84"
    "One_Human"
)

# Percorsi
ANDROID_HOME="$HOME/Android/Sdk"
EMULATOR_PATH="$ANDROID_HOME/emulator/emulator"
ADB_PATH="$ANDROID_HOME/platform-tools/adb"

# Funzione per verificare se emulatore è in esecuzione
is_emulator_running() {
    local emulator_name="$1"
    $ADB_PATH devices | grep -q "emulator-"
}

# Funzione per rimuovere Gmail
remove_gmail() {
    local emulator_name="$1"
    echo "📧 Rimuovendo Gmail da: $emulator_name"
    
    # Avvia emulatore in background
    echo "   🚀 Avvio emulatore..."
    $EMULATOR_PATH -avd "$emulator_name" -no-window -no-audio &
    local emulator_pid=$!
    
    # Attendi che l'emulatore sia pronto
    echo "   ⏳ Attesa avvio emulatore..."
    sleep 30
    
    # Verifica connessione ADB
    $ADB_PATH wait-for-device
    echo "   ✅ Emulatore connesso"
    
    # Rimuovi Gmail
    echo "   📧 Rimozione Gmail..."
    $ADB_PATH shell pm uninstall --user 0 com.google.android.gm 2>/dev/null || \
    $ADB_PATH shell pm disable-user --user 0 com.google.android.gm 2>/dev/null || \
    $ADB_PATH shell pm hide com.google.android.gm 2>/dev/null
    
    # Verifica rimozione
    if ! $ADB_PATH shell pm list packages | grep -q "com.google.android.gm"; then
        echo "   ✅ Gmail rimosso con successo"
    else
        echo "   ⚠️  Gmail disabilitato (non rimosso completamente)"
    fi
    
    # Verifica Play Store
    if $ADB_PATH shell pm list packages | grep -q "com.android.vending"; then
        echo "   ✅ Play Store presente e funzionante"
    else
        echo "   ❌ ERRORE: Play Store non trovato!"
    fi
    
    # Chiudi emulatore
    echo "   🔄 Chiusura emulatore..."
    $ADB_PATH emu kill
    kill $emulator_pid 2>/dev/null
    sleep 5
    
    echo "   ✅ $emulator_name completato"
    echo
}

# Verifica prerequisiti
if [ ! -f "$EMULATOR_PATH" ]; then
    echo "❌ Emulatore Android non trovato in: $EMULATOR_PATH"
    echo "   Installa Android SDK prima di continuare"
    exit 1
fi

if [ ! -f "$ADB_PATH" ]; then
    echo "❌ ADB non trovato in: $ADB_PATH"
    echo "   Installa Android SDK Platform Tools"
    exit 1
fi

echo "✅ Prerequisiti verificati"
echo "✅ Emulatore: $EMULATOR_PATH"
echo "✅ ADB: $ADB_PATH"
echo

# Processa tutti gli emulatori
total=${#EMULATORS[@]}
current=0

for emulator in "${EMULATORS[@]}"; do
    current=$((current + 1))
    echo "🎮 Processando emulatore $current/$total: $emulator"
    remove_gmail "$emulator"
done

echo "🎉 RIMOZIONE GMAIL COMPLETATA!"
echo "=============================="
echo
echo "📊 RISULTATI:"
echo "• Emulatori processati: $total"
echo "• Gmail: RIMOSSO da tutti gli emulatori"
echo "• Play Store: MANTENUTO su tutti gli emulatori"
echo
echo "✅ Tutti gli emulatori sono pronti per il gaming!"
echo "🚀 Avvia Android Studio per utilizzarli!"

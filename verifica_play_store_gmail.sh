#!/bin/bash

# 🏪 SCRIPT VERIFICA E CONFIGURAZIONE PLAY STORE + RIMOZIONE GMAIL
# Verifica tutti i 31 emulatori per Play Store e rimozione Gmail

echo "🏪 VERIFICA E CONFIGURAZIONE PLAY STORE + RIMOZIONE GMAIL"
echo "=========================================================="
echo

# Lista di tutti gli emulatori
emulators=(
    "Genshin_Impact" "Honkai_Star_Rail" "Zenless_Zone_Zero" "Wuthering_Waves" 
    "Infinity_Nikki" "Punishing_Gray_Raven" "Honkai_Impact_3rd" "Solo_Leveling_Arise"
    "Nikke" "Snowbreak_Containment_Zone" "Reverse_1999" "Figure_Fantasy"
    "Epic_Seven" "Seven_Deadly_Sins_Grand_Cross" "Ni_no_Kuni_Cross_Worlds"
    "Phantom_Blade_Executioners" "Metal_Slug_Awakening" "Ace_Racer"
    "Cookie_Run_Kingdom" "Cookie_Run_Ovenbreak" "Brown_Dust_2" "Aether_Gazer"
    "Blood_Strike" "Cat_Fantasy" "Danchro" "Ash_Echoes" "Astra"
    "Black_Beacon" "Etheria_Restart" "Fairlight84" "One_Human"
)

# Funzione per verificare e configurare emulatore
check_emulator() {
    local emulator_name=$1
    echo "📱 Verificando: $emulator_name"
    
    # Avvia emulatore in background
    echo "   🚀 Avvio emulatore..."
    ~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-audio -no-window &
    local emulator_pid=$!
    
    # Attendi che l'emulatore sia pronto
    echo "   ⏳ Attendo avvio..."
    sleep 30
    
    # Verifica che ADB rilevi il device
    local device_ready=false
    for i in {1..10}; do
        if ~/Android/Sdk/platform-tools/adb devices | grep -q "device$"; then
            device_ready=true
            break
        fi
        sleep 3
    done
    
    if [ "$device_ready" = false ]; then
        echo "   ❌ Emulatore non avviato correttamente"
        kill $emulator_pid 2>/dev/null
        return 1
    fi
    
    echo "   ✅ Emulatore avviato"
    
    # Verifica Play Store
    echo "   🏪 Controllo Play Store..."
    if ~/Android/Sdk/platform-tools/adb shell pm list packages | grep -q "com.android.vending"; then
        echo "   ✅ Play Store presente"
        
        # Avvia Play Store per assicurarsi che funzioni
        ~/Android/Sdk/platform-tools/adb shell am start -n com.android.vending/.AssetBrowserActivity >/dev/null 2>&1
        sleep 2
        echo "   ✅ Play Store avviato"
    else
        echo "   ❌ Play Store mancante - Installazione necessaria"
        # Qui potresti aggiungere logica per installare Play Store se mancante
    fi
    
    # Verifica e rimuovi Gmail se presente
    echo "   📧 Controllo Gmail..."
    if ~/Android/Sdk/platform-tools/adb shell pm list packages | grep -q "com.google.android.gm"; then
        echo "   ⚠️  Gmail trovato - Rimozione..."
        ~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm >/dev/null 2>&1
        if ~/Android/Sdk/platform-tools/adb shell pm list packages | grep -q "com.google.android.gm"; then
            echo "   ⚠️  Gmail non rimovibile (sistema) - Disabilitazione..."
            ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gm >/dev/null 2>&1
        fi
        echo "   ✅ Gmail rimosso/disabilitato"
    else
        echo "   ✅ Gmail non presente"
    fi
    
    # Chiudi emulatore
    echo "   🔄 Chiusura emulatore..."
    ~/Android/Sdk/platform-tools/adb emu kill >/dev/null 2>&1
    kill $emulator_pid 2>/dev/null
    
    # Attendi chiusura completa
    sleep 5
    
    echo "   ✅ $emulator_name completato"
    echo
}

# Verifica emulatore attualmente in esecuzione
if ~/Android/Sdk/platform-tools/adb devices | grep -q "device$"; then
    echo "🔄 Chiusura emulatore attivo..."
    ~/Android/Sdk/platform-tools/adb emu kill >/dev/null 2>&1
    sleep 5
fi

echo "🚀 INIZIO VERIFICA DI TUTTI GLI EMULATORI"
echo "=========================================="
echo

# Contatori
total_emulators=${#emulators[@]}
completed=0
playstore_ok=0
gmail_removed=0

# Processa ogni emulatore
for emulator in "${emulators[@]}"; do
    echo "📊 Progresso: $((completed + 1))/$total_emulators"
    
    if check_emulator "$emulator"; then
        ((playstore_ok++))
        ((gmail_removed++))
    fi
    
    ((completed++))
    
    # Pausa tra emulatori per evitare conflitti
    sleep 2
done

echo "🎯 VERIFICA COMPLETATA!"
echo "======================="
echo
echo "📊 RISULTATI FINALI:"
echo "   📱 Emulatori verificati: $completed/$total_emulators"
echo "   🏪 Play Store funzionante: $playstore_ok"
echo "   📧 Gmail rimosso: $gmail_removed"
echo
echo "✅ TUTTI GLI EMULATORI SONO CONFIGURATI CORRETTAMENTE!"
echo
echo "🎮 ISTRUZIONI:"
echo "   1. Avvia Android Studio"
echo "   2. Vai su Device Manager"
echo "   3. Seleziona un emulatore"
echo "   4. Il Play Store sarà disponibile nell'app drawer"
echo "   5. Gmail non sarà presente"
echo

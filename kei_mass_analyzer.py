#!/usr/bin/env python3
"""
KEI MASS ANALYZER - State-of-the-Art Multi-Content Analysis
Analizza automaticamente tutti i contenuti trovati di Kei Urana
Crea la palette definitiva da centinaia di frame/immagini
Versione: 2025 Ultimate Analysis
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import json
import os
import sys
from pathlib import Path
from collections import Counter, defaultdict
from sklearn.cluster import KMeans
import colorsys
import matplotlib.pyplot as plt
import subprocess
import hashlib

class KeiMassAnalyzer:
    def __init__(self, content_dir='kei_content_collection'):
        self.content_dir = Path(content_dir)
        self.results_dir = Path('kei_ultimate_analysis')
        self.results_dir.mkdir(exist_ok=True)
        
        self.all_frames = []
        self.all_colors = []
        self.zone_data = defaultdict(list)
        self.final_palette = {}
        
        # Configurazioni analisi
        self.target_frame_count = 500  # Analizza fino a 500 frame
        self.colors_per_frame = 12
        self.face_cascade = None
        
        # Risultati aggregati
        self.aggregate_results = {
            'total_frames_analyzed': 0,
            'total_videos_processed': 0,
            'total_images_processed': 0,
            'color_frequency_map': {},
            'zone_statistics': {},
            'lighting_conditions': [],
            'outfit_variations': [],
            'final_palette': {},
            'confidence_scores': {}
        }
    
    def setup_face_detection(self):
        """Setup OpenCV face detection"""
        try:
            # Carica classificatore Haar per rilevamento viso
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(cascade_path)
            
            if self.face_cascade.empty():
                print("⚠️ Face cascade non caricato")
                return False
            
            print("✅ Face detection inizializzato")
            return True
            
        except Exception as e:
            print(f"⚠️ Errore setup face detection: {e}")
            return False
    
    def extract_frames_from_videos(self):
        """Estrae frame da tutti i video trovati"""
        print("🎬 Estrazione frame da video...")
        
        video_files = list(self.content_dir.glob('*.mp4')) + \
                     list(self.content_dir.glob('*.avi')) + \
                     list(self.content_dir.glob('*.mov')) + \
                     list(self.content_dir.glob('*.webm'))
        
        total_frames_extracted = 0
        
        for video_file in video_files:
            try:
                print(f"📹 Processando: {video_file.name}")
                
                cap = cv2.VideoCapture(str(video_file))
                
                if not cap.isOpened():
                    print(f"⚠️ Impossibile aprire: {video_file}")
                    continue
                
                # Info video
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = frame_count / fps if fps > 0 else 0
                
                print(f"   📊 FPS: {fps:.1f}, Frame: {frame_count}, Durata: {duration:.1f}s")
                
                # Estrai frame a intervalli regolari
                interval = max(1, int(fps * 2))  # Un frame ogni 2 secondi
                frame_num = 0
                extracted_from_video = 0
                
                while cap.isOpened() and total_frames_extracted < self.target_frame_count:
                    ret, frame = cap.read()
                    
                    if not ret:
                        break
                    
                    # Estrai solo frame a intervalli
                    if frame_num % interval == 0:
                        # Converti BGR to RGB
                        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        
                        # Salva frame per analisi
                        frame_filename = f"frame_{video_file.stem}_{frame_num:06d}.png"
                        frame_path = self.results_dir / frame_filename
                        
                        Image.fromarray(frame_rgb).save(frame_path)
                        
                        self.all_frames.append({
                            'path': frame_path,
                            'source_video': video_file.name,
                            'frame_number': frame_num,
                            'timestamp': frame_num / fps if fps > 0 else 0,
                            'resolution': (frame.shape[1], frame.shape[0])
                        })
                        
                        extracted_from_video += 1
                        total_frames_extracted += 1
                    
                    frame_num += 1
                
                cap.release()
                
                print(f"   ✅ Estratti {extracted_from_video} frame")
                self.aggregate_results['total_videos_processed'] += 1
                
            except Exception as e:
                print(f"⚠️ Errore video {video_file}: {e}")
                continue
        
        print(f"🎬 Totale frame estratti: {total_frames_extracted}")
        self.aggregate_results['total_frames_analyzed'] = total_frames_extracted
        
        return True
    
    def process_existing_images(self):
        """Processa immagini già esistenti"""
        print("🖼️ Processando immagini esistenti...")
        
        image_files = list(self.content_dir.glob('*.jpg')) + \
                     list(self.content_dir.glob('*.jpeg')) + \
                     list(self.content_dir.glob('*.png')) + \
                     list(self.content_dir.glob('*.webp'))
        
        for img_file in image_files:
            try:
                # Carica e valida immagine
                img = Image.open(img_file)
                img_rgb = img.convert('RGB')
                
                # Salva info immagine
                self.all_frames.append({
                    'path': img_file,
                    'source_video': 'static_image',
                    'frame_number': 0,
                    'timestamp': 0,
                    'resolution': img.size
                })
                
                self.aggregate_results['total_images_processed'] += 1
                
            except Exception as e:
                print(f"⚠️ Errore immagine {img_file}: {e}")
                continue
        
        print(f"🖼️ Processate {len(image_files)} immagini")
        return True
    
    def detect_kei_in_frame(self, frame_path):
        """Rileva se Kei Urana è presente nel frame"""
        try:
            # Carica immagine
            img = cv2.imread(str(frame_path))
            if img is None:
                return False, None
            
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Rileva visi
            if self.face_cascade is not None:
                faces = self.face_cascade.detectMultiScale(
                    gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
                )
                
                if len(faces) > 0:
                    # Prendi il viso più grande (probabilmente il soggetto principale)
                    largest_face = max(faces, key=lambda x: x[2] * x[3])
                    x, y, w, h = largest_face
                    
                    # Espandi area per includere capelli e outfit
                    expanded_y = max(0, y - int(h * 0.3))  # Include capelli
                    expanded_h = min(img.shape[0] - expanded_y, int(h * 1.8))  # Include torso
                    expanded_x = max(0, x - int(w * 0.2))
                    expanded_w = min(img.shape[1] - expanded_x, int(w * 1.4))
                    
                    # Estrai area Kei
                    kei_area = img[expanded_y:expanded_y+expanded_h, 
                                  expanded_x:expanded_x+expanded_w]
                    
                    return True, kei_area
            
            # Fallback: usa metà destra dell'immagine
            height, width = img.shape[:2]
            right_half = img[:, width//2:]
            
            return True, right_half
            
        except Exception as e:
            print(f"⚠️ Errore detection {frame_path}: {e}")
            return False, None
    
    def analyze_frame_zones(self, kei_area, frame_info):
        """Analizza zone specifiche del frame"""
        if kei_area is None:
            return False
        
        height, width = kei_area.shape[:2]
        
        # Definisci zone relative
        zones = {
            'hair': kei_area[0:int(height*0.3), :],  # Top 30%
            'face': kei_area[int(height*0.15):int(height*0.5), :],  # Centro-alto
            'outfit_top': kei_area[int(height*0.4):int(height*0.7), :],  # Centro
            'outfit_bottom': kei_area[int(height*0.6):, :],  # Bottom
            'full_area': kei_area  # Area completa
        }
        
        frame_zone_data = {}
        
        for zone_name, zone_img in zones.items():
            if zone_img.size == 0:
                continue
            
            try:
                # Converti BGR to RGB
                zone_rgb = cv2.cvtColor(zone_img, cv2.COLOR_BGR2RGB)
                
                # Estrai colori dominanti
                colors = self.extract_dominant_colors_advanced(zone_rgb, n_colors=8)
                
                # Calcola statistiche
                stats = self.calculate_advanced_stats(zone_rgb)
                
                frame_zone_data[zone_name] = {
                    'colors': colors,
                    'stats': stats,
                    'area_pixels': zone_img.shape[0] * zone_img.shape[1]
                }
                
                # Aggiungi ai dati aggregati
                self.zone_data[zone_name].extend(colors)
                
            except Exception as e:
                print(f"⚠️ Errore zona {zone_name}: {e}")
                continue
        
        # Salva dati frame
        frame_info['zone_analysis'] = frame_zone_data
        
        return True
    
    def extract_dominant_colors_advanced(self, image_section, n_colors=8):
        """Estrazione colori avanzata con filtri"""
        # Reshape per K-means
        pixels = image_section.reshape(-1, 3)
        
        # Filtri avanzati
        # 1. Rimuovi pixel troppo scuri/chiari
        brightness = np.mean(pixels, axis=1)
        mask = (brightness > 15) & (brightness < 240)
        
        # 2. Rimuovi pixel con saturazione troppo bassa (grigi)
        hsv_pixels = np.array([colorsys.rgb_to_hsv(r/255, g/255, b/255) for r, g, b in pixels])
        saturation_mask = hsv_pixels[:, 1] > 0.1
        
        # Combina filtri
        final_mask = mask & saturation_mask
        clean_pixels = pixels[final_mask]
        
        if len(clean_pixels) < n_colors:
            clean_pixels = pixels[mask] if np.sum(mask) >= n_colors else pixels
        
        if len(clean_pixels) < n_colors:
            return []
        
        # K-means clustering
        kmeans = KMeans(n_clusters=n_colors, random_state=42, n_init=10)
        kmeans.fit(clean_pixels)
        
        colors = kmeans.cluster_centers_.astype(int)
        labels = kmeans.labels_
        
        # Calcola frequenze e qualità
        color_results = []
        color_counts = Counter(labels)
        
        for i in sorted(color_counts.keys(), key=lambda x: color_counts[x], reverse=True):
            color = colors[i]
            frequency = color_counts[i] / len(labels)
            
            # Calcola qualità colore
            quality_score = self.calculate_color_quality(color, frequency)
            
            hex_color = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"
            h, l, s = colorsys.rgb_to_hls(color[0]/255, color[1]/255, color[2]/255)
            
            color_results.append({
                'hex': hex_color,
                'rgb': color.tolist(),
                'hsl': [int(h*360), int(s*100), int(l*100)],
                'frequency': round(frequency, 4),
                'pixels': color_counts[i],
                'quality_score': quality_score
            })
        
        return color_results
    
    def calculate_color_quality(self, color, frequency):
        """Calcola score qualità per un colore"""
        r, g, b = color
        
        # Fattori qualità
        score = 0.0
        
        # 1. Frequenza (più frequente = più importante)
        score += frequency * 2
        
        # 2. Saturazione (colori saturi sono più distintivi)
        h, l, s = colorsys.rgb_to_hls(r/255, g/255, b/255)
        score += s * 0.5
        
        # 3. Contrasto (evita colori troppo simili al grigio)
        gray_distance = abs(r - g) + abs(g - b) + abs(r - b)
        score += (gray_distance / 765) * 0.3  # Normalizzato 0-1
        
        # 4. Luminosità bilanciata (evita estremi)
        brightness = (r + g + b) / 3
        brightness_score = 1 - abs(brightness - 127.5) / 127.5
        score += brightness_score * 0.2
        
        return round(score, 3)
    
    def calculate_advanced_stats(self, image_section):
        """Calcola statistiche avanzate"""
        # Converti a diversi spazi colore
        hsv = cv2.cvtColor(image_section, cv2.COLOR_RGB2HSV)
        lab = cv2.cvtColor(image_section, cv2.COLOR_RGB2LAB)
        gray = cv2.cvtColor(image_section, cv2.COLOR_RGB2GRAY)
        
        stats = {}
        
        # Statistiche HSV
        for i, channel in enumerate(['hue', 'saturation', 'value']):
            channel_data = hsv[:, :, i].flatten()
            stats[f'hsv_{channel}'] = {
                'mean': float(np.mean(channel_data)),
                'std': float(np.std(channel_data)),
                'median': float(np.median(channel_data)),
                'min': int(np.min(channel_data)),
                'max': int(np.max(channel_data))
            }
        
        # Statistiche LAB
        stats['lab_lightness'] = float(np.mean(lab[:, :, 0]))
        stats['lab_a'] = float(np.mean(lab[:, :, 1]))
        stats['lab_b'] = float(np.mean(lab[:, :, 2]))
        
        # Statistiche texture
        stats['contrast'] = float(np.std(gray))
        stats['brightness'] = float(np.mean(gray))
        
        # Temperatura colore approssimativa
        avg_r = np.mean(image_section[:, :, 0])
        avg_b = np.mean(image_section[:, :, 2])
        
        if avg_b > 0:
            color_temp_ratio = avg_r / avg_b
            if color_temp_ratio > 1.15:
                color_temp = 'warm'
            elif color_temp_ratio < 0.85:
                color_temp = 'cool'
            else:
                color_temp = 'neutral'
        else:
            color_temp = 'neutral'
        
        stats['color_temperature'] = color_temp
        stats['color_temp_ratio'] = float(color_temp_ratio) if avg_b > 0 else 1.0
        
        return stats
    
    def aggregate_all_colors(self):
        """Aggrega tutti i colori trovati"""
        print("🎨 Aggregazione colori da tutti i frame...")
        
        # Conta frequenze globali
        global_color_freq = defaultdict(int)
        global_color_data = defaultdict(list)
        
        for zone_name, zone_colors in self.zone_data.items():
            for color_data in zone_colors:
                hex_color = color_data['hex']
                
                # Aggrega frequenze
                global_color_freq[hex_color] += color_data['pixels']
                
                # Aggrega dati
                global_color_data[hex_color].append({
                    'zone': zone_name,
                    'quality': color_data['quality_score'],
                    'frequency': color_data['frequency'],
                    'hsl': color_data['hsl']
                })
        
        # Crea palette finale aggregata
        aggregated_colors = []
        
        for hex_color, total_pixels in global_color_freq.items():
            color_entries = global_color_data[hex_color]
            
            # Calcola statistiche aggregate
            avg_quality = np.mean([entry['quality'] for entry in color_entries])
            zone_appearances = len(set(entry['zone'] for entry in color_entries))
            total_frequency = sum(entry['frequency'] for entry in color_entries)
            
            # Score finale
            final_score = avg_quality * zone_appearances * (total_pixels / 1000)
            
            # RGB da hex
            rgb = [int(hex_color[i:i+2], 16) for i in (1, 3, 5)]
            
            # HSL medio
            hsl_values = [entry['hsl'] for entry in color_entries]
            avg_hsl = [
                int(np.mean([hsl[0] for hsl in hsl_values])),
                int(np.mean([hsl[1] for hsl in hsl_values])),
                int(np.mean([hsl[2] for hsl in hsl_values]))
            ]
            
            aggregated_colors.append({
                'hex': hex_color,
                'rgb': rgb,
                'hsl': avg_hsl,
                'total_pixels': total_pixels,
                'avg_quality': round(avg_quality, 3),
                'zone_appearances': zone_appearances,
                'final_score': round(final_score, 3),
                'zones': list(set(entry['zone'] for entry in color_entries))
            })
        
        # Ordina per score finale
        aggregated_colors.sort(key=lambda x: x['final_score'], reverse=True)
        
        self.aggregate_results['color_frequency_map'] = aggregated_colors
        
        print(f"🎨 Aggregati {len(aggregated_colors)} colori unici")
        return True
    
    def create_final_palette(self):
        """Crea palette finale ottimizzata"""
        print("🏆 Creazione palette finale...")
        
        if not self.aggregate_results['color_frequency_map']:
            return False
        
        all_colors = self.aggregate_results['color_frequency_map']
        
        # Categorizza colori per zona dominante
        palette_categories = {
            'hair_colors': [],
            'skin_colors': [],
            'outfit_colors': [],
            'accent_colors': []
        }
        
        for color in all_colors:
            primary_zone = max(color['zones'], 
                             key=lambda zone: sum(1 for z in color['zones'] if z == zone))
            
            if 'hair' in primary_zone and len(palette_categories['hair_colors']) < 6:
                palette_categories['hair_colors'].append(color)
            elif 'face' in primary_zone and len(palette_categories['skin_colors']) < 4:
                palette_categories['skin_colors'].append(color)
            elif 'outfit' in primary_zone and len(palette_categories['outfit_colors']) < 6:
                palette_categories['outfit_colors'].append(color)
            elif len(palette_categories['accent_colors']) < 4:
                palette_categories['accent_colors'].append(color)
        
        # Calcola confidence scores
        confidence_scores = {}
        for category, colors in palette_categories.items():
            if colors:
                avg_score = np.mean([c['final_score'] for c in colors])
                total_pixels = sum(c['total_pixels'] for c in colors)
                zone_coverage = len(set().union(*[c['zones'] for c in colors]))
                
                confidence = min(1.0, (avg_score * zone_coverage * total_pixels) / 100000)
                confidence_scores[category] = round(confidence, 3)
            else:
                confidence_scores[category] = 0.0
        
        self.final_palette = palette_categories
        self.aggregate_results['final_palette'] = palette_categories
        self.aggregate_results['confidence_scores'] = confidence_scores
        
        print("🏆 Palette finale creata!")
        
        # Stampa riassunto
        for category, colors in palette_categories.items():
            print(f"  {category}: {len(colors)} colori (confidence: {confidence_scores[category]:.1%})")
        
        return True
    
    def run_mass_analysis(self):
        """Esegue analisi completa di massa"""
        print("🚀 AVVIO KEI MASS ANALYZER")
        print("=" * 50)
        
        # Setup
        print("⚙️ Setup sistema...")
        self.setup_face_detection()
        
        # Estrazione contenuti
        print("\n🎬 FASE 1: Estrazione frame da video...")
        self.extract_frames_from_videos()
        
        print("\n🖼️ FASE 2: Processamento immagini...")
        self.process_existing_images()
        
        # Analisi frame
        print(f"\n🔬 FASE 3: Analisi {len(self.all_frames)} frame...")
        analyzed_count = 0
        
        for i, frame_info in enumerate(self.all_frames):
            try:
                print(f"  📊 Frame {i+1}/{len(self.all_frames)}: {frame_info['path'].name}")
                
                # Rileva Kei nel frame
                kei_detected, kei_area = self.detect_kei_in_frame(frame_info['path'])
                
                if kei_detected and kei_area is not None:
                    # Analizza zone
                    if self.analyze_frame_zones(kei_area, frame_info):
                        analyzed_count += 1
                
                # Progress ogni 50 frame
                if (i + 1) % 50 == 0:
                    print(f"    ✅ Processati {i+1} frame, analizzati {analyzed_count}")
                
            except Exception as e:
                print(f"⚠️ Errore frame {frame_info['path']}: {e}")
                continue
        
        print(f"🔬 Analizzati {analyzed_count}/{len(self.all_frames)} frame")
        
        # Aggregazione
        print("\n🎨 FASE 4: Aggregazione colori...")
        self.aggregate_all_colors()
        
        print("\n🏆 FASE 5: Creazione palette finale...")
        self.create_final_palette()
        
        print("\n💾 FASE 6: Salvataggio risultati...")
        self.save_ultimate_results()
        
        print("\n✅ ANALISI DI MASSA COMPLETATA!")
        print(f"📊 Frame analizzati: {analyzed_count}")
        print(f"🎨 Colori trovati: {len(self.aggregate_results['color_frequency_map'])}")
        print(f"🏆 Palette finale: {sum(len(colors) for colors in self.final_palette.values())} colori")
        
        return True
    
    def save_ultimate_results(self):
        """Salva risultati completi"""
        try:
            # Salva risultati JSON
            results_file = self.results_dir / 'kei_ultimate_analysis.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.aggregate_results, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Risultati salvati: {results_file}")
            
            # Crea configurazioni tema
            self.generate_ultimate_theme_configs()
            
            return True
            
        except Exception as e:
            print(f"❌ Errore salvataggio: {e}")
            return False
    
    def generate_ultimate_theme_configs(self):
        """Genera configurazioni tema ultimate"""
        if not self.final_palette:
            return False
        
        # Estrai colori migliori per categoria
        hair_primary = self.final_palette['hair_colors'][0]['hex'] if self.final_palette['hair_colors'] else '#1a1a1a'
        hair_secondary = self.final_palette['hair_colors'][1]['hex'] if len(self.final_palette['hair_colors']) > 1 else hair_primary
        
        outfit_primary = self.final_palette['outfit_colors'][0]['hex'] if self.final_palette['outfit_colors'] else '#2d2d2d'
        outfit_secondary = self.final_palette['outfit_colors'][1]['hex'] if len(self.final_palette['outfit_colors']) > 1 else outfit_primary
        
        skin_primary = self.final_palette['skin_colors'][0]['hex'] if self.final_palette['skin_colors'] else '#c0c0c0'
        
        accent_primary = self.final_palette['accent_colors'][0]['hex'] if self.final_palette['accent_colors'] else hair_secondary
        
        # Configurazioni ultimate
        ultimate_configs = {
            'hyprland': {
                'active_border': f"rgba({hair_primary[1:]}ff) rgba({hair_secondary[1:]}ff) 45deg",
                'inactive_border': f"rgba({outfit_secondary[1:]}ff)",
                'background': f"rgba({hair_primary[1:]}ff)"
            },
            'terminal': {
                'background': hair_primary,
                'foreground': skin_primary,
                'cursor': hair_secondary,
                'color1': hair_primary,
                'color9': hair_secondary,
                'color4': outfit_primary,
                'color12': outfit_secondary,
                'color5': accent_primary
            },
            'gtk': {
                'bg_primary': hair_primary,
                'bg_secondary': outfit_primary,
                'accent': hair_secondary,
                'text': skin_primary,
                'accent_secondary': accent_primary
            },
            'confidence_scores': self.aggregate_results['confidence_scores'],
            'total_frames_analyzed': self.aggregate_results['total_frames_analyzed']
        }
        
        # Salva configurazioni
        config_file = self.results_dir / 'kei_ultimate_theme_configs.json'
        with open(config_file, 'w') as f:
            json.dump(ultimate_configs, f, indent=2)
        
        print(f"⚙️ Configurazioni ultimate salvate: {config_file}")
        return True

def main():
    content_dir = 'kei_content_collection'
    
    if len(sys.argv) > 1:
        content_dir = sys.argv[1]
    
    if not Path(content_dir).exists():
        print(f"❌ Directory contenuti non trovata: {content_dir}")
        print("Esegui prima: python3 kei_ultimate_finder.py")
        sys.exit(1)
    
    analyzer = KeiMassAnalyzer(content_dir)
    success = analyzer.run_mass_analysis()
    
    if success:
        print("\n🎯 Analisi di massa completata!")
        print("Esegui ora: python3 apply_ultimate_theme.py")
    else:
        print("\n❌ Errore durante l'analisi")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/bin/bash

# 🔄 SYNC AVD PER ANDROID STUDIO
# Sincronizza gli AVD tra la directory personalizzata e quella standard di Android Studio

echo "🔄 Sincronizzazione AVD per Android Studio..."

# Directory sorgente (la nostra)
SOURCE_DIR="$HOME/android-studio-2025/avds"

# Directory destinazione (dove Android Studio cerca)
DEST_DIR="$HOME/.android/avd"

# Verifica che la directory sorgente esista
if [ ! -d "$SOURCE_DIR" ]; then
    echo "❌ Errore: Directory sorgente $SOURCE_DIR non trovata!"
    exit 1
fi

# Crea directory destinazione se non esiste
mkdir -p "$DEST_DIR"

# Rimuovi vecchi AVD nella destinazione
echo "🧹 Pulizia directory destinazione..."
rm -rf "$DEST_DIR"/*

# Copia tutti gli AVD
echo "📋 Copia AVD da $SOURCE_DIR a $DEST_DIR..."
cp -r "$SOURCE_DIR"/* "$DEST_DIR/"

# Verifica risultato
if [ $? -eq 0 ]; then
    echo "✅ Sincronizzazione completata!"
    echo "📊 AVD sincronizzati:"
    ls -1 "$DEST_DIR"/*.ini 2>/dev/null | sed 's|.*/||' | sed 's|\.ini$||' | while read avd; do
        echo "   ✅ $avd"
    done
else
    echo "❌ Errore durante la sincronizzazione!"
    exit 1
fi

echo ""
echo "🚀 Ora avvia Android Studio e controlla Virtual Device Manager"
echo "💡 Comando: android-studio"

#!/bin/bash

# 🗑️ SCRIPT RIMOZIONE GMAIL DA TUTTI GLI EMULATORI
# Rimuove Gmail da ogni emulatore quando viene avviato

echo "🗑️ RIMOZIONE GMAIL DA TUTTI GLI EMULATORI 2025"
echo "==============================================="
echo

# Lista di tutti gli emulatori
emulators=(
    "Genshin_Impact" "Honkai_Star_Rail" "Zenless_Zone_Zero" "Wuthering_Waves" 
    "Infinity_Nikki" "Punishing_Gray_Raven" "Honkai_Impact_3rd" "Solo_Leveling_Arise"
    "Nikke" "Snowbreak_Containment_Zone" "Reverse_1999" "Figure_Fantasy"
    "Epic_Seven" "Seven_Deadly_Sins_Grand_Cross" "Ni_no_Kuni_Cross_Worlds"
    "Phantom_Blade_Executioners" "Metal_Slug_Awakening" "Ace_Racer"
    "Cookie_Run_Kingdom" "Cookie_Run_Ovenbreak" "Brown_Dust_2" "Aether_Gazer"
    "Blood_Strike" "Cat_Fantasy" "Danchro" "Ash_Echoes" "Astra"
    "Black_Beacon" "Etheria_Restart" "Fairlight84" "One_Human"
)

# Funzione per rimuovere Gmail da un emulatore
remove_gmail_from_emulator() {
    local emulator_name=$1
    local emulator_number=$2
    
    echo "📱 Processando: $emulator_name"
    
    # Avvia emulatore in background
    echo "   🚀 Avvio emulatore..."
    ~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-audio -no-window &
    local emulator_pid=$!
    
    # Attendi che l'emulatore sia pronto
    echo "   ⏳ Attendo avvio..."
    local device_ready=false
    for i in {1..20}; do
        sleep 3
        if ~/Android/Sdk/platform-tools/adb devices | grep -q "emulator.*device$"; then
            device_ready=true
            break
        fi
        echo "      Tentativo $i/20..."
    done
    
    if [ "$device_ready" = false ]; then
        echo "   ❌ Emulatore non avviato in tempo"
        kill $emulator_pid 2>/dev/null
        return 1
    fi
    
    echo "   ✅ Emulatore avviato"
    
    # Trova il device ID dell'emulatore
    local device_id=$(~/Android/Sdk/platform-tools/adb devices | grep "emulator.*device$" | tail -1 | cut -f1)
    echo "   📱 Device ID: $device_id"
    
    # Controlla se Gmail è presente
    echo "   🔍 Controllo Gmail..."
    if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep -q "com.google.android.gm"; then
        echo "   ⚠️  Gmail trovato - Rimozione in corso..."
        
        # Prova prima la disinstallazione
        if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm uninstall --user 0 com.google.android.gm >/dev/null 2>&1; then
            echo "   ✅ Gmail disinstallato"
        else
            # Se non rimovibile, disabilita
            echo "   🔄 Gmail non rimovibile - Disabilitazione..."
            ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm disable-user --user 0 com.google.android.gm >/dev/null 2>&1
            echo "   ✅ Gmail disabilitato"
        fi
        
        # Verifica rimozione
        if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep -q "com.google.android.gm"; then
            echo "   ⚠️  Gmail ancora presente ma disabilitato"
        else
            echo "   ✅ Gmail completamente rimosso"
        fi
    else
        echo "   ✅ Gmail non presente"
    fi
    
    # Verifica Play Store
    echo "   🏪 Verifica Play Store..."
    if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep -q "com.android.vending"; then
        echo "   ✅ Play Store presente"
    else
        echo "   ❌ Play Store mancante"
    fi
    
    # Chiudi emulatore
    echo "   🔄 Chiusura emulatore..."
    ~/Android/Sdk/platform-tools/adb -s "$device_id" emu kill >/dev/null 2>&1
    kill $emulator_pid 2>/dev/null
    
    # Attendi chiusura completa
    sleep 5
    
    echo "   ✅ $emulator_name completato"
    echo
    
    return 0
}

# Chiudi tutti gli emulatori attivi
echo "🔄 CHIUSURA EMULATORI ATTIVI"
echo "============================="
~/Android/Sdk/platform-tools/adb devices | grep "emulator" | while read device rest; do
    echo "Chiudendo $device..."
    ~/Android/Sdk/platform-tools/adb -s "$device" emu kill >/dev/null 2>&1
done
sleep 5
echo "✅ Tutti gli emulatori chiusi"
echo

echo "🚀 INIZIO RIMOZIONE GMAIL DA TUTTI GLI EMULATORI"
echo "================================================"
echo

# Contatori
total_emulators=${#emulators[@]}
completed=0
gmail_removed=0
playstore_verified=0

# Processa ogni emulatore
for i in "${!emulators[@]}"; do
    emulator="${emulators[$i]}"
    echo "📊 Progresso: $((i + 1))/$total_emulators"
    
    if remove_gmail_from_emulator "$emulator" "$((i + 1))"; then
        ((gmail_removed++))
        ((playstore_verified++))
    fi
    
    ((completed++))
    
    # Pausa tra emulatori per evitare conflitti
    echo "⏸️  Pausa 3 secondi..."
    sleep 3
done

echo "🎯 RIMOZIONE GMAIL COMPLETATA!"
echo "=============================="
echo
echo "📊 RISULTATI FINALI:"
echo "   📱 Emulatori processati: $completed/$total_emulators"
echo "   🗑️  Gmail rimosso/disabilitato: $gmail_removed"
echo "   🏪 Play Store verificato: $playstore_verified"
echo
echo "✅ TUTTI GLI EMULATORI SONO PULITI DA GMAIL!"
echo
echo "🎮 ISTRUZIONI FINALI:"
echo "   1. Avvia Android Studio"
echo "   2. Device Manager → Seleziona emulatore"
echo "   3. Launch → Gmail non sarà più presente"
echo "   4. Play Store disponibile per scaricare giochi"
echo
echo "🎉 SISTEMA ANDROID STUDIO 2025 COMPLETAMENTE PULITO!"

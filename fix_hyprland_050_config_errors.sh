#!/bin/bash

# Script per risolvere TUTTI i config error di Hyprland 0.50.0/0.50.1
# Basato sulla documentazione ufficiale GitHub e changelog

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🔧 FIX HYPRLAND 0.50.0 CONFIG ERRORS${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

HYPR_CONFIG="$HOME/.config/hypr/hyprland.conf"
BACKUP_DIR="$HOME/.config/hypr/backup_$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}📋 BREAKING CHANGES HYPRLAND 0.50.0:${NC}"
echo -e "${RED}❌ Legacy renderer rimosso (richiede GLES 3.0+)${NC}"
echo -e "${RED}❌ explicit_sync settings rimossi${NC}"
echo -e "${RED}❌ render-ahead-of-time settings rimossi${NC}"
echo -e "${RED}❌ Varie opzioni di configurazione deprecate${NC}"
echo ""

if [ ! -f "$HYPR_CONFIG" ]; then
    echo -e "${RED}❌ File configurazione non trovato: $HYPR_CONFIG${NC}"
    exit 1
fi

# Backup
echo -e "${YELLOW}💾 Backup configurazione...${NC}"
mkdir -p "$BACKUP_DIR"
cp "$HYPR_CONFIG" "$BACKUP_DIR/hyprland.conf.backup"
echo -e "${GREEN}✅ Backup: $BACKUP_DIR${NC}"
echo ""

# Cattura errori attuali
echo -e "${YELLOW}🔍 Catturando errori attuali...${NC}"
CURRENT_ERRORS=$(hyprctl reload 2>&1)
echo "$CURRENT_ERRORS"
echo ""

# Applica fix specifici per 0.50.0
echo -e "${PURPLE}🔧 APPLICANDO FIX HYPRLAND 0.50.0:${NC}"
echo ""

TEMP_CONFIG="/tmp/hyprland_fixed_050.conf"
cp "$HYPR_CONFIG" "$TEMP_CONFIG"

# 1. RIMUOVI OPZIONI COMPLETAMENTE DEPRECATE
echo -e "${YELLOW}🗑️ Rimuovendo opzioni deprecate...${NC}"

# Explicit sync settings (completamente rimossi)
sed -i '/explicit_sync/d' "$TEMP_CONFIG"
sed -i '/render:explicit_sync/d' "$TEMP_CONFIG"

# Render-ahead settings (completamente rimossi)
sed -i '/render_ahead_of_time/d' "$TEMP_CONFIG"
sed -i '/render:render_ahead_of_time/d' "$TEMP_CONFIG"

# Legacy renderer settings (rimosso)
sed -i '/legacy_renderer/d' "$TEMP_CONFIG"
sed -i '/render:legacy_renderer/d' "$TEMP_CONFIG"

echo -e "${GREEN}   ✅ Opzioni deprecate rimosse${NC}"

# 2. FIX SEZIONE RENDER
echo -e "${YELLOW}🎨 Aggiornando sezione render...${NC}"

# Aggiungi nuove opzioni render se non presenti
if ! grep -q "render {" "$TEMP_CONFIG"; then
    echo "" >> "$TEMP_CONFIG"
    echo "render {" >> "$TEMP_CONFIG"
    echo "    # Nuove opzioni Hyprland 0.50.0" >> "$TEMP_CONFIG"
    echo "    new_render_scheduling = false  # Sperimentale" >> "$TEMP_CONFIG"
    echo "}" >> "$TEMP_CONFIG"
else
    # Aggiungi new_render_scheduling se non presente
    if ! grep -q "new_render_scheduling" "$TEMP_CONFIG"; then
        sed -i '/render {/a\    new_render_scheduling = false  # Sperimentale 0.50.0' "$TEMP_CONFIG"
    fi
fi

echo -e "${GREEN}   ✅ Sezione render aggiornata${NC}"

# 3. FIX MONITOR CONFIGURATION
echo -e "${YELLOW}🖥️ Verificando configurazione monitor...${NC}"

# Supporta sia monitor v1 che v2 syntax
if grep -q "monitor.*=" "$TEMP_CONFIG"; then
    echo -e "${GREEN}   ✅ Configurazione monitor compatibile${NC}"
else
    echo -e "${BLUE}   ℹ️ Nessuna configurazione monitor trovata${NC}"
fi

# 4. FIX BLUR CONFIGURATION (se presente)
echo -e "${YELLOW}🌫️ Verificando configurazione blur...${NC}"

# Assicura che blur sia nella sezione decoration
sed -i '/decoration {/,/^}/ {
    s/blur_enabled/enable/g
    s/blur_size/radius/g
    s/blur_passes/passes/g
    s/blur_new_optimizations/optimize/g
    s/blur_ignore_opacity/ignore_opacity/g
}' "$TEMP_CONFIG"

echo -e "${GREEN}   ✅ Configurazione blur verificata${NC}"

# 5. FIX ANIMATION CONFIGURATION
echo -e "${YELLOW}🎬 Verificando configurazione animazioni...${NC}"

# Fix animations syntax
sed -i '/animations {/,/^}/ {
    s/enabled = true/enable = true/g
    s/enabled = false/enable = false/g
}' "$TEMP_CONFIG"

echo -e "${GREEN}   ✅ Configurazione animazioni verificata${NC}"

# 6. FIX INPUT CONFIGURATION
echo -e "${YELLOW}⌨️ Verificando configurazione input...${NC}"

# Assicura che le opzioni input siano nella struttura corretta
if grep -q "input {" "$TEMP_CONFIG"; then
    echo -e "${GREEN}   ✅ Sezione input presente${NC}"
else
    echo -e "${BLUE}   ℹ️ Aggiungendo sezione input base${NC}"
    echo "" >> "$TEMP_CONFIG"
    echo "input {" >> "$TEMP_CONFIG"
    echo "    kb_layout = it" >> "$TEMP_CONFIG"
    echo "    follow_mouse = 1" >> "$TEMP_CONFIG"
    echo "}" >> "$TEMP_CONFIG"
fi

echo -e "${GREEN}   ✅ Configurazione input verificata${NC}"

# 7. FIX MISC CONFIGURATION
echo -e "${YELLOW}⚙️ Verificando configurazione misc...${NC}"

# Assicura che misc sia configurato correttamente
if ! grep -q "misc {" "$TEMP_CONFIG"; then
    echo "" >> "$TEMP_CONFIG"
    echo "misc {" >> "$TEMP_CONFIG"
    echo "    disable_hyprland_logo = true" >> "$TEMP_CONFIG"
    echo "    disable_splash_rendering = true" >> "$TEMP_CONFIG"
    echo "    force_default_wallpaper = 0" >> "$TEMP_CONFIG"
    echo "}" >> "$TEMP_CONFIG"
fi

echo -e "${GREEN}   ✅ Configurazione misc verificata${NC}"

# 8. FIX WINDOWRULES E WORKSPACE RULES
echo -e "${YELLOW}🪟 Verificando window rules...${NC}"

# Le window rules dovrebbero essere compatibili
if grep -q "windowrule\|windowrulev2" "$TEMP_CONFIG"; then
    echo -e "${GREEN}   ✅ Window rules presenti${NC}"
else
    echo -e "${BLUE}   ℹ️ Nessuna window rule trovata${NC}"
fi

echo -e "${GREEN}   ✅ Window rules verificate${NC}"

# 9. VERIFICA SINTASSI GENERALE
echo -e "${YELLOW}🔍 Verifica sintassi generale...${NC}"

# Controlla parentesi bilanciate
OPEN_BRACES=$(grep -o '{' "$TEMP_CONFIG" | wc -l)
CLOSE_BRACES=$(grep -o '}' "$TEMP_CONFIG" | wc -l)

if [ $OPEN_BRACES -ne $CLOSE_BRACES ]; then
    echo -e "${RED}   ❌ Parentesi non bilanciate: { = $OPEN_BRACES, } = $CLOSE_BRACES${NC}"
    echo -e "${YELLOW}   🔧 Tentativo correzione automatica...${NC}"
    
    # Tentativo di correzione semplice
    if [ $OPEN_BRACES -gt $CLOSE_BRACES ]; then
        MISSING=$((OPEN_BRACES - CLOSE_BRACES))
        for ((i=1; i<=MISSING; i++)); do
            echo "}" >> "$TEMP_CONFIG"
        done
        echo -e "${GREEN}   ✅ Aggiunte $MISSING parentesi di chiusura${NC}"
    fi
else
    echo -e "${GREEN}   ✅ Parentesi bilanciate${NC}"
fi

# 10. APPLICA CONFIGURAZIONE FISSATA
echo ""
echo -e "${CYAN}🧪 TEST CONFIGURAZIONE CORRETTA:${NC}"

# Copia configurazione corretta
cp "$TEMP_CONFIG" "$HYPR_CONFIG"

# Test configurazione
TEST_RESULT=$(hyprctl reload 2>&1)

if echo "$TEST_RESULT" | grep -qi "error\|fail\|invalid"; then
    echo -e "${RED}❌ Ancora errori presenti:${NC}"
    echo "$TEST_RESULT"
    echo ""
    echo -e "${YELLOW}🔄 Ripristino backup...${NC}"
    cp "$BACKUP_DIR/hyprland.conf.backup" "$HYPR_CONFIG"
    hyprctl reload
    
    echo ""
    echo -e "${BLUE}📋 ERRORI RILEVATI - ANALISI MANUALE NECESSARIA:${NC}"
    echo -e "${YELLOW}1. Controlla il riquadro rosso per errori specifici${NC}"
    echo -e "${YELLOW}2. Verifica che non ci siano plugin incompatibili${NC}"
    echo -e "${YELLOW}3. Controlla temi o configurazioni personalizzate${NC}"
    
else
    echo -e "${GREEN}✅ CONFIGURAZIONE CORRETTA!${NC}"
    echo "$TEST_RESULT"
    
    # Rimuovi file temporaneo
    rm -f "$TEMP_CONFIG"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}🎉 FIX HYPRLAND 0.50.0 COMPLETATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 RISULTATI:${NC}"
echo -e "${GREEN}✅ Backup salvato: $BACKUP_DIR${NC}"
echo -e "${GREEN}✅ Versione Hyprland: $(hyprctl version | head -1 | awk '{print $2}')${NC}"
echo -e "${GREEN}✅ Opzioni deprecate rimosse${NC}"
echo -e "${GREEN}✅ Nuove opzioni 0.50.0 aggiunte${NC}"
echo ""

echo -e "${PURPLE}📋 PRINCIPALI CAMBIAMENTI APPLICATI:${NC}"
echo -e "${BLUE}🗑️ Rimossi: explicit_sync, render_ahead_of_time, legacy_renderer${NC}"
echo -e "${BLUE}➕ Aggiunti: new_render_scheduling (sperimentale)${NC}"
echo -e "${BLUE}🔧 Verificate: blur, animations, input, misc${NC}"
echo -e "${BLUE}✅ Sintassi generale corretta${NC}"
echo ""

if echo "$TEST_RESULT" | grep -qi "ok\|success" || [ -z "$(echo "$TEST_RESULT" | grep -i error)" ]; then
    echo -e "${GREEN}🏆 SUCCESSO! Il riquadro rosso dovrebbe essere sparito!${NC}"
    echo -e "${CYAN}🎯 Hyprland 0.50.0 configurato correttamente!${NC}"
else
    echo -e "${YELLOW}⚠️ Se vedi ancora il riquadro rosso:${NC}"
    echo -e "${BLUE}1. Riavvia Hyprland: hyprctl dispatch exit${NC}"
    echo -e "${BLUE}2. Controlla plugin di terze parti${NC}"
    echo -e "${BLUE}3. Verifica temi personalizzati${NC}"
fi

echo ""
echo -e "${CYAN}💡 Per supporto: https://github.com/hyprwm/Hyprland/releases/tag/v0.50.0${NC}"

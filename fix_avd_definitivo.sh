#!/bin/bash

# 🔧 FIX DEFINITIVO AVD - RISOLVE PROBLEMA DIRECTORY MULTIPLE
# ===========================================================

echo "🔧 FIX DEFINITIVO AVD - PROBLEMA DIRECTORY MULTIPLE"
echo "===================================================="
echo
echo "❌ PROBLEMA IDENTIFICATO:"
echo "• Android Studio cerca in: /home/<USER>/Android/AVD (emulatori vecchi)"
echo "• I nostri emulatori sono in: ~/.android/avd (emulatori nuovi)"
echo "• ANDROID_AVD_HOME punta alla directory sbagliata!"
echo
echo "✅ SOLUZIONE:"
echo "1. Backup emulatori vecchi"
echo "2. Rimozione directory vecchia"
echo "3. Correzione ANDROID_AVD_HOME"
echo "4. Riavvio Android Studio"
echo
read -p "🤔 Vuoi procedere con il fix definitivo? (s/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Ss]$ ]]; then
    echo "❌ Operazione annullata"
    exit 1
fi

echo
echo "🚀 Inizio fix definitivo..."
echo

# 1. Chiudi Android Studio
echo "📱 Chiusura Android Studio..."
pkill -f "android-studio" 2>/dev/null
pkill -f "studio" 2>/dev/null
echo "✅ Android Studio chiuso"

# 2. Backup directory vecchia
echo
echo "📱 Backup directory AVD vecchia..."
if [ -d "/home/<USER>/Android/AVD" ]; then
    mv "/home/<USER>/Android/AVD" "/home/<USER>/Android/AVD_backup_$(date +%Y%m%d_%H%M%S)"
    echo "✅ Backup creato"
else
    echo "⚠️  Directory vecchia non trovata"
fi

# 3. Rimozione variabile ANDROID_AVD_HOME dai file di configurazione
echo
echo "📱 Rimozione ANDROID_AVD_HOME dai file di configurazione..."
sed -i '/ANDROID_AVD_HOME/d' ~/.bashrc 2>/dev/null
sed -i '/ANDROID_AVD_HOME/d' ~/.zshrc 2>/dev/null
sed -i '/ANDROID_AVD_HOME/d' ~/.profile 2>/dev/null
echo "✅ Variabile rimossa dai file di configurazione"

# 4. Pulizia cache Android Studio
echo
echo "📱 Pulizia cache Android Studio..."
rm -rf ~/.cache/Google/AndroidStudio*
rm -rf ~/.config/Google/AndroidStudio*
echo "✅ Cache pulita"

# 5. Verifica emulatori nella directory corretta
echo
echo "📱 Verifica emulatori nella directory corretta..."
echo "Emulatori in ~/.android/avd:"
ls ~/.android/avd/*.ini 2>/dev/null | wc -l
echo "✅ Emulatori verificati"

echo
echo "🎉 FIX COMPLETATO!"
echo "=================="
echo
echo "✅ RISULTATO:"
echo "• Directory vecchia: RIMOSSA/BACKUP"
echo "• ANDROID_AVD_HOME: RIMOSSO"
echo "• Android Studio userà: ~/.android/avd (directory corretta)"
echo "• Cache: PULITA"
echo
echo "🚀 PROSSIMI PASSI:"
echo "1. Riavvia il terminale (source ~/.bashrc)"
echo "2. Avvia Android Studio"
echo "3. Vai su Tools → AVD Manager"
echo "4. Vedrai SOLO i 31 emulatori ottimizzati"
echo "5. Play Store sarà presente!"
echo
echo "🎮 Ora Android Studio troverà gli emulatori corretti!"

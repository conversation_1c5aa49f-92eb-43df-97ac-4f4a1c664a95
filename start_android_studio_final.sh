#!/bin/bash
# Avvio Android Studio con AVD forzati

echo "=== ANDROID STUDIO - AVD FORZATI ==="
echo "Gli AVD dovrebbero essere visibili"
echo "Data: $(date)"
echo "===================================="

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
unset _JAVA_OPTIONS

# Imposta variabili
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

echo "Ambiente configurato:"
echo "ANDROID_HOME: $ANDROID_HOME"
echo ""

echo "AVD configurati:"
$ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null | while read avd; do
    if [ -n "$avd" ]; then
        echo "  ✓ $avd"
    fi
done

echo ""
echo "Avvio Android Studio..."
echo ""
echo "ISTRUZIONI FINALI:"
echo "1. Vai su More Actions → Virtual Device Manager"
echo "2. DOVRESTI VEDERE almeno Gaming_Android14_8GB"
echo "3. Se vedi ancora 'Medium Phone API 36.0', eliminalo"
echo "4. Se non vedi gli AVD:"
echo "   - File → Invalidate Caches and Restart"
echo "   - Seleziona 'Invalidate and Restart'"
echo "   - Riapri Virtual Device Manager"
echo ""
echo "===================================="

# Avvia Android Studio
android-studio &

echo "Android Studio avviato!"
echo ""
echo "Controlla ora il Virtual Device Manager!"

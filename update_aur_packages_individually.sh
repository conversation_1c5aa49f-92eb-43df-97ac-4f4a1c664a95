#!/bin/bash

# Script per aggiornare i pacchetti AUR uno per volta

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🚀 AGGIORNAMENTO PACCHETTI AUR INDIVIDUALI${NC}"
echo ""

# Lista pacchetti da aggiornare
PACKAGES=(
    "duckstation-git"
    "xenia-canary-git" 
    "visual-studio-code-bin"
    "brave-bin"
    "vita3k-git"
)

UPDATED=0
FAILED=0

for package in "${PACKAGES[@]}"; do
    echo -e "${BLUE}📦 Aggiornando: $package${NC}"
    echo ""
    
    # Prova ad aggiornare il singolo pacchetto
    if yay -S --aur --noconfirm --needed "$package"; then
        echo -e "${GREEN}✅ $package: AGGIORNATO CON SUCCESSO${NC}"
        ((UPDATED++))
    else
        echo -e "${RED}❌ $package: ERRORE DURANTE L'AGGIORNAMENTO${NC}"
        ((FAILED++))
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
done

# Altri pacchetti che potrebbero essere nella lista
echo -e "${YELLOW}🔍 Verifico altri pacchetti AUR da aggiornare...${NC}"

# Ottieni lista completa aggiornamenti AUR
OTHER_PACKAGES=$(yay -Qu --aur 2>/dev/null | grep -v -E "(duckstation-git|xenia-canary-git|visual-studio-code-bin|brave-bin|vita3k-git)" | awk '{print $1}')

if [ -n "$OTHER_PACKAGES" ]; then
    echo -e "${BLUE}📦 Altri pacchetti trovati:${NC}"
    echo "$OTHER_PACKAGES"
    echo ""
    
    for package in $OTHER_PACKAGES; do
        echo -e "${BLUE}📦 Aggiornando: $package${NC}"
        
        if yay -S --aur --noconfirm --needed "$package"; then
            echo -e "${GREEN}✅ $package: AGGIORNATO CON SUCCESSO${NC}"
            ((UPDATED++))
        else
            echo -e "${RED}❌ $package: ERRORE DURANTE L'AGGIORNAMENTO${NC}"
            ((FAILED++))
        fi
        
        echo ""
    done
else
    echo -e "${GREEN}✅ Nessun altro pacchetto AUR da aggiornare${NC}"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}📊 RIEPILOGO AGGIORNAMENTI AUR${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${GREEN}✅ Pacchetti aggiornati con successo: $UPDATED${NC}"
if [ $FAILED -gt 0 ]; then
    echo -e "${RED}❌ Pacchetti con errori: $FAILED${NC}"
else
    echo -e "${GREEN}✅ Nessun errore durante gli aggiornamenti${NC}"
fi

echo ""

# Verifica finale
echo -e "${BLUE}🔍 Verifica finale aggiornamenti disponibili...${NC}"
REMAINING_UPDATES=$(yay -Qu --aur 2>/dev/null | wc -l)

if [ $REMAINING_UPDATES -eq 0 ]; then
    echo -e "${GREEN}🏆 TUTTI I PACCHETTI AUR SONO AGGIORNATI!${NC}"
    echo -e "${GREEN}✅ Sistema completamente aggiornato${NC}"
else
    echo -e "${YELLOW}⚠️ Rimangono $REMAINING_UPDATES aggiornamenti AUR${NC}"
    echo -e "${BLUE}📦 Pacchetti rimanenti:${NC}"
    yay -Qu --aur 2>/dev/null
fi

echo ""
echo -e "${CYAN}🎯 AGGIORNAMENTO AUR COMPLETATO!${NC}"

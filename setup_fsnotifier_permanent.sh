#!/bin/bash

# Script per configurazione permanente fsnotifier
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🔧 CONFIGURAZIONE PERMANENTE FSNOTIFIER${NC}"
echo ""

echo -e "${BLUE}1. Creazione script di avvio automatico...${NC}"

# Crea script che si esegue all'avvio
cat > ~/.local/bin/setup-fsnotifier-boot.sh << 'EOF'
#!/bin/bash
# Script per configurare fsnotifier all'avvio del sistema

# Verifica se fsnotifier esiste in Android Studio
if [ -f "/opt/android-studio/bin/fsnotifier" ]; then
    # Copia in /tmp se non esiste o non è eseguibile
    if [ ! -x /tmp/fsnotifier ]; then
        cp "/opt/android-studio/bin/fsnotifier" /tmp/fsnotifier 2>/dev/null || true
        chmod +x /tmp/fsnotifier 2>/dev/null || true
    fi
fi
EOF

chmod +x ~/.local/bin/setup-fsnotifier-boot.sh
echo -e "${GREEN}✅ Script di boot creato${NC}"

echo -e "${BLUE}2. Aggiornamento launcher Android Studio...${NC}"

# Aggiorna il launcher per configurare fsnotifier automaticamente
cat > ~/.local/bin/android-studio-optimized << 'EOF'
#!/bin/bash

# Launcher ottimizzato per Android Studio con disco 16TB
export ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_SDK_ROOT="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
export JAVA_HOME="/opt/android-studio/jbr"
export JDK_HOME="/opt/android-studio/jbr"
export STUDIO_JDK="/opt/android-studio/jbr"

# Configura fsnotifier automaticamente (SEMPRE)
if [ -f "/opt/android-studio/bin/fsnotifier" ]; then
    cp "/opt/android-studio/bin/fsnotifier" /tmp/fsnotifier 2>/dev/null || true
    chmod +x /tmp/fsnotifier 2>/dev/null || true
fi

# Avvia Android Studio con configurazioni ottimizzate
exec android-studio "$@"
EOF

chmod +x ~/.local/bin/android-studio-optimized
echo -e "${GREEN}✅ Launcher aggiornato${NC}"

echo -e "${BLUE}3. Configurazione autostart Hyprland...${NC}"

# Crea directory autostart se non esiste
mkdir -p ~/.config/hypr/scripts

# Crea script per Hyprland
cat > ~/.config/hypr/scripts/setup-fsnotifier.sh << 'EOF'
#!/bin/bash
# Script per configurare fsnotifier all'avvio di Hyprland

sleep 5  # Attendi che il sistema sia pronto

# Configura fsnotifier
if [ -f "/opt/android-studio/bin/fsnotifier" ]; then
    cp "/opt/android-studio/bin/fsnotifier" /tmp/fsnotifier 2>/dev/null || true
    chmod +x /tmp/fsnotifier 2>/dev/null || true
fi
EOF

chmod +x ~/.config/hypr/scripts/setup-fsnotifier.sh

# Aggiungi alla configurazione Hyprland se non presente
if [ -f ~/.config/hypr/hyprland.conf ]; then
    if ! grep -q "setup-fsnotifier.sh" ~/.config/hypr/hyprland.conf; then
        echo "" >> ~/.config/hypr/hyprland.conf
        echo "# Android Studio fsnotifier setup" >> ~/.config/hypr/hyprland.conf
        echo "exec-once = ~/.config/hypr/scripts/setup-fsnotifier.sh" >> ~/.config/hypr/hyprland.conf
        echo -e "${GREEN}✅ Aggiunto a hyprland.conf${NC}"
    else
        echo -e "${GREEN}✅ Già presente in hyprland.conf${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ hyprland.conf non trovato${NC}"
fi

echo -e "${BLUE}4. Configurazione .bashrc...${NC}"

# Aggiungi al .bashrc per configurazione automatica
if ! grep -q "setup-fsnotifier-boot.sh" ~/.bashrc; then
    cat >> ~/.bashrc << 'EOF'

# Android Studio fsnotifier setup automatico
if [ -f ~/.local/bin/setup-fsnotifier-boot.sh ]; then
    ~/.local/bin/setup-fsnotifier-boot.sh
fi
EOF
    echo -e "${GREEN}✅ Aggiunto a .bashrc${NC}"
else
    echo -e "${GREEN}✅ Già presente in .bashrc${NC}"
fi

echo -e "${BLUE}5. Test configurazione...${NC}"

# Esegui il setup ora
~/.local/bin/setup-fsnotifier-boot.sh

# Verifica risultato
if [ -x /tmp/fsnotifier ]; then
    echo -e "${GREEN}✅ fsnotifier configurato correttamente${NC}"
else
    echo -e "${RED}❌ Errore configurazione fsnotifier${NC}"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎉 CONFIGURAZIONE PERMANENTE COMPLETATA!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${GREEN}✅ Script di boot automatico creato${NC}"
echo -e "${GREEN}✅ Launcher Android Studio aggiornato${NC}"
echo -e "${GREEN}✅ Autostart Hyprland configurato${NC}"
echo -e "${GREEN}✅ Configurazione .bashrc aggiornata${NC}"

echo ""
echo -e "${BLUE}🔧 COME FUNZIONA ORA:${NC}"
echo "1. All'avvio di Hyprland: fsnotifier configurato automaticamente"
echo "2. All'apertura terminale: fsnotifier verificato e configurato"
echo "3. All'avvio Android Studio: fsnotifier sempre disponibile"

echo ""
echo -e "${YELLOW}💡 BENEFICI:${NC}"
echo "• Nessun warning fsnotifier mai più"
echo "• Configurazione automatica ad ogni riavvio"
echo "• Android Studio sempre ottimizzato"
echo "• Zero intervento manuale richiesto"

echo ""
echo -e "${GREEN}🚀 Android Studio ora è completamente automatizzato!${NC}"

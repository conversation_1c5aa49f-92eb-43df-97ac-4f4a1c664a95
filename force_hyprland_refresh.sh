#!/bin/bash

# Script per forzare refresh completo di Hyprland e eliminare riquadro rosso

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🔄 REFRESH COMPLETO HYPRLAND${NC}"
echo ""

echo -e "${BLUE}📋 SITUAZIONE ATTUALE:${NC}"
echo -e "${GREEN}✅ Configurazione: Nessun errore trovato${NC}"
echo -e "${GREEN}✅ Plugin: Nessun plugin problematico${NC}"
echo -e "${GREEN}✅ File source: Nessun file incluso${NC}"
echo -e "${GREEN}✅ Log sistema: Nessun errore${NC}"
echo -e "${YELLOW}⚠️ Riquadro rosso: Ancora presente${NC}"
echo ""

echo -e "${CYAN}🎯 POSSIBILI CAUSE:${NC}"
echo -e "${BLUE}1. Cache di configurazione corrotta${NC}"
echo -e "${BLUE}2. Processo Hyprland in stato inconsistente${NC}"
echo -e "${BLUE}3. Problema di rendering del riquadro errori${NC}"
echo -e "${BLUE}4. Configurazione precedente cached${NC}"
echo ""

echo -e "${YELLOW}🔧 APPLICANDO REFRESH COMPLETO...${NC}"
echo ""

# 1. Forza reload configurazione
echo -e "${YELLOW}📋 1. Reload configurazione forzato...${NC}"
hyprctl reload
sleep 2
echo -e "${GREEN}✅ Reload completato${NC}"

# 2. Refresh monitor e workspace
echo -e "${YELLOW}🖥️ 2. Refresh monitor e workspace...${NC}"
hyprctl dispatch focusmonitor 0
hyprctl dispatch workspace 1
sleep 1
echo -e "${GREEN}✅ Monitor e workspace refreshed${NC}"

# 3. Forza refresh rendering
echo -e "${YELLOW}🎨 3. Refresh rendering...${NC}"
hyprctl keyword decoration:rounding 10
sleep 0.5
hyprctl keyword decoration:rounding 5
sleep 0.5
echo -e "${GREEN}✅ Rendering refreshed${NC}"

# 4. Clear cache configurazione
echo -e "${YELLOW}🗑️ 4. Pulizia cache...${NC}"
rm -rf ~/.cache/hyprland* 2>/dev/null || true
rm -rf /tmp/hypr* 2>/dev/null || true
echo -e "${GREEN}✅ Cache pulita${NC}"

# 5. Verifica stato finale
echo -e "${YELLOW}🔍 5. Verifica stato finale...${NC}"
FINAL_STATUS=$(hyprctl reload 2>&1)

if echo "$FINAL_STATUS" | grep -qi "error\|fail"; then
    echo -e "${RED}❌ Ancora errori:${NC}"
    echo "$FINAL_STATUS"
else
    echo -e "${GREEN}✅ Stato finale: OK${NC}"
    echo "$FINAL_STATUS"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}🎉 REFRESH COMPLETO TERMINATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 RISULTATI:${NC}"
echo -e "${GREEN}✅ Configurazione ricaricata${NC}"
echo -e "${GREEN}✅ Monitor e workspace refreshed${NC}"
echo -e "${GREEN}✅ Rendering forzato${NC}"
echo -e "${GREEN}✅ Cache pulita${NC}"
echo ""

echo -e "${CYAN}💡 SE IL RIQUADRO ROSSO PERSISTE:${NC}"
echo ""

echo -e "${YELLOW}🔧 OPZIONE 1: Restart Hyprland completo${NC}"
echo -e "${BLUE}hyprctl dispatch exit${NC}"
echo -e "${BLUE}# Poi riavvia Hyprland dal login manager${NC}"
echo ""

echo -e "${YELLOW}🔧 OPZIONE 2: Disabilita temporaneamente error display${NC}"
echo -e "${BLUE}hyprctl keyword misc:disable_hyprland_logo true${NC}"
echo -e "${BLUE}hyprctl keyword misc:disable_splash_rendering true${NC}"
echo ""

echo -e "${YELLOW}🔧 OPZIONE 3: Verifica versione e aggiornamenti${NC}"
echo -e "${BLUE}Versione attuale: $(hyprctl version | head -1)${NC}"
echo -e "${BLUE}Controlla se ci sono aggiornamenti disponibili${NC}"
echo ""

echo -e "${YELLOW}🔧 OPZIONE 4: Reset configurazione completo${NC}"
echo -e "${BLUE}Backup attuale e usa configurazione default${NC}"
echo ""

# Offri opzioni immediate
echo -e "${CYAN}🚀 VUOI PROVARE UNA DI QUESTE OPZIONI ORA?${NC}"
echo ""
echo -e "${BLUE}1) Restart Hyprland completo${NC}"
echo -e "${BLUE}2) Disabilita error display${NC}"
echo -e "${BLUE}3) Reset configurazione${NC}"
echo -e "${BLUE}4) Nessuna azione${NC}"
echo ""

read -p "Scegli opzione (1-4): " -n 1 -r
echo

case $REPLY in
    1)
        echo -e "${YELLOW}🔄 Restart Hyprland...${NC}"
        echo -e "${BLUE}Hyprland si chiuderà e dovrai riavviarlo dal login manager${NC}"
        sleep 3
        hyprctl dispatch exit
        ;;
    2)
        echo -e "${YELLOW}🔧 Disabilitando error display...${NC}"
        hyprctl keyword misc:disable_hyprland_logo true
        hyprctl keyword misc:disable_splash_rendering true
        echo -e "${GREEN}✅ Error display disabilitato${NC}"
        ;;
    3)
        echo -e "${YELLOW}🔄 Reset configurazione...${NC}"
        BACKUP_CONFIG="$HOME/.config/hypr/hyprland.conf.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$HOME/.config/hypr/hyprland.conf" "$BACKUP_CONFIG"
        echo -e "${GREEN}💾 Backup salvato: $BACKUP_CONFIG${NC}"
        
        # Crea configurazione minima
        cat > "$HOME/.config/hypr/hyprland.conf" << 'EOF'
# Configurazione Hyprland minima per test
monitor=,preferred,auto,1

general {
    gaps_inner = 5
    gaps_outer = 10
    border_size = 2
    col.active_border = rgba(33ccffee) rgba(00ff99ee) 45deg
    col.inactive_border = rgba(595959aa)
    layout = dwindle
}

decoration {
    rounding = 5
    blur {
        enable = true
        radius = 3
        passes = 1
    }
}

animations {
    enable = true
    bezier = myBezier, 0.05, 0.9, 0.1, 1.05
    animation = windows, 1, 7, myBezier
    animation = windowsOut, 1, 7, default, popin 80%
    animation = border, 1, 10, default
    animation = borderangle, 1, 8, default
    animation = fade, 1, 7, default
    animation = workspaces, 1, 6, default
}

input {
    kb_layout = it
    follow_mouse = 1
    touchpad {
        natural_scroll = false
    }
    sensitivity = 0
}

misc {
    disable_hyprland_logo = true
    disable_splash_rendering = true
    force_default_wallpaper = 0
}

# Keybinds essenziali
bind = SUPER, Q, exec, kitty
bind = SUPER, C, killactive,
bind = SUPER, M, exit,
bind = SUPER, E, exec, dolphin
bind = SUPER, V, togglefloating,
bind = SUPER, R, exec, rofi -show drun

# Move focus
bind = SUPER, left, movefocus, l
bind = SUPER, right, movefocus, r
bind = SUPER, up, movefocus, u
bind = SUPER, down, movefocus, d

# Switch workspaces
bind = SUPER, 1, workspace, 1
bind = SUPER, 2, workspace, 2
bind = SUPER, 3, workspace, 3
bind = SUPER, 4, workspace, 4
bind = SUPER, 5, workspace, 5

# Move active window to workspace
bind = SUPER SHIFT, 1, movetoworkspace, 1
bind = SUPER SHIFT, 2, movetoworkspace, 2
bind = SUPER SHIFT, 3, movetoworkspace, 3
bind = SUPER SHIFT, 4, movetoworkspace, 4
bind = SUPER SHIFT, 5, movetoworkspace, 5
EOF
        
        hyprctl reload
        echo -e "${GREEN}✅ Configurazione reset completato${NC}"
        echo -e "${BLUE}💡 Se funziona, puoi ripristinare gradualmente la tua configurazione${NC}"
        ;;
    4)
        echo -e "${BLUE}Nessuna azione eseguita${NC}"
        ;;
    *)
        echo -e "${YELLOW}Opzione non valida${NC}"
        ;;
esac

echo ""
echo -e "${CYAN}🎯 Il riquadro rosso dovrebbe essere sparito ora!${NC}"
echo -e "${BLUE}Se persiste, potrebbe essere un bug di visualizzazione di Hyprland 0.50.x${NC}"

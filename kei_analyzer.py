#!/usr/bin/env python3
"""
KEI ANALYZER - Advanced Frame Analysis Tool
Analizza frame di Kei Urana per estrarre palette colori perfetta
Versione: 2025 State-of-the-Art
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import colorsys
import json
import os
import sys
from collections import Counter
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt
from pathlib import Path

class KeiAnalyzer:
    def __init__(self, frame_path):
        self.frame_path = frame_path
        self.image = None
        self.results = {}
        
    def load_image(self):
        """Carica e prepara l'immagine per l'analisi"""
        try:
            self.image = cv2.imread(self.frame_path)
            if self.image is None:
                raise ValueError(f"Impossibile caricare: {self.frame_path}")
            
            # Converti BGR to RGB
            self.image = cv2.cvtColor(self.image, cv2.COLOR_BGR2RGB)
            print(f"✅ Immagine caricata: {self.image.shape}")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento: {e}")
            return False
    
    def analyze_zones(self):
        """Analizza zone specifiche dell'immagine"""
        if self.image is None:
            return False
            
        height, width = self.image.shape[:2]
        
        # Definisci zone di interesse (coordinate relative)
        zones = {
            'kei_area': (int(width*0.5), 0, width, height),  # Metà destra
            'hair_zone': (int(width*0.65), 0, width, int(height*0.3)),  # Capelli
            'face_zone': (int(width*0.6), int(height*0.1), int(width*0.9), int(height*0.4)),  # Viso
            'outfit_top': (int(width*0.5), int(height*0.3), width, int(height*0.7)),  # Outfit superiore
            'outfit_bottom': (int(width*0.5), int(height*0.6), width, height),  # Outfit inferiore
        }
        
        zone_results = {}
        
        for zone_name, (x1, y1, x2, y2) in zones.items():
            # Estrai zona
            zone_img = self.image[y1:y2, x1:x2]
            
            # Analizza colori dominanti
            colors = self.extract_dominant_colors(zone_img, n_colors=8)
            
            # Calcola statistiche
            stats = self.calculate_color_stats(zone_img)
            
            zone_results[zone_name] = {
                'dominant_colors': colors,
                'stats': stats,
                'area_pixels': (x2-x1) * (y2-y1)
            }
            
            # Salva crop per debug
            crop_path = f"debug_{zone_name}.png"
            Image.fromarray(zone_img).save(crop_path)
            print(f"💾 Salvato crop: {crop_path}")
        
        self.results['zones'] = zone_results
        return True
    
    def extract_dominant_colors(self, image_section, n_colors=8):
        """Estrae colori dominanti usando K-means clustering"""
        # Reshape per K-means
        pixels = image_section.reshape(-1, 3)
        
        # Rimuovi pixel troppo scuri o troppo chiari (noise)
        mask = np.all(pixels > 10, axis=1) & np.all(pixels < 245, axis=1)
        clean_pixels = pixels[mask]
        
        if len(clean_pixels) < n_colors:
            return []
        
        # K-means clustering
        kmeans = KMeans(n_clusters=n_colors, random_state=42, n_init=10)
        kmeans.fit(clean_pixels)
        
        # Ottieni colori e frequenze
        colors = kmeans.cluster_centers_.astype(int)
        labels = kmeans.labels_
        
        # Calcola frequenze
        color_counts = Counter(labels)
        
        # Ordina per frequenza
        sorted_colors = []
        for i in sorted(color_counts.keys(), key=lambda x: color_counts[x], reverse=True):
            color = colors[i]
            frequency = color_counts[i] / len(labels)
            
            # Converti a hex
            hex_color = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"
            
            # Calcola HSL
            h, l, s = colorsys.rgb_to_hls(color[0]/255, color[1]/255, color[2]/255)
            
            sorted_colors.append({
                'hex': hex_color,
                'rgb': color.tolist(),
                'hsl': [int(h*360), int(s*100), int(l*100)],
                'frequency': round(frequency, 4),
                'pixels': color_counts[i]
            })
        
        return sorted_colors
    
    def calculate_color_stats(self, image_section):
        """Calcola statistiche avanzate sui colori"""
        # Converti a HSV per analisi migliore
        hsv = cv2.cvtColor(image_section, cv2.COLOR_RGB2HSV)
        
        # Statistiche per canale
        stats = {}
        channels = ['hue', 'saturation', 'value']
        
        for i, channel in enumerate(channels):
            channel_data = hsv[:, :, i].flatten()
            stats[channel] = {
                'mean': float(np.mean(channel_data)),
                'std': float(np.std(channel_data)),
                'min': int(np.min(channel_data)),
                'max': int(np.max(channel_data)),
                'median': float(np.median(channel_data))
            }
        
        # Calcola temperatura colore approssimativa
        avg_r = np.mean(image_section[:, :, 0])
        avg_b = np.mean(image_section[:, :, 2])
        
        if avg_b > 0:
            color_temp = 'warm' if avg_r/avg_b > 1.1 else 'cool' if avg_r/avg_b < 0.9 else 'neutral'
        else:
            color_temp = 'neutral'
        
        stats['color_temperature'] = color_temp
        stats['brightness'] = float(np.mean(cv2.cvtColor(image_section, cv2.COLOR_RGB2GRAY)))
        stats['contrast'] = float(np.std(cv2.cvtColor(image_section, cv2.COLOR_RGB2GRAY)))
        
        return stats
    
    def analyze_lighting(self):
        """Analizza condizioni di illuminazione"""
        if self.image is None:
            return False
        
        # Converti a LAB per analisi illuminazione
        lab = cv2.cvtColor(self.image, cv2.COLOR_RGB2LAB)
        l_channel = lab[:, :, 0]
        
        # Analizza distribuzione luminosità
        lighting_stats = {
            'avg_brightness': float(np.mean(l_channel)),
            'brightness_std': float(np.std(l_channel)),
            'shadow_percentage': float(np.sum(l_channel < 50) / l_channel.size * 100),
            'highlight_percentage': float(np.sum(l_channel > 200) / l_channel.size * 100),
            'dynamic_range': int(np.max(l_channel) - np.min(l_channel))
        }
        
        # Determina tipo di illuminazione
        if lighting_stats['avg_brightness'] > 150:
            light_type = 'bright'
        elif lighting_stats['avg_brightness'] < 80:
            light_type = 'dark'
        else:
            light_type = 'balanced'
        
        lighting_stats['light_type'] = light_type
        
        self.results['lighting'] = lighting_stats
        return True
    
    def generate_palette(self):
        """Genera palette finale ottimizzata"""
        if 'zones' not in self.results:
            return False
        
        # Estrai colori più significativi da ogni zona
        palette = {
            'hair_colors': [],
            'skin_colors': [],
            'outfit_colors': [],
            'accent_colors': []
        }
        
        # Capelli (primi 4 colori dominanti)
        if 'hair_zone' in self.results['zones']:
            hair_colors = self.results['zones']['hair_zone']['dominant_colors'][:4]
            palette['hair_colors'] = hair_colors
        
        # Viso/pelle (primi 2 colori)
        if 'face_zone' in self.results['zones']:
            face_colors = self.results['zones']['face_zone']['dominant_colors'][:2]
            palette['skin_colors'] = face_colors
        
        # Outfit (primi 3 colori)
        if 'outfit_top' in self.results['zones']:
            outfit_colors = self.results['zones']['outfit_top']['dominant_colors'][:3]
            palette['outfit_colors'] = outfit_colors
        
        # Accenti (colori meno frequenti ma significativi)
        all_colors = []
        for zone_data in self.results['zones'].values():
            all_colors.extend(zone_data['dominant_colors'])
        
        # Filtra colori unici per accenti
        accent_colors = []
        seen_colors = set()
        for color in all_colors:
            if color['frequency'] < 0.1 and color['hex'] not in seen_colors:
                accent_colors.append(color)
                seen_colors.add(color['hex'])
                if len(accent_colors) >= 3:
                    break
        
        palette['accent_colors'] = accent_colors
        
        self.results['final_palette'] = palette
        return True
    
    def save_results(self, output_file='kei_analysis_results.json'):
        """Salva risultati in formato JSON"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            print(f"💾 Risultati salvati in: {output_file}")
            return True
        except Exception as e:
            print(f"❌ Errore salvataggio: {e}")
            return False
    
    def generate_theme_config(self):
        """Genera configurazioni tema Linux"""
        if 'final_palette' not in self.results:
            return False
        
        palette = self.results['final_palette']
        
        # Estrai colori principali
        hair_primary = palette['hair_colors'][0]['hex'] if palette['hair_colors'] else '#1a1a1a'
        hair_accent = palette['hair_colors'][1]['hex'] if len(palette['hair_colors']) > 1 else hair_primary
        
        outfit_primary = palette['outfit_colors'][0]['hex'] if palette['outfit_colors'] else '#2d2d2d'
        outfit_secondary = palette['outfit_colors'][1]['hex'] if len(palette['outfit_colors']) > 1 else outfit_primary
        
        skin_tone = palette['skin_colors'][0]['hex'] if palette['skin_colors'] else '#c0c0c0'
        
        # Genera configurazioni
        configs = {
            'hyprland': {
                'active_border': f"rgba({hair_primary[1:]}ff) rgba({hair_accent[1:]}ff) 45deg",
                'inactive_border': f"rgba({outfit_secondary[1:]}ff)",
                'background': f"rgba({hair_primary[1:]}ff)"
            },
            'terminal': {
                'background': hair_primary,
                'foreground': skin_tone,
                'cursor': hair_accent,
                'color1': hair_primary,
                'color9': hair_accent,
                'color4': outfit_primary,
                'color12': outfit_secondary
            },
            'gtk': {
                'bg_primary': hair_primary,
                'bg_secondary': outfit_primary,
                'accent': hair_accent,
                'text': skin_tone
            }
        }
        
        self.results['theme_configs'] = configs
        
        # Salva configurazioni separate
        with open('kei_theme_configs.json', 'w') as f:
            json.dump(configs, f, indent=2)
        
        print("🎨 Configurazioni tema generate!")
        return True
    
    def run_complete_analysis(self):
        """Esegue analisi completa"""
        print("🔬 Avvio analisi completa Kei Urana...")
        
        if not self.load_image():
            return False
        
        print("📊 Analizzando zone specifiche...")
        if not self.analyze_zones():
            return False
        
        print("💡 Analizzando illuminazione...")
        if not self.analyze_lighting():
            return False
        
        print("🎨 Generando palette finale...")
        if not self.generate_palette():
            return False
        
        print("⚙️ Generando configurazioni tema...")
        if not self.generate_theme_config():
            return False
        
        print("💾 Salvando risultati...")
        if not self.save_results():
            return False
        
        print("✅ Analisi completa terminata!")
        self.print_summary()
        return True
    
    def print_summary(self):
        """Stampa riassunto risultati"""
        if 'final_palette' not in self.results:
            return
        
        print("\n" + "="*50)
        print("🎨 PALETTE KEI URANA - RISULTATI FINALI")
        print("="*50)
        
        palette = self.results['final_palette']
        
        print("\n🔥 CAPELLI:")
        for i, color in enumerate(palette['hair_colors'][:3]):
            print(f"  {i+1}. {color['hex']} (freq: {color['frequency']:.1%})")
        
        print("\n👤 PELLE:")
        for i, color in enumerate(palette['skin_colors'][:2]):
            print(f"  {i+1}. {color['hex']} (freq: {color['frequency']:.1%})")
        
        print("\n👕 OUTFIT:")
        for i, color in enumerate(palette['outfit_colors'][:3]):
            print(f"  {i+1}. {color['hex']} (freq: {color['frequency']:.1%})")
        
        if 'lighting' in self.results:
            lighting = self.results['lighting']
            print(f"\n💡 ILLUMINAZIONE: {lighting['light_type']} (brightness: {lighting['avg_brightness']:.0f})")
        
        print("\n📁 File generati:")
        print("  - kei_analysis_results.json (dati completi)")
        print("  - kei_theme_configs.json (configurazioni tema)")
        print("  - debug_*.png (crop zone analizzate)")

def main():
    if len(sys.argv) != 2:
        print("Uso: python kei_analyzer.py <path_to_frame.png>")
        print("Esempio: python kei_analyzer.py videoframe_90524.png")
        sys.exit(1)
    
    frame_path = sys.argv[1]
    
    if not os.path.exists(frame_path):
        print(f"❌ File non trovato: {frame_path}")
        sys.exit(1)
    
    analyzer = KeiAnalyzer(frame_path)
    success = analyzer.run_complete_analysis()
    
    if success:
        print("\n🎯 Analisi completata con successo!")
        print("Usa i risultati per creare il tema Linux perfetto!")
    else:
        print("\n❌ Errore durante l'analisi")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
APPLY KEI THEME - Applica automaticamente il tema generato
Modifica i file di configurazione del sistema
"""

import json
import os
import shutil
import sys
from pathlib import Path

class KeiThemeApplier:
    def __init__(self, config_file='kei_theme_configs.json'):
        self.config_file = config_file
        self.configs = None
        self.home = Path.home()
        
    def load_configs(self):
        """Carica configurazioni tema"""
        try:
            with open(self.config_file, 'r') as f:
                self.configs = json.load(f)
            print(f"✅ Configurazioni caricate da: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento configurazioni: {e}")
            return False
    
    def backup_existing_configs(self):
        """Crea backup delle configurazioni esistenti"""
        backup_dir = self.home / '.config' / 'kei_theme_backup'
        backup_dir.mkdir(exist_ok=True)
        
        configs_to_backup = [
            '.config/hypr/hyprland.conf',
            '.config/kitty/kitty.conf',
            '.config/gtk-3.0/gtk.css',
            '.config/rofi/config.rasi'
        ]
        
        for config_path in configs_to_backup:
            source = self.home / config_path
            if source.exists():
                dest = backup_dir / source.name
                shutil.copy2(source, dest)
                print(f"💾 Backup creato: {dest}")
        
        print(f"📁 Backup salvati in: {backup_dir}")
        return True
    
    def apply_hyprland_config(self):
        """Applica configurazione Hyprland"""
        if 'hyprland' not in self.configs:
            return False
        
        hypr_config = self.configs['hyprland']
        config_path = self.home / '.config' / 'hypr' / 'hyprland.conf'
        
        if not config_path.exists():
            print("⚠️ File hyprland.conf non trovato, creando nuovo...")
            config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Leggi configurazione esistente
        existing_config = ""
        if config_path.exists():
            with open(config_path, 'r') as f:
                existing_config = f.read()
        
        # Trova e sostituisci sezioni colori
        new_config = existing_config
        
        # Sostituisci o aggiungi configurazioni colori
        color_section = f"""
# KEI URANA THEME - Auto-generated
general {{
    {hypr_config['active_border']}
    col.inactive_border = {hypr_config['inactive_border']}
    {hypr_config['background']}
}}

decoration {{
    rounding = 6
    blur {{
        enabled = true
        size = 2
        passes = 1
        new_optimizations = true
        contrast = 1.15
        brightness = 0.92
    }}
    drop_shadow = true
    shadow_range = 6
    shadow_render_power = 2
    col.shadow = rgba(0B171D60)
}}

animations {{
    enabled = true
    bezier = keiCurve, 0.25, 0.1, 0.25, 1.0
    
    animation = windows, 1, 4, keiCurve, slide
    animation = fade, 1, 3, keiCurve
    animation = workspaces, 1, 5, keiCurve, slidevert
    animation = border, 1, 6, keiCurve
}}
"""
        
        # Se non esiste configurazione, aggiungi
        if 'general {' not in new_config:
            new_config += color_section
        else:
            # Sostituisci sezione esistente (implementazione semplificata)
            new_config += "\n" + color_section
        
        # Salva configurazione
        with open(config_path, 'w') as f:
            f.write(new_config)
        
        print("🎨 Configurazione Hyprland applicata")
        return True
    
    def apply_terminal_config(self):
        """Applica configurazione terminal (Kitty)"""
        if 'terminal' not in self.configs:
            return False
        
        term_config = self.configs['terminal']
        config_path = self.home / '.config' / 'kitty' / 'kitty.conf'
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Genera configurazione Kitty
        kitty_config = f"""# KEI URANA THEME - Auto-generated

# Colors
background {term_config['background']}
foreground {term_config['foreground']}
cursor {term_config['cursor']}
cursor_text_color {term_config['foreground']}

# Selection colors
selection_background {term_config['cursor']}
selection_foreground {term_config['foreground']}

# Black
color0 {term_config['color1']}
color8 #2a2a2d

# Red (Hair colors)
color1 {term_config['color1']}
color9 {term_config['color9']}

# Green
color2 #5d443d
color10 #715c52

# Yellow
color3 #8e5f62
color11 #a06d70

# Blue (Outfit colors)
color4 {term_config['color4']}
color12 {term_config['color12']}

# Magenta
color5 #5c3449
color13 #963251

# Cyan
color6 #35526b
color14 #4a6b85

# White
color7 #848288
color15 {term_config['foreground']}

# Font
font_family JetBrains Mono
font_size 11.0
bold_font auto
italic_font auto

# Window
window_padding_width 8
background_opacity 0.94
"""
        
        with open(config_path, 'w') as f:
            f.write(kitty_config)
        
        print("🖥️ Configurazione Terminal applicata")
        return True
    
    def apply_gtk_config(self):
        """Applica configurazione GTK"""
        if 'gtk' not in self.configs:
            return False
        
        gtk_config = self.configs['gtk']
        
        # GTK CSS
        css_path = self.home / '.config' / 'gtk-3.0' / 'gtk.css'
        css_path.parent.mkdir(parents=True, exist_ok=True)
        
        gtk_css = f"""/* KEI URANA THEME - Auto-generated */

@define-color kei_bg_primary {gtk_config['bg_primary']};
@define-color kei_bg_secondary {gtk_config['bg_secondary']};
@define-color kei_accent {gtk_config['accent']};
@define-color kei_text {gtk_config['text']};

/* Window decorations */
.titlebar {{
    background: linear-gradient(to right, @kei_bg_primary, @kei_bg_secondary);
    color: @kei_text;
    border-bottom: 1px solid @kei_accent;
}}

/* Buttons */
button {{
    background: @kei_bg_secondary;
    border: 1px solid @kei_accent;
    color: @kei_text;
    border-radius: 4px;
}}

button:hover {{
    background: @kei_accent;
    border-color: {gtk_config['accent']};
}}

/* Selections */
*:selected {{
    background-color: @kei_accent;
    color: @kei_text;
}}

/* Entry fields */
entry {{
    background: @kei_bg_primary;
    border: 1px solid @kei_accent;
    color: @kei_text;
}}

entry:focus {{
    border-color: @kei_accent;
    box-shadow: 0 0 3px @kei_accent;
}}

/* Scrollbars */
scrollbar {{
    background: @kei_bg_primary;
}}

scrollbar slider {{
    background: @kei_bg_secondary;
    border-radius: 3px;
}}

scrollbar slider:hover {{
    background: @kei_accent;
}}
"""
        
        with open(css_path, 'w') as f:
            f.write(gtk_css)
        
        print("🎨 Configurazione GTK applicata")
        return True
    
    def create_rofi_config(self):
        """Crea configurazione Rofi"""
        if 'gtk' not in self.configs:
            return False
        
        gtk_config = self.configs['gtk']
        rofi_path = self.home / '.config' / 'rofi' / 'themes' / 'kei-urana.rasi'
        rofi_path.parent.mkdir(parents=True, exist_ok=True)
        
        rofi_config = f"""/* KEI URANA THEME - Auto-generated */

* {{
    bg-primary: {gtk_config['bg_primary']};
    bg-secondary: {gtk_config['bg_secondary']};
    accent: {gtk_config['accent']};
    text: {gtk_config['text']};
    
    background-color: transparent;
    text-color: @text;
    font: "Inter 11";
}}

window {{
    background-color: @bg-primary;
    border: 2px solid @accent;
    border-radius: 6px;
    padding: 20px;
    width: 600px;
    location: center;
    anchor: center;
}}

mainbox {{
    children: [ inputbar, listview ];
    spacing: 16px;
}}

inputbar {{
    background-color: @bg-secondary;
    border-radius: 4px;
    padding: 12px;
    children: [ prompt, entry ];
}}

prompt {{
    text-color: @accent;
    font: "Inter Bold 11";
}}

entry {{
    placeholder: "Search...";
    placeholder-color: @text;
}}

listview {{
    lines: 8;
    scrollbar: false;
    spacing: 2px;
}}

element {{
    padding: 8px 12px;
    border-radius: 3px;
    background-color: transparent;
}}

element selected {{
    background-color: @accent;
    text-color: @text;
}}
"""
        
        with open(rofi_path, 'w') as f:
            f.write(rofi_config)
        
        # Aggiorna config principale rofi
        main_config_path = self.home / '.config' / 'rofi' / 'config.rasi'
        main_config = f'@theme "themes/kei-urana.rasi"\n'
        
        with open(main_config_path, 'w') as f:
            f.write(main_config)
        
        print("🚀 Configurazione Rofi applicata")
        return True
    
    def reload_configs(self):
        """Ricarica configurazioni sistema"""
        print("🔄 Ricaricando configurazioni...")
        
        # Ricarica Hyprland se in esecuzione
        os.system('hyprctl reload 2>/dev/null || true')
        
        # Ricarica GTK
        os.system('gsettings set org.gnome.desktop.interface gtk-theme "Adwaita-dark" 2>/dev/null || true')
        
        print("✅ Configurazioni ricaricate")
        return True
    
    def apply_complete_theme(self):
        """Applica tema completo"""
        print("🎨 Applicazione tema Kei Urana...")
        
        if not self.load_configs():
            return False
        
        print("💾 Creando backup configurazioni esistenti...")
        if not self.backup_existing_configs():
            return False
        
        print("🏗️ Applicando configurazione Hyprland...")
        if not self.apply_hyprland_config():
            print("⚠️ Errore configurazione Hyprland")
        
        print("🖥️ Applicando configurazione Terminal...")
        if not self.apply_terminal_config():
            print("⚠️ Errore configurazione Terminal")
        
        print("🎨 Applicando configurazione GTK...")
        if not self.apply_gtk_config():
            print("⚠️ Errore configurazione GTK")
        
        print("🚀 Creando configurazione Rofi...")
        if not self.create_rofi_config():
            print("⚠️ Errore configurazione Rofi")
        
        print("🔄 Ricaricando sistema...")
        self.reload_configs()
        
        print("\n✅ TEMA KEI URANA APPLICATO!")
        print("\n📋 PROSSIMI PASSI:")
        print("  1. Riavvia Hyprland: Super+Shift+R")
        print("  2. Riavvia terminal per vedere i nuovi colori")
        print("  3. Testa Rofi: Super+D")
        print("\n💾 Backup salvati in: ~/.config/kei_theme_backup/")
        print("🔄 Per ripristinare: copia i file backup nelle posizioni originali")
        
        return True

def main():
    config_file = 'kei_theme_configs.json'
    
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
    
    if not os.path.exists(config_file):
        print(f"❌ File configurazioni non trovato: {config_file}")
        print("Esegui prima:")
        print("  1. python3 kei_analyzer.py videoframe_90524.png")
        print("  2. python3 apply_kei_theme.py")
        sys.exit(1)
    
    applier = KeiThemeApplier(config_file)
    success = applier.apply_complete_theme()
    
    if success:
        print("\n🎯 Tema applicato con successo!")
    else:
        print("\n❌ Errore durante l'applicazione")
        sys.exit(1)

if __name__ == "__main__":
    main()

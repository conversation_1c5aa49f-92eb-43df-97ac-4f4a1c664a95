#!/bin/bash
# <PERSON>ript diretto per creare tutti i 31 emulatori gaming
# <PERSON><PERSON>mizzato per i9-12900KF + RTX 4080 + 6GB RAM + 32GB Storage

set -e

ANDROID_HOME="$HOME/android_studio_2025/sdk"
ANDROID_AVD_HOME="$HOME/android_studio_2025/avds"

# Colori
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Lista completa dei 31 giochi
GAMES=(
    # HIGH-END (4K)
    "Genshin_Impact:Genshin Impact:3840x2160:640"
    "Wuthering_Waves:Wuthering Waves:3840x2160:640"
    "Zenless_Zone_Zero:Zenless Zone Zero:3840x2160:640"
    "Tower_of_Fantasy:Tower of Fantasy:3840x2160:640"
    "Once_Human:Once Human:3840x2160:640"
    "Snowbreak:Snowbreak:3840x2160:640"
    
    # PERFORMANCE (1440p)
    "Blood_Strike:Blood Strike:2560x1440:560"
    "Ace_Racer:Ace Racer:2560x1440:560"
    "Nikke_Goddess_Victory:Nikke Goddess Victory:2560x1440:560"
    "Punishing_Gray_Raven:Punishing Gray Raven:2560x1440:560"
    
    # STANDARD (1080p)
    "Honkai_Impact_3rd:Honkai Impact 3rd:1920x1080:420"
    "Honkai_Star_Rail:Honkai Star Rail:1920x1080:420"
    "Epic_Seven:Epic Seven:1920x1080:420"
    "Girls_Frontline_2:Girls Frontline 2:1920x1080:420"
    "Infinity_Nikki:Infinity Nikki:1920x1080:420"
    "Solo_Leveling_Arise:Solo Leveling Arise:1920x1080:420"
    "Aether_Gazer:Aether Gazer:1920x1080:420"
    "Reverse_1999:Reverse 1999:1920x1080:420"
    "Heaven_Burns_Red:Heaven Burns Red:1920x1080:420"
    "STARSEED_Asnia_Trigger:STARSEED Asnia Trigger:1920x1080:420"
    "Metal_Slug_Awakening:Metal Slug Awakening:1920x1080:420"
    "Phantom_Blade_Executioners:Phantom Blade Executioners:1920x1080:420"
    "Ash_Echoes:Ash Echoes:1920x1080:420"
    "Brown_Dust_2:Brown Dust 2:1920x1080:420"
    "Danchro:Danchro:1920x1080:420"
    
    # CASUAL (Portrait)
    "Cookie_Run_Kingdom:Cookie Run Kingdom:1080x1920:420"
    "Cookie_Run_Ovenbreak:Cookie Run Ovenbreak:1080x1920:420"
    "Cookie_Run_Tower_Adventure:Cookie Run Tower Adventure:1080x1920:420"
    "Cat_Fantasy:Cat Fantasy:1080x1920:420"
    "Memento_Mori:Memento Mori:1080x1920:420"
    "Fairlight84:Fairlight84:1080x1920:420"
)

# Funzione per creare AVD direttamente
create_avd_direct() {
    local game_data=$1
    IFS=':' read -r name display_name resolution density <<< "$game_data"
    IFS='x' read -r width height <<< "$resolution"
    
    echo -e "${CYAN}🎮 Creando: ${YELLOW}$display_name${NC}"
    
    # Crea directory AVD
    AVD_DIR="$ANDROID_AVD_HOME/${name}.avd"
    mkdir -p "$AVD_DIR"
    
    # Crea file .ini
    cat > "$ANDROID_AVD_HOME/${name}.ini" << EOF
avd.ini.encoding=UTF-8
path=$AVD_DIR
target=android-34
EOF
    
    # Crea config.ini ottimizzato
    cat > "$AVD_DIR/config.ini" << EOF
# Gaming Configuration - $display_name
# Ottimizzato per i9-12900KF + RTX 4080
PlayStore.enabled=no
abi.type=x86_64
avd.id=$name
avd.ini.displayname=$display_name
avd.ini.encoding=UTF-8
avd.name=$name

# Storage Configuration
disk.cachePartition=yes
disk.cachePartition.size=128MB
disk.dataPartition.path=$AVD_DIR/userdata-qemu.img
disk.dataPartition.size=32GB
disk.systemPartition.size=0
disk.vendorPartition.size=0

# Boot Optimization
fastboot.chosenSnapshotFile=
fastboot.forceChosenSnapshotBoot=no
fastboot.forceColdBoot=no
fastboot.forceFastBoot=yes

# Performance Settings - 6GB RAM
hw.ramSize=6144
vm.heapSize=1024
hw.cpu.ncore=8

# RTX 4080 GPU Acceleration
hw.gpu.enabled=yes
hw.gpu.mode=host
hw.gpu.blacklisted=no
hw.gltransport=pipe

# Display Settings
hw.lcd.width=$width
hw.lcd.height=$height
hw.lcd.density=$density
hw.lcd.vsync=60
hw.lcd.backlight=yes
hw.lcd.depth=16

# Gaming Sensors
hw.accelerometer=yes
hw.accelerometer_uncalibrated=yes
hw.sensors.gyroscope=yes
hw.sensors.gps=yes
hw.sensors.proximity=yes
hw.sensors.magnetic_field=yes
hw.sensors.light=yes
hw.sensors.pressure=yes
hw.sensors.temperature=yes

# Audio/Video
hw.audioInput=yes
hw.audioOutput=yes
hw.camera.back=emulated
hw.camera.front=emulated

# Input/Controls
hw.keyboard=yes
hw.keyboard.charmap=qwerty2
hw.keyboard.lid=yes
hw.screen=multi-touch
hw.mainKeys=no
hw.dPad=no
hw.trackBall=no

# Network Gaming
runtime.network.speed=full
runtime.network.latency=none
hw.wifi=yes
hw.gsmModem=yes

# Device Properties
hw.device.manufacturer=Google
hw.device.name=pixel_7_pro
hw.device.hash2=MD5:2016577e1656e8e7c2adb0fac972beea

# System Image
image.androidVersion.api=34
image.sysdir.1=system-images/android-34/google_apis_playstore/x86_64/
tag.display=Google Play
tag.id=google_apis_playstore
tag.ids=google_apis_playstore
target=android-34

# Memory Management
userdata.useQcow2=no
hw.useext4=yes

# Additional Gaming Optimizations
hw.initialOrientation=Portrait
showDeviceFrame=yes
hw.battery=yes
hw.sdCard=yes
sdcard.size=512MB

# Advanced Features
hw.arc=false
hw.arc.autologin=false
hw.hotplug_multi_display=no
hw.multi_display_window=no
hw.rotaryInput=no
hw.sensor.hinge=no
hw.sensor.roll=no

# Test Settings
test.delayAdbTillBootComplete=0
test.monitorAdb=0
test.quitAfterBootTimeOut=-1
EOF
    
    # Crea userdata image
    if [ ! -f "$AVD_DIR/userdata-qemu.img" ]; then
        $ANDROID_HOME/emulator/mksdcard -l e 32GB "$AVD_DIR/userdata-qemu.img" 2>/dev/null || true
    fi
    
    # Crea cache image
    if [ ! -f "$AVD_DIR/cache.img" ]; then
        $ANDROID_HOME/emulator/mksdcard -l e 128MB "$AVD_DIR/cache.img" 2>/dev/null || true
    fi
    
    echo -e "${GREEN}✅ $display_name creato!${NC}"
}

# Header
clear
echo -e "${CYAN}🚀 CREAZIONE DIRETTA 31 EMULATORI GAMING 2025${NC}"
echo -e "${CYAN}===============================================${NC}"
echo -e "${YELLOW}Sistema:${NC} i9-12900KF + RTX 4080 + 4K"
echo -e "${YELLOW}Configurazione:${NC} 6GB RAM, 32GB Storage (tutti)"
echo -e "${YELLOW}Senza Gmail:${NC} PlayStore disabilitato"
echo ""

# Verifica sistema
if [ ! -d "$ANDROID_HOME/system-images/android-34/google_apis_playstore/x86_64" ]; then
    echo -e "${RED}❌ System image Android 34 non trovato!${NC}"
    echo "Installando system image..."
    $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "system-images;android-34;google_apis_playstore;x86_64"
fi

# Crea tutti gli emulatori
count=0
total=${#GAMES[@]}

for game in "${GAMES[@]}"; do
    count=$((count + 1))
    echo -e "${BLUE}[$count/$total]${NC}"
    create_avd_direct "$game"
    echo ""
done

# Completamento
echo -e "${GREEN}🎉 TUTTI I 31 EMULATORI CREATI CON SUCCESSO!${NC}"
echo -e "${GREEN}=============================================${NC}"
echo ""
echo -e "${CYAN}📊 RIEPILOGO:${NC}"
echo -e "   ${RED}🏆 6 Emulatori HIGH-END${NC}     (4K, 6GB RAM, 32GB storage)"
echo -e "   ${YELLOW}🎯 4 Emulatori PERFORMANCE${NC}  (1440p, 6GB RAM, 32GB storage)"
echo -e "   ${GREEN}🎮 15 Emulatori STANDARD${NC}    (1080p, 6GB RAM, 32GB storage)"
echo -e "   ${BLUE}🍪 6 Emulatori CASUAL${NC}       (Portrait, 6GB RAM, 32GB storage)"
echo ""
echo -e "${PURPLE}📁 Posizione:${NC} $ANDROID_AVD_HOME"
echo -e "${PURPLE}💾 Spazio utilizzato:${NC} ~992GB (31 x 32GB)"
echo ""
echo -e "${CYAN}🎮 CARATTERISTICHE:${NC}"
echo -e "   • Senza Gmail/Google Play Store"
echo -e "   • RTX 4080 Hardware Acceleration"
echo -e "   • 8 CPU cores per emulatore"
echo -e "   • Sensori gaming completi"
echo -e "   • Network ottimizzato"
echo ""
echo -e "${GREEN}✅ Pronti per il gaming!${NC}"
echo -e "${YELLOW}Avvia Android Studio e usa Device Manager per lanciare gli emulatori${NC}"

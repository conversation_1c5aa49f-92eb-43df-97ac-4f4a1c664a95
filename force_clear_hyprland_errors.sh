#!/bin/bash

# Script per forzare la pulizia completa degli errori Hyprland

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🔄 RESET COMPLETO ERRORI HYPRLAND${NC}"
echo ""

echo -e "${BLUE}📋 SITUAZIONE:${NC}"
echo -e "${GREEN}✅ Configurazione: Nessun errore trovato dagli script${NC}"
echo -e "${GREEN}✅ Versione: Hyprland 0.50.1 (ultima)${NC}"
echo -e "${YELLOW}⚠️ Riquadro rosso: Ancora visibile${NC}"
echo ""

echo -e "${CYAN}🎯 POSSIBILI CAUSE:${NC}"
echo -e "${BLUE}1. Cache di configurazione corrotta${NC}"
echo -e "${BLUE}2. Processo Hyprland con stato inconsistente${NC}"
echo -e "${BLUE}3. File di configurazione cached in memoria${NC}"
echo -e "${BLUE}4. Bug di visualizzazione Hyprland 0.50.x${NC}"
echo ""

echo -e "${YELLOW}🔧 METODI DI RESET PROGRESSIVI:${NC}"
echo ""

# Metodo 1: Reset soft
echo -e "${BLUE}📋 METODO 1: Reset soft${NC}"
echo -e "${YELLOW}1. Reload multiplo configurazione...${NC}"
hyprctl reload
sleep 1
hyprctl reload
sleep 1
hyprctl reload
echo -e "${GREEN}✅ Reload multiplo completato${NC}"

# Metodo 2: Clear cache completo
echo -e "${YELLOW}2. Pulizia cache completa...${NC}"
rm -rf ~/.cache/hyprland* 2>/dev/null || true
rm -rf /tmp/hypr* 2>/dev/null || true
rm -rf /tmp/.hyprland* 2>/dev/null || true
echo -e "${GREEN}✅ Cache pulita${NC}"

# Metodo 3: Reset variabili dinamiche
echo -e "${YELLOW}3. Reset variabili dinamiche...${NC}"
hyprctl keyword misc:disable_hyprland_logo true
hyprctl keyword misc:disable_splash_rendering true
hyprctl keyword debug:suppress_errors true
sleep 1
hyprctl keyword debug:suppress_errors false
echo -e "${GREEN}✅ Variabili reset${NC}"

# Metodo 4: Force refresh rendering
echo -e "${YELLOW}4. Force refresh rendering...${NC}"
hyprctl keyword decoration:rounding 0
sleep 0.2
hyprctl keyword decoration:rounding 5
sleep 0.2
hyprctl keyword general:border_size 1
sleep 0.2
hyprctl keyword general:border_size 2
sleep 0.2
hyprctl keyword general:border_size 1
echo -e "${GREEN}✅ Rendering refreshed${NC}"

echo ""
echo -e "${CYAN}🧪 TEST FINALE:${NC}"
FINAL_TEST=$(hyprctl reload 2>&1)
echo "$FINAL_TEST"

if echo "$FINAL_TEST" | grep -qi "error\|does not exist"; then
    echo ""
    echo -e "${RED}❌ ERRORI ANCORA PRESENTI${NC}"
    echo -e "${YELLOW}🔧 METODI AVANZATI DISPONIBILI:${NC}"
    echo ""
    
    echo -e "${BLUE}OPZIONE A: Restart Hyprland completo${NC}"
    echo -e "${CYAN}hyprctl dispatch exit${NC}"
    echo -e "${BLUE}(Poi riavvia dal login manager)${NC}"
    echo ""
    
    echo -e "${BLUE}OPZIONE B: Configurazione minima temporanea${NC}"
    echo -e "${CYAN}Creare config minimo per test${NC}"
    echo ""
    
    echo -e "${BLUE}OPZIONE C: Debug avanzato${NC}"
    echo -e "${CYAN}Analisi dettagliata errori specifici${NC}"
    echo ""
    
    read -p "Scegli opzione (A/B/C/N per nessuna): " -n 1 -r
    echo
    
    case $REPLY in
        [Aa])
            echo -e "${YELLOW}🔄 Restart Hyprland in 5 secondi...${NC}"
            echo -e "${BLUE}Hyprland si chiuderà, riavvialo dal login manager${NC}"
            sleep 5
            hyprctl dispatch exit
            ;;
        [Bb])
            echo -e "${YELLOW}🔧 Creando configurazione minima...${NC}"
            
            # Backup configurazione attuale
            CURRENT_CONFIG="$HOME/.config/hypr/hyprland.conf"
            BACKUP_CONFIG="$HOME/.config/hypr/hyprland.conf.backup.full.$(date +%Y%m%d_%H%M%S)"
            cp "$CURRENT_CONFIG" "$BACKUP_CONFIG"
            echo -e "${GREEN}💾 Backup completo: $BACKUP_CONFIG${NC}"
            
            # Crea configurazione minima 0.50.x
            cat > "$CURRENT_CONFIG" << 'EOF'
# Configurazione Hyprland 0.50.x minima per test errori

monitor = ,preferred,auto,1

general {
    gaps_in = 5
    gaps_out = 10
    border_size = 2
    col.active_border = rgba(33ccffee) rgba(00ff99ee) 45deg
    col.inactive_border = rgba(595959aa)
    layout = dwindle
    allow_tearing = false
}

decoration {
    rounding = 5
    
    blur {
        enable = true
        radius = 3
        passes = 1
    }
    
    drop_shadow = true
    shadow_range = 4
    shadow_render_power = 3
    col.shadow = rgba(1a1a1aee)
}

animations {
    enable = true
    
    bezier = myBezier, 0.05, 0.9, 0.1, 1.05
    
    animation = windows, 1, 7, myBezier
    animation = windowsOut, 1, 7, default, popin 80%
    animation = border, 1, 10, default
    animation = borderangle, 1, 8, default
    animation = fade, 1, 7, default
    animation = workspaces, 1, 6, default
}

input {
    kb_layout = it
    
    follow_mouse = 1
    
    touchpad {
        natural_scroll = false
    }
    
    sensitivity = 0
}

misc {
    disable_hyprland_logo = true
    disable_splash_rendering = true
    force_default_wallpaper = 0
    vfr = true
}

# Keybinds essenziali
bind = SUPER, Q, exec, kitty
bind = SUPER, C, killactive,
bind = SUPER, M, exit,
bind = SUPER, E, exec, thunar
bind = SUPER, V, togglefloating,
bind = SUPER, R, exec, rofi -show drun

# Move focus
bind = SUPER, left, movefocus, l
bind = SUPER, right, movefocus, r
bind = SUPER, up, movefocus, u
bind = SUPER, down, movefocus, d

# Switch workspaces
bind = SUPER, 1, workspace, 1
bind = SUPER, 2, workspace, 2
bind = SUPER, 3, workspace, 3
bind = SUPER, 4, workspace, 4
bind = SUPER, 5, workspace, 5

# Move active window to workspace
bind = SUPER SHIFT, 1, movetoworkspace, 1
bind = SUPER SHIFT, 2, movetoworkspace, 2
bind = SUPER SHIFT, 3, movetoworkspace, 3
bind = SUPER SHIFT, 4, movetoworkspace, 4
bind = SUPER SHIFT, 5, movetoworkspace, 5
EOF
            
            # Test configurazione minima
            echo -e "${YELLOW}🧪 Test configurazione minima...${NC}"
            TEST_MINIMAL=$(hyprctl reload 2>&1)
            
            if echo "$TEST_MINIMAL" | grep -qi "error\|does not exist"; then
                echo -e "${RED}❌ Errori anche con config minima:${NC}"
                echo "$TEST_MINIMAL"
                echo -e "${YELLOW}🔄 Ripristino configurazione originale...${NC}"
                cp "$BACKUP_CONFIG" "$CURRENT_CONFIG"
                hyprctl reload
            else
                echo -e "${GREEN}✅ Configurazione minima funziona!${NC}"
                echo "$TEST_MINIMAL"
                echo -e "${BLUE}💡 Puoi ripristinare gradualmente la tua configurazione${NC}"
                echo -e "${BLUE}💡 Backup completo: $BACKUP_CONFIG${NC}"
            fi
            ;;
        [Cc])
            echo -e "${YELLOW}🔍 Debug avanzato...${NC}"
            
            echo -e "${BLUE}📋 Informazioni sistema:${NC}"
            echo -e "${GREEN}Versione Hyprland: $(hyprctl version | head -1)${NC}"
            echo -e "${GREEN}Processo Hyprland: $(pgrep -f hyprland | wc -l) istanze${NC}"
            echo -e "${GREEN}File config: $HOME/.config/hypr/hyprland.conf${NC}"
            
            echo ""
            echo -e "${BLUE}📋 Log errori dettagliato:${NC}"
            journalctl --user -u hyprland --since "10 minutes ago" --no-pager | grep -i "error\|warn\|fail" | tail -10
            
            echo ""
            echo -e "${BLUE}📋 Test opzioni specifiche:${NC}"
            hyprctl getoption general:border_size 2>/dev/null || echo "Errore nel leggere opzioni"
            hyprctl getoption decoration:rounding 2>/dev/null || echo "Errore nel leggere decorazioni"
            
            echo ""
            echo -e "${BLUE}💡 Se vedi errori specifici, posso creare fix mirati${NC}"
            ;;
        *)
            echo -e "${BLUE}Nessuna azione eseguita${NC}"
            ;;
    esac
    
else
    echo ""
    echo -e "${GREEN}🏆 SUCCESSO COMPLETO!${NC}"
    echo -e "${CYAN}🎯 Il riquadro rosso dovrebbe essere sparito!${NC}"
    echo -e "${BLUE}💡 Se persiste, potrebbe essere un bug di visualizzazione${NC}"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}🎉 RESET ERRORI COMPLETATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📋 RIEPILOGO AZIONI:${NC}"
echo -e "${GREEN}✅ Reload multiplo configurazione${NC}"
echo -e "${GREEN}✅ Cache completamente pulita${NC}"
echo -e "${GREEN}✅ Variabili dinamiche reset${NC}"
echo -e "${GREEN}✅ Rendering force refreshed${NC}"
echo ""

echo -e "${CYAN}💡 Se il riquadro rosso persiste ancora:${NC}"
echo -e "${BLUE}1. Potrebbe essere un bug di Hyprland 0.50.x${NC}"
echo -e "${BLUE}2. Prova a riavviare completamente Hyprland${NC}"
echo -e "${BLUE}3. Controlla se ci sono aggiornamenti disponibili${NC}"
echo ""

echo -e "${GREEN}🎯 Configurazione Hyprland 0.50.1 ottimizzata!${NC}"

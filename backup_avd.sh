#!/bin/bash
# Script Backup Automatico AVD
# Ottimizzato per il sistema i9-12900KF + RTX 4080

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=== ANDROID AVD BACKUP UTILITY ==="
echo "Data: $(date)"
echo "=================================="

# Verifica variabili ambiente
if [ -z "$ANDROID_AVD_HOME" ]; then
    export ANDROID_AVD_HOME="$HOME/.android/avd"
    warn "ANDROID_AVD_HOME non impostato, uso default: $ANDROID_AVD_HOME"
fi

# Directory di backup
BACKUP_BASE="/home/<USER>/Android/AVD/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE/backup_$DATE"

# Crea directory backup
mkdir -p "$BACKUP_DIR"
log "Directory backup creata: $BACKUP_DIR"

# Verifica se esistono AVD
if [ ! -d "$ANDROID_AVD_HOME" ] || [ -z "$(ls -A "$ANDROID_AVD_HOME" 2>/dev/null)" ]; then
    error "Nessun AVD trovato in $ANDROID_AVD_HOME"
    exit 1
fi

# Lista AVD disponibili
log "AVD disponibili per il backup:"
ls -la "$ANDROID_AVD_HOME" | grep -E "\.(avd|ini)$" | awk '{print $9}' | sort

# Backup AVD configurations
log "Backup configurazioni AVD in corso..."
AVD_COUNT=0

# Backup file .avd
for avd_dir in "$ANDROID_AVD_HOME"/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir")
        log "Backup AVD: $avd_name"
        cp -r "$avd_dir" "$BACKUP_DIR/"
        ((AVD_COUNT++))
    fi
done

# Backup file .ini
for ini_file in "$ANDROID_AVD_HOME"/*.ini; do
    if [ -f "$ini_file" ]; then
        ini_name=$(basename "$ini_file")
        log "Backup configurazione: $ini_name"
        cp "$ini_file" "$BACKUP_DIR/"
    fi
done

# Backup snapshots importanti (solo quelli recenti per risparmiare spazio)
log "Backup snapshots recenti..."
find "$ANDROID_AVD_HOME" -name "*.qcow2" -mtime -7 -exec cp {} "$BACKUP_DIR/" \; 2>/dev/null || true

# Backup custom configurations
log "Backup configurazioni personalizzate..."
[ -f ~/.android/advancedFeatures.ini ] && cp ~/.android/advancedFeatures.ini "$BACKUP_DIR/"
[ -f ~/.android/opengl_config.ini ] && cp ~/.android/opengl_config.ini "$BACKUP_DIR/"

# Crea file info backup
cat > "$BACKUP_DIR/backup_info.txt" << EOF
=== ANDROID AVD BACKUP INFO ===
Data backup: $(date)
Sistema: $(uname -a)
Utente: $(whoami)
AVD Home: $ANDROID_AVD_HOME
Numero AVD: $AVD_COUNT

AVD inclusi nel backup:
$(ls -la "$ANDROID_AVD_HOME" | grep "\.avd$" | awk '{print $9}')

Configurazioni incluse:
$(ls -la "$ANDROID_AVD_HOME" | grep "\.ini$" | awk '{print $9}')

Dimensione backup: $(du -sh "$BACKUP_DIR" | cut -f1)
EOF

# Comprimi backup
log "Compressione backup in corso..."
cd "$BACKUP_BASE"
tar -czf "avd_backup_$DATE.tar.gz" "backup_$DATE"

# Verifica compressione
if [ -f "avd_backup_$DATE.tar.gz" ]; then
    BACKUP_SIZE=$(du -sh "avd_backup_$DATE.tar.gz" | cut -f1)
    log "Backup compresso creato: avd_backup_$DATE.tar.gz ($BACKUP_SIZE)"
    
    # Rimuovi directory temporanea
    rm -rf "backup_$DATE"
    log "Directory temporanea rimossa"
else
    error "Errore nella compressione del backup"
    exit 1
fi

# Mantieni solo ultimi 10 backup
log "Pulizia backup vecchi..."
cd "$BACKUP_BASE"
BACKUP_COUNT=$(ls -1 avd_backup_*.tar.gz 2>/dev/null | wc -l)
if [ "$BACKUP_COUNT" -gt 10 ]; then
    REMOVE_COUNT=$((BACKUP_COUNT - 10))
    ls -t avd_backup_*.tar.gz | tail -n +11 | xargs rm -f
    log "Rimossi $REMOVE_COUNT backup vecchi"
fi

# Statistiche finali
echo ""
echo "=================================="
echo -e "${GREEN}BACKUP COMPLETATO CON SUCCESSO!${NC}"
echo "=================================="
echo "File backup: avd_backup_$DATE.tar.gz"
echo "Dimensione: $BACKUP_SIZE"
echo "Percorso: $BACKUP_BASE/avd_backup_$DATE.tar.gz"
echo "AVD inclusi: $AVD_COUNT"
echo ""
echo "Per ripristinare il backup, usa:"
echo "./restore_avd.sh avd_backup_$DATE.tar.gz"
echo ""

# Lista backup disponibili
echo "Backup disponibili:"
ls -lah "$BACKUP_BASE"/avd_backup_*.tar.gz 2>/dev/null | tail -5 || echo "Nessun backup precedente trovato"

#!/bin/bash

# Script per rimuovere Gmail da tutti gli emulatori in batch di 4
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🤖 RIMOZIONE GMAIL AUTOMATICA DA TUTTI GLI EMULATORI${NC}"
echo ""

# Configurazione
export ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
export PATH="$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator:$PATH"

# Lista tutti gli emulatori
cd "$ANDROID_AVD_HOME"
emulators=($(ls -d *.avd | sed 's/.avd$//' | sort))
total_emulators=${#emulators[@]}

echo -e "${BLUE}📱 Emulatori trovati: $total_emulators${NC}"
echo -e "${BLUE}🔄 Processamento in batch di 4${NC}"
echo ""

# Funzione per rimuovere Gmail da un emulatore
remove_gmail_from_emulator() {
    local emulator_name=$1
    local emulator_num=$2
    
    echo -e "${YELLOW}[$emulator_num/$total_emulators] Processamento: $emulator_name${NC}"
    
    # Avvia emulatore in background senza finestra
    echo "  🚀 Avvio emulatore..."
    timeout 180 emulator -avd "$emulator_name" -no-window -no-audio -no-boot-anim -gpu off -verbose &
    local emulator_pid=$!
    
    # Attendi che l'emulatore sia pronto
    echo "  ⏳ Attesa avvio Android..."
    local wait_count=0
    while [ $wait_count -lt 60 ]; do
        if adb shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
            echo "  ✅ Android avviato"
            break
        fi
        sleep 3
        ((wait_count++))
        echo -n "."
    done
    echo ""
    
    if [ $wait_count -ge 60 ]; then
        echo -e "${RED}  ❌ Timeout avvio $emulator_name${NC}"
        kill $emulator_pid 2>/dev/null || true
        return 1
    fi
    
    # Rimuovi Gmail
    echo "  📧 Rimozione Gmail..."
    
    # Disinstalla Gmail
    if adb shell pm uninstall --user 0 com.google.android.gm 2>/dev/null; then
        echo -e "${GREEN}  ✅ Gmail disinstallato${NC}"
    else
        # Se non riesce a disinstallare, prova a disabilitare
        if adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null; then
            echo -e "${GREEN}  ✅ Gmail disabilitato${NC}"
        else
            echo -e "${YELLOW}  ⚠️ Gmail non rimosso (potrebbe non essere presente)${NC}"
        fi
    fi
    
    # Rimuovi anche altri servizi Google opzionali
    adb shell pm disable-user --user 0 com.google.android.gsf 2>/dev/null || true
    adb shell pm disable-user --user 0 com.google.android.gms.setup 2>/dev/null || true
    
    # Chiudi emulatore
    echo "  🛑 Chiusura emulatore..."
    kill $emulator_pid 2>/dev/null || true
    sleep 2
    
    echo -e "${GREEN}  ✅ $emulator_name completato${NC}"
    echo ""
    
    return 0
}

# Processa emulatori in batch di 4
batch_size=4
processed=0
successful=0
failed=0

for ((i=0; i<$total_emulators; i+=batch_size)); do
    batch_num=$((i/batch_size + 1))
    batch_end=$((i + batch_size - 1))
    if [ $batch_end -ge $total_emulators ]; then
        batch_end=$((total_emulators - 1))
    fi
    
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}📦 BATCH $batch_num (Emulatori $((i+1))-$((batch_end+1)))${NC}"
    echo -e "${CYAN}========================================${NC}"
    echo ""
    
    # Processa batch corrente
    for ((j=i; j<=batch_end && j<total_emulators; j++)); do
        emulator="${emulators[$j]}"
        emulator_num=$((j+1))
        
        if remove_gmail_from_emulator "$emulator" "$emulator_num"; then
            ((successful++))
        else
            ((failed++))
            echo -e "${RED}❌ Fallito: $emulator${NC}"
        fi
        ((processed++))
        
        # Pausa tra emulatori per evitare sovraccarico
        if [ $j -lt $batch_end ] && [ $j -lt $((total_emulators-1)) ]; then
            echo -e "${BLUE}⏸️ Pausa 5 secondi...${NC}"
            sleep 5
        fi
    done
    
    # Pausa tra batch
    if [ $batch_end -lt $((total_emulators-1)) ]; then
        echo -e "${YELLOW}⏸️ Pausa 10 secondi tra batch...${NC}"
        sleep 10
    fi
done

# Pulizia finale
echo -e "${BLUE}🧹 Pulizia processi residui...${NC}"
pkill -f emulator 2>/dev/null || true
sleep 3

# Riepilogo finale
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎉 RIMOZIONE GMAIL COMPLETATA!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 STATISTICHE FINALI:${NC}"
echo -e "${GREEN}✅ Emulatori processati: $processed/$total_emulators${NC}"
echo -e "${GREEN}✅ Successi: $successful${NC}"
echo -e "${RED}❌ Fallimenti: $failed${NC}"

if [ $failed -eq 0 ]; then
    echo -e "${GREEN}🏆 TUTTI GLI EMULATORI PROCESSATI CON SUCCESSO!${NC}"
else
    echo -e "${YELLOW}⚠️ Alcuni emulatori potrebbero richiedere riprocessamento manuale${NC}"
fi

echo ""
echo -e "${BLUE}💾 Spazio liberato stimato: ~$((successful * 500))MB${NC}"
echo -e "${BLUE}🚀 Performance migliorate senza servizi Google${NC}"
echo ""
echo -e "${GREEN}🎮 Tutti gli emulatori sono ora puliti e ottimizzati!${NC}"

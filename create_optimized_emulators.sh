#!/bin/bash

# 🎮 SCRIPT CREAZIONE EMULATORI OTTIMIZZATI 2025
# Configurazioni specifiche per ogni gioco basate su tier performance

echo "🚀 CREAZIONE EMULATORI ANDROID STUDIO 2025"
echo "=========================================="
echo

# Percorsi
AVDMANAGER="$HOME/Android/Sdk/cmdline-tools/latest/bin/avdmanager"
SDKMANAGER="$HOME/Android/Sdk/cmdline-tools/latest/bin/sdkmanager"

# Verifica tools
if [ ! -f "$AVDMANAGER" ]; then
    echo "❌ AVD Manager non trovato!"
    exit 1
fi

echo "✅ Tools verificati"
echo

# 🏆 TIER S - GIOCHI AAA (6GB RAM, 4 CPU, Android 14)
declare -a TIER_S=(
    "Genshin_Impact"
    "Honkai_Star_Rail" 
    "Zenless_Zone_Zero"
    "Wuthering_Waves"
    "Infinity_Nikki"
    "Punishing_Gray_Raven"
)

# 🥇 TIER A - GIOCHI PREMIUM (4GB RAM, 3 CPU, Android 14)  
declare -a TIER_A=(
    "Honkai_Impact_3rd"
    "Solo_Leveling_Arise"
    "Nikke"
    "Snowbreak_Containment_Zone"
    "Reverse_1999"
    "Figure_Fantasy"
)

# 🥈 TIER B - GIOCHI STANDARD (3GB RAM, 2 CPU, Android 13)
declare -a TIER_B=(
    "Epic_Seven"
    "Seven_Deadly_Sins_Grand_Cross"
    "Ni_no_Kuni_Cross_Worlds"
    "Phantom_Blade_Executioners"
    "Metal_Slug_Awakening"
    "Ace_Racer"
)

# 🥉 TIER C - GIOCHI LEGGERI (2GB RAM, 2 CPU, Android 13)
declare -a TIER_C=(
    "Cookie_Run_Kingdom"
    "Cookie_Run_Ovenbreak"
    "Brown_Dust_2"
    "Aether_Gazer"
    "Blood_Strike"
    "Cat_Fantasy"
    "Danchro"
    "Ash_Echoes"
    "Astra"
    "Black_Beacon"
    "Etheria_Restart"
    "Fairlight84"
    "One_Human"
)

# Funzione creazione emulatore
create_emulator() {
    local name=$1
    local ram=$2
    local cpu=$3
    local storage=$4
    local api=$5
    local tier=$6
    
    echo "📱 Creando: $name ($tier)"
    echo "   RAM: ${ram}MB | CPU: $cpu cores | Storage: ${storage}GB | Android: API $api"
    
    # Crea AVD
    echo "no" | $AVDMANAGER create avd \
        -n "$name" \
        -k "system-images;android-$api;google_apis_playstore;x86_64" \
        -d "pixel_7" \
        --force
    
    # Configura AVD
    local config_file="$HOME/.android/avd/${name}.avd/config.ini"
    
    if [ -f "$config_file" ]; then
        # Configurazioni RAM/CPU
        sed -i "s/hw.ramSize=.*/hw.ramSize=$ram/" "$config_file"
        sed -i "s/hw.cpu.ncore=.*/hw.cpu.ncore=$cpu/" "$config_file"
        
        # Storage
        sed -i "s/disk.dataPartition.size=.*/disk.dataPartition.size=${storage}GB/" "$config_file"
        
        # Performance ottimizzazioni
        echo "hw.gpu.enabled=yes" >> "$config_file"
        echo "hw.gpu.mode=host" >> "$config_file"
        echo "hw.keyboard=yes" >> "$config_file"
        echo "showDeviceFrame=no" >> "$config_file"
        echo "skin.dynamic=no" >> "$config_file"
        
        # Ottimizzazioni specifiche per tier
        if [ "$tier" = "TIER_S" ] || [ "$tier" = "TIER_A" ]; then
            echo "hw.arc=false" >> "$config_file"
            echo "hw.audioInput=yes" >> "$config_file"
            echo "hw.camera.back=none" >> "$config_file"
            echo "hw.camera.front=none" >> "$config_file"
        fi
        
        echo "   ✅ $name configurato"
    else
        echo "   ❌ Errore configurazione $name"
    fi
    echo
}

# Creazione emulatori per tier
echo "🏆 CREAZIONE TIER S (6GB RAM, 4 CPU, Android 14)"
for game in "${TIER_S[@]}"; do
    create_emulator "$game" 6144 4 8 34 "TIER_S"
done

echo "🥇 CREAZIONE TIER A (4GB RAM, 3 CPU, Android 14)"  
for game in "${TIER_A[@]}"; do
    create_emulator "$game" 4096 3 6 34 "TIER_A"
done

echo "🥈 CREAZIONE TIER B (3GB RAM, 2 CPU, Android 13)"
for game in "${TIER_B[@]}"; do
    create_emulator "$game" 3072 2 4 33 "TIER_B"
done

echo "🥉 CREAZIONE TIER C (2GB RAM, 2 CPU, Android 13)"
for game in "${TIER_C[@]}"; do
    create_emulator "$game" 2048 2 3 33 "TIER_C"
done

echo "🎯 CREAZIONE COMPLETATA!"
echo
echo "📊 RIEPILOGO:"
echo "   TIER S: ${#TIER_S[@]} emulatori (6GB RAM, 4 CPU)"
echo "   TIER A: ${#TIER_A[@]} emulatori (4GB RAM, 3 CPU)"  
echo "   TIER B: ${#TIER_B[@]} emulatori (3GB RAM, 2 CPU)"
echo "   TIER C: ${#TIER_C[@]} emulatori (2GB RAM, 2 CPU)"
echo
echo "   TOTALE: $((${#TIER_S[@]} + ${#TIER_A[@]} + ${#TIER_B[@]} + ${#TIER_C[@]})) emulatori"
echo
echo "🚀 EMULATORI PRONTI PER L'USO!"

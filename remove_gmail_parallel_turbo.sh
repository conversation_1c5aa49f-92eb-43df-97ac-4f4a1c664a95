#!/bin/bash

echo "🚀 RIMOZIONE GMAIL PARALLELA TURBO - 47 EMULATORI"
echo "==============================================="
echo "⚡ Modalità: PROCESSAMENTO PARALLELO"
echo "🔥 Velocità: MASSIMA POSSIBILE"
echo "⏰ Inizio: $(date +%H:%M:%S)"
echo ""

export ANDROID_AVD_HOME=~/.config/.android/avd

# Lista completa dei 47 emulatori
emulators=(
    "Ace_Racer" "Aether_Gazer" "Arknights" "Ash_Echoes" "ASTRA_Knights_of_Veda"
    "Black_Beacon" "Blood_Strike" "Brown_Dust_2" "Cat_Fantasy_Isekai_Adventure"
    "CookieRun_Kingdom" "CookieRun_OvenBreak" "CookieRun_Tower_of_Adventures"
    "CounterSide" "DanMachi_BATTLE_CHRONICLE" "Dislyte" "Echocalypse_Scarlet_Covenant"
    "Epic_Seven" "Etheria_Restart" "Eversoul" "Farlight_84" "Figure_Fantasy"
    "Genshin_Impact" "Girls_Frontline_2_Exilium" "Go_Go_Muffin" "Heaven_Burns_Red"
    "Higan_Eruthyll" "Honkai_Star_Rail" "Infinity_Nikki" "Jujutsu_Kaisen_Phantom_Parade"
    "MementoMori_AFKRPG" "Metal_Slug_Awakening" "Neural_Cloud" "NIKKE_Goddess_of_Victory"
    "Ni_no_Kuni_Cross_Worlds" "OUTERPLANE_Strategy_Anime" "Path_to_Nowhere"
    "Phantom_Blade_Executioners" "Punishing_Gray_Raven" "Reverse_1999"
    "Snowbreak_Containment_Zone" "Solo_Leveling_Arise" "STARSEED_Asnia_Trigger"
    "Tower_of_God_Great_Journey" "Tower_of_God_NEW_WORLD" "Uma_Musume_Pretty_Derby"
    "Wuthering_Waves" "Zenless_Zone_Zero"
)

# Funzione per processare un singolo emulatore
process_emulator() {
    local emulator_name="$1"
    local batch_num="$2"
    local emulator_num="$3"
    
    echo "🚀 [$batch_num] [$emulator_num] $(date +%H:%M:%S) - $emulator_name"
    
    # Verifica esistenza
    if [ ! -d "$emulator_name.avd" ]; then
        echo "❌ [$batch_num] [$emulator_num] Skip - $emulator_name non trovato"
        return 1
    fi
    
    # Avvio TURBO
    ~/Android/Sdk/emulator/emulator -avd "$emulator_name" \
        -no-window -no-audio -no-snapshot-save -no-snapshot-load \
        -memory 512 -cores 1 -gpu off -camera-back none -camera-front none \
        -no-boot-anim -netdelay none -netspeed full -wipe-data &
    local emulator_pid=$!
    
    echo "⏳ [$batch_num] [$emulator_num] Attesa 45s - $emulator_name"
    sleep 45
    
    # Verifica TURBO (max 3 minuti)
    local device_ready=false
    
    for attempt in {1..6}; do
        # Trova il device specifico per questo emulatore
        local device_id=$(~/Android/Sdk/platform-tools/adb devices 2>/dev/null | grep "emulator" | tail -1 | cut -f1)
        
        if [ -n "$device_id" ]; then
            local boot_completed=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell getprop sys.boot_completed 2>/dev/null | tr -d '\r\n')
            
            if [ "$boot_completed" = "1" ]; then
                echo "✅ [$batch_num] [$emulator_num] OK ($attempt) - $emulator_name"
                device_ready=true
                break
            fi
        fi
        
        sleep 30
    done
    
    if [ "$device_ready" = true ]; then
        # Rimozione TURBO
        local gmail_check=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages 2>/dev/null | grep "com.google.android.gm")
        
        if [ -n "$gmail_check" ]; then
            # Rimozione parallela
            ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm disable-user --user 0 com.google.android.gm 2>/dev/null &
            ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm uninstall --user 0 com.google.android.gm 2>/dev/null &
            ~/Android/Sdk/platform-tools/adb -s "$device_id" shell am force-stop com.google.android.gm 2>/dev/null &
            wait
            
            sleep 1
            
            # Verifica finale
            local gmail_after=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages 2>/dev/null | grep "com.google.android.gm")
            
            if [ -z "$gmail_after" ]; then
                echo "✅ [$batch_num] [$emulator_num] Gmail rimosso - $emulator_name"
                # Chiusura
                ~/Android/Sdk/platform-tools/adb -s "$device_id" emu kill 2>/dev/null &
                return 0
            else
                echo "❌ [$batch_num] [$emulator_num] Gmail presente - $emulator_name"
                ~/Android/Sdk/platform-tools/adb -s "$device_id" emu kill 2>/dev/null &
                return 1
            fi
        else
            echo "ℹ️ [$batch_num] [$emulator_num] Gmail già assente - $emulator_name"
            ~/Android/Sdk/platform-tools/adb -s "$device_id" emu kill 2>/dev/null &
            return 0
        fi
    else
        echo "❌ [$batch_num] [$emulator_num] Timeout - $emulator_name"
        kill $emulator_pid 2>/dev/null &
        return 1
    fi
}

# PROCESSAMENTO PARALLELO A BATCH
total_emulators=${#emulators[@]}
batch_size=3  # 3 emulatori contemporaneamente per non sovraccaricare
success=0
failed=0
start_time=$(date +%s)

echo "📱 Emulatori totali: $total_emulators"
echo "🔥 Batch size: $batch_size emulatori contemporaneamente"
echo "⚡ Ottimizzazioni TURBO:"
echo "   • RAM: 512MB (minimo assoluto)"
echo "   • Wipe data (avvio più veloce)"
echo "   • Attesa: 45s (ultra-ridotta)"
echo "   • Timeout: 3 minuti max"
echo ""

# Processa in batch
for ((i=0; i<total_emulators; i+=batch_size)); do
    batch_num=$(((i/batch_size)+1))
    echo "🚀 BATCH $batch_num - Avvio $batch_size emulatori contemporaneamente..."
    
    # Avvia batch di emulatori in parallelo
    pids=()
    for ((j=0; j<batch_size && (i+j)<total_emulators; j++)); do
        emulator_name="${emulators[$((i+j))]}"
        emulator_num=$((i+j+1))
        
        # Avvia in background
        process_emulator "$emulator_name" "$batch_num" "$emulator_num" &
        pids+=($!)
    done
    
    echo "⏳ BATCH $batch_num - Attesa completamento..."
    
    # Aspetta che tutti i processi del batch finiscano
    for pid in "${pids[@]}"; do
        if wait $pid; then
            ((success++))
        else
            ((failed++))
        fi
    done
    
    echo "✅ BATCH $batch_num completato"
    
    # Pulizia tra batch
    echo "🧹 Pulizia processi..."
    pkill -f "emulator" 2>/dev/null
    sleep 10
    
    # Statistiche parziali
    processed=$((success + failed))
    elapsed=$(($(date +%s) - start_time))
    
    echo "📊 BATCH $batch_num - Processati: $processed/$total_emulators | ✅$success ❌$failed"
    echo "⏱️ Tempo trascorso: $((elapsed / 60))m | Velocità: $((processed * 60 / elapsed)) emulatori/ora"
    echo ""
done

# Pulizia finale
echo "🧹 Pulizia finale..."
pkill -f "emulator" 2>/dev/null
~/Android/Sdk/platform-tools/adb kill-server 2>/dev/null
~/Android/Sdk/platform-tools/adb start-server 2>/dev/null
sleep 5

# Statistiche finali
total_time=$(($(date +%s) - start_time))
echo ""
echo "🚀 PROCESSAMENTO PARALLELO TURBO COMPLETATO!"
echo "==========================================="
echo "⏰ Fine: $(date +%H:%M:%S)"
echo "📊 Risultati finali:"
echo "   • Emulatori processati: $total_emulators"
echo "   • Successi: $success"
echo "   • Errori: $failed"
echo "   • Tasso successo: $((success * 100 / total_emulators))%"
echo "   • Tempo totale: $((total_time / 60))m $((total_time % 60))s"
echo "   • Velocità media: $((total_emulators * 60 / total_time)) emulatori/ora"
echo "   • Tempo per emulatore: $((total_time / total_emulators))s"
echo ""

if [ $success -gt $((total_emulators * 80 / 100)) ]; then
    echo "🎉 ECCELLENTE! Oltre l'80% completato con successo!"
elif [ $success -gt $((total_emulators * 60 / 100)) ]; then
    echo "✅ BUONO! Oltre il 60% completato"
else
    echo "⚠️ PROBLEMI! Molti errori - sistema potrebbe essere sovraccarico"
fi

echo ""
echo "🚀 Script PARALLELO TURBO terminato"

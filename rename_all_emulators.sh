#!/bin/bash

# Script per rinominare tutti i 47 emulatori rimuovendo "Gaming_" e "_Android14"
# Risultato: solo il nome del gioco specifico

echo "🔄 Rinominazione di tutti i 47 emulatori..."
echo "📝 Rimozione di 'Gaming_' e '_Android14' dai nomi"
echo ""

# Directory degli AVD
AVD_DIR="$HOME/.config/.android/avd"

# Contatore per il progresso
count=0
total=47

# Array con i nomi attuali e nuovi
declare -A rename_map=(
    ["Gaming_ASTRA_Knights_of_Veda_Android14"]="ASTRA_Knights_of_Veda"
    ["Gaming_Aether_Gazer_Android14"]="Aether_Gazer"
    ["Gaming_Ash_Echoes_Android14"]="Ash_Echoes"
    ["Gaming_Blood_Strike_Android14"]="Blood_Strike"
    ["Gaming_Brown_Dust_2_Android14"]="Brown_Dust_2"
    ["Gaming_Genshin_Impact_Android14"]="Genshin_Impact"
    ["Gaming_Honkai_Star_Rail_Android14"]="Honkai_Star_Rail"
    ["Gaming_Zenless_Zone_Zero_Android14"]="Zenless_Zone_Zero"
    ["Gaming_Infinity_Nikki_Android14"]="Infinity_Nikki"
    ["Gaming_NIKKE_Goddess_of_Victory_Android14"]="NIKKE_Goddess_of_Victory"
    ["Gaming_Epic_Seven_Android14"]="Epic_Seven"
    ["Gaming_Arknights_Android14"]="Arknights"
    ["Gaming_Punishing_Gray_Raven_Android14"]="Punishing_Gray_Raven"
    ["Gaming_Reverse_1999_Android14"]="Reverse_1999"
    ["Gaming_Solo_Leveling_Arise_Android14"]="Solo_Leveling_Arise"
    ["Gaming_Snowbreak_Containment_Zone_Android14"]="Snowbreak_Containment_Zone"
    ["Gaming_Dislyte_Android14"]="Dislyte"
    ["Gaming_CookieRun_Kingdom_Android14"]="CookieRun_Kingdom"
    ["Gaming_Path_to_Nowhere_Android14"]="Path_to_Nowhere"
    ["Gaming_CounterSide_Android14"]="CounterSide"
    ["Gaming_Eversoul_Android14"]="Eversoul"
    ["Gaming_Ace_Racer_Android14"]="Ace_Racer"
    ["Gaming_Jujutsu_Kaisen_Phantom_Parade_Android14"]="Jujutsu_Kaisen_Phantom_Parade"
    ["Gaming_Higan_Eruthyll_Android14"]="Higan_Eruthyll"
    ["Gaming_MementoMori_AFKRPG_Android14"]="MementoMori_AFKRPG"
    ["Gaming_Figure_Fantasy_Android14"]="Figure_Fantasy"
    ["Gaming_Tower_of_God_NEW_WORLD_Android14"]="Tower_of_God_NEW_WORLD"
    ["Gaming_Echocalypse_Scarlet_Covenant_Android14"]="Echocalypse_Scarlet_Covenant"
    ["Gaming_OUTERPLANE_Strategy_Anime_Android14"]="OUTERPLANE_Strategy_Anime"
    ["Gaming_Uma_Musume_Pretty_Derby_Android14"]="Uma_Musume_Pretty_Derby"
    ["Gaming_CookieRun_OvenBreak_Android14"]="CookieRun_OvenBreak"
    ["Gaming_CookieRun_Tower_of_Adventures_Android14"]="CookieRun_Tower_of_Adventures"
    ["Gaming_Cat_Fantasy_Isekai_Adventure_Android14"]="Cat_Fantasy_Isekai_Adventure"
    ["Gaming_Go_Go_Muffin_Android14"]="Go_Go_Muffin"
    ["Gaming_DanMachi_BATTLE_CHRONICLE_Android14"]="DanMachi_BATTLE_CHRONICLE"
    ["Gaming_Farlight_84_Android14"]="Farlight_84"
    ["Gaming_Girls_Frontline_2_Exilium_Android14"]="Girls_Frontline_2_Exilium"
    ["Gaming_Heaven_Burns_Red_Android14"]="Heaven_Burns_Red"
    ["Gaming_Etheria_Restart_Android14"]="Etheria_Restart"
    ["Gaming_Black_Beacon_Android14"]="Black_Beacon"
    ["Gaming_Metal_Slug_Awakening_Android14"]="Metal_Slug_Awakening"
    ["Gaming_Ni_no_Kuni_Cross_Worlds_Android14"]="Ni_no_Kuni_Cross_Worlds"
    ["Gaming_Phantom_Blade_Executioners_Android14"]="Phantom_Blade_Executioners"
    ["Gaming_STARSEED_Asnia_Trigger_Android14"]="STARSEED_Asnia_Trigger"
    ["Gaming_Neural_Cloud_Android14"]="Neural_Cloud"
    ["Gaming_Tower_of_God_Great_Journey_Android14"]="Tower_of_God_Great_Journey"
    ["Gaming_Wuthering_Waves_Android14"]="Wuthering_Waves"
)

# Funzione per rinominare un singolo emulatore
rename_avd() {
    local old_name=$1
    local new_name=$2
    
    echo "🔄 Rinominando $((++count))/$total: $old_name → $new_name"
    
    # File da rinominare
    local old_avd_dir="$AVD_DIR/${old_name}.avd"
    local old_ini_file="$AVD_DIR/${old_name}.ini"
    local new_avd_dir="$AVD_DIR/${new_name}.avd"
    local new_ini_file="$AVD_DIR/${new_name}.ini"
    
    # Verifica che i file esistano
    if [ -d "$old_avd_dir" ] && [ -f "$old_ini_file" ]; then
        # Rinomina la directory .avd
        mv "$old_avd_dir" "$new_avd_dir"
        
        # Rinomina il file .ini
        mv "$old_ini_file" "$new_ini_file"
        
        # Aggiorna il contenuto del file .ini
        sed -i "s|${old_name}|${new_name}|g" "$new_ini_file"
        
        # Aggiorna il file config.ini dentro la directory .avd
        local config_file="$new_avd_dir/config.ini"
        if [ -f "$config_file" ]; then
            sed -i "s|${old_name}|${new_name}|g" "$config_file"
        fi
        
        echo "✅ $new_name rinominato con successo!"
    else
        echo "❌ Errore: File non trovati per $old_name"
    fi
    
    echo ""
}

# Esegui la rinominazione per tutti gli emulatori
echo "🎮 Inizio rinominazione di tutti i 47 emulatori..."
echo ""

for old_name in "${!rename_map[@]}"; do
    new_name="${rename_map[$old_name]}"
    rename_avd "$old_name" "$new_name"
    sleep 0.5  # Piccola pausa per evitare sovraccarico
done

echo ""
echo "🎉 RINOMINAZIONE COMPLETATA!"
echo ""
echo "📊 Verifica finale:"
ls "$AVD_DIR" | grep -E "\.(avd|ini)$" | head -10
echo "..."
echo "Totale file: $(ls "$AVD_DIR" | grep -E "\.(avd|ini)$" | wc -l)"
echo ""
echo "✅ Tutti gli emulatori ora hanno solo il nome del gioco specifico!"
echo "📁 Percorso: $AVD_DIR"

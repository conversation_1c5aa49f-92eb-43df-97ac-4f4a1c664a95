#!/bin/bash
# Completa la creazione di tutti i 31 AVD con Play Store

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}COMPLETAMENTO 31 AVD CON PLAY STORE${NC}"
echo "Aggiunge i restanti 26 AVD ai 5 già creati"
echo "Data: $(date)"
echo "======================================================="
echo ""

export ANDROID_HOME=/home/<USER>/Android/Sdk

section "CREAZIONE AVD RIMANENTI"

# Funzione per creare AVD
create_avd() {
    local name="$1"
    local device="$2"
    local ram="$3"
    local cores="$4"
    local storage="$5"
    local gpu="$6"
    local api="$7"
    
    info "Creazione: $name"
    
    echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
        -n "$name" \
        -k "system-images;$api;google_apis_playstore;x86_64" \
        -d "$device" \
        --force
    
    # Configura performance
    if [ -d ~/.android/avd/$name.avd ]; then
        cat >> ~/.android/avd/$name.avd/config.ini << EOF

# === PLAY STORE GAMING OPTIMIZED ===
PlayStore.enabled=true
tag.id=google_apis_playstore
hw.ramSize=$ram
hw.cpu.ncore=$cores
vm.heapSize=$((ram/16))
hw.gpu.enabled=yes
hw.gpu.mode=$gpu
hw.keyboard=yes
disk.dataPartition.size=${storage}G
fastboot.forceColdBoot=yes
EOF
        log "$name configurato ($ram MB RAM, $cores cores)"
    fi
}

# GACHA/RPG GAMES (15 AVD rimanenti)
info "Creazione 15 AVD Gacha/RPG rimanenti..."

create_avd "Aether_Gazer_PlayStore" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_avd "Ash_Echoes_PlayStore" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_avd "Brown_Dust_2_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"
create_avd "Danchro_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"
create_avd "Honkai_Star_Rail_PlayStore" "pixel_7_pro" "8192" "8" "48" "host" "android-34"
create_avd "Honkai_Impact_3rd_PlayStore" "pixel_7_pro" "8192" "8" "48" "host" "android-34"
create_avd "Zenless_Zone_Zero_PlayStore" "pixel_7_pro" "8192" "8" "48" "host" "android-34"
create_avd "Infinity_Nikki_PlayStore" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_avd "Nikke_PlayStore" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_avd "Ni_no_Kuni_Cross_Worlds_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"
create_avd "Etheria_Restart_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"
create_avd "Figure_Fantasy_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"
create_avd "Reverse_1999_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"
create_avd "Solo_Leveling_Arise_PlayStore" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_avd "Seven_Deadly_Sins_Grand_Cross_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"

# ACTION/SHOOTER GAMES (4 AVD rimanenti)
info "Creazione 4 AVD Action/Shooter rimanenti..."

create_avd "Metal_Slug_Awakening_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"
create_avd "Phantom_Blade_Executioners_PlayStore" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_avd "Black_Beacon_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"
create_avd "Snowbreak_Containment_Zone_PlayStore" "pixel_7_pro" "8192" "8" "32" "host" "android-34"

# GACHA/RPG GAMES (5 AVD rimanenti)
info "Creazione 5 AVD Gacha/RPG finali..."

create_avd "Punishing_Gray_Raven_PlayStore" "pixel_7_pro" "8192" "8" "32" "host" "android-34"
create_avd "Wuthering_Waves_PlayStore" "pixel_7_pro" "8192" "8" "48" "host" "android-34"
create_avd "Astra_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"

# CASUAL/PUZZLE GAMES (3 AVD rimanenti)
info "Creazione 3 AVD Casual rimanenti..."

create_avd "Cookie_Run_Ovenbreak_PlayStore" "pixel_6" "4096" "4" "16" "auto" "android-34"
create_avd "Cat_Fantasy_PlayStore" "pixel_6" "4096" "4" "16" "auto" "android-34"
create_avd "One_Human_PlayStore" "pixel_6" "4096" "4" "16" "auto" "android-34"

# SPECIAL GAMES (1 AVD rimanente)
info "Creazione 1 AVD Special..."

create_avd "Fairlight84_PlayStore" "pixel_7" "6144" "6" "24" "host" "android-34"

section "VERIFICA FINALE"

# Conta AVD totali
TOTAL_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds | wc -l)
log "Totale AVD creati: $TOTAL_AVDS"

# Lista tutti gli AVD
info "Lista completa AVD:"
$ANDROID_HOME/emulator/emulator -list-avds | while read avd; do
    if [ -n "$avd" ]; then
        echo "  ✓ $avd"
    fi
done

echo ""
echo "======================================================="
echo -e "${GREEN}31 AVD COMPLETI CON PLAY STORE!${NC}"
echo "======================================================="
echo ""
echo "CONFIGURAZIONE FINALE:"
echo ""
echo "📱 ${CYAN}31 AVD CREATI:${NC}"
echo "• Gacha/RPG: 20 AVD (6-8GB RAM, gaming ottimizzato)"
echo "• Action/Shooter: 5 AVD (6-8GB RAM, performance focus)"
echo "• Casual/Puzzle: 4 AVD (4GB RAM, bilanciato)"
echo "• Racing: 1 AVD (8GB RAM, racing ottimizzato)"
echo "• Special: 1 AVD (6GB RAM, versatile)"
echo ""
echo "✅ ${CYAN}CARATTERISTICHE:${NC}"
echo "• Play Store: Abilitato su tutti gli AVD"
echo "• Gmail: Da rimuovere dopo primo avvio"
echo "• GPU: RTX 4080 host mode per gaming"
echo "• CPU: i9-12900KF ottimizzato (4-8 cores per AVD)"
echo "• RAM: 4-8GB per AVD secondo le esigenze"
echo ""
echo "🎮 ${CYAN}GIOCHI SUPPORTATI:${NC}"
echo "Ogni AVD è dedicato a un gioco specifico:"
echo "• Genshin_Impact_PlayStore_Test → Genshin Impact"
echo "• Blood_Strike_PlayStore_Test → Blood Strike"
echo "• Epic_Seven_PlayStore_Test → Epic Seven"
echo "• Cookie_Run_PlayStore_Test → Cookie Run Kingdom"
echo "• Ace_Racer_PlayStore_Test → Ace Racer"
echo "• E tutti gli altri 26 giochi della lista!"
echo ""
echo "🚀 ${CYAN}UTILIZZO:${NC}"
echo ""
echo "1. ${YELLOW}Android Studio è già aperto${NC}"
echo "2. ${YELLOW}Tools → Virtual Device Manager${NC}"
echo "3. ${YELLOW}Seleziona AVD per il gioco desiderato${NC}"
echo "4. ${YELLOW}Clicca Play (▶️) per avviare${NC}"
echo "5. ${YELLOW}Apri Play Store nell'emulatore${NC}"
echo "6. ${YELLOW}Cerca e installa il gioco${NC}"
echo "7. ${YELLOW}Rimuovi Gmail: ./remove_gmail_from_emulators.sh${NC}"
echo ""
echo "SPAZIO UTILIZZATO: $(du -sh ~/.android/avd 2>/dev/null | cut -f1)"
echo ""
echo -e "${GREEN}SISTEMA GAMING ANDROID COMPLETO AL 100%!${NC} 🎮"
echo ""
echo "Ogni gioco ha il suo emulatore dedicato con Play Store"
echo "per download diretto e performance ottimali su RTX 4080!"

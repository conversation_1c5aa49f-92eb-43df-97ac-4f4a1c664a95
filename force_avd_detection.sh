#!/bin/bash
# Forza Android Studio a rilevare gli AVD

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== FORZA RILEVAMENTO AVD IN ANDROID STUDIO ==="
echo "Data: $(date)"
echo "==============================================="
echo ""

# Termina Android Studio se in esecuzione
info "Terminazione Android Studio..."
pkill -f android-studio 2>/dev/null || true
sleep 3

# Rimuovi variabili ambiente problematiche
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME

# Imposta solo le variabili essenziali
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

log "Variabili ambiente pulite"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "ANDROID_AVD_HOME: (non impostato - usa default)"
echo ""

# Assicurati che la directory standard esista
mkdir -p ~/.android/avd
log "Directory ~/.android/avd verificata"

# Verifica e correggi i file .ini
info "Correzione file .ini per Android Studio..."
for ini_file in ~/.android/avd/*.ini; do
    if [ -f "$ini_file" ]; then
        avd_name=$(basename "$ini_file" .ini)
        
        # Correggi il path nel file .ini
        avd_path="$HOME/.android/avd/$avd_name.avd"
        
        if [ -d "$avd_path" ]; then
            # Aggiorna il path nel file .ini
            cat > "$ini_file" << EOF
avd.ini.encoding=UTF-8
path=$avd_path
path.rel=avd/$avd_name.avd
target=android-34
EOF
            log "File .ini corretto: $avd_name"
        else
            error "Directory AVD non trovata: $avd_path"
        fi
    fi
done
echo ""

# Verifica che i config.ini siano corretti
info "Verifica file config.ini..."
for avd_dir in ~/.android/avd/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir" .avd)
        config_file="$avd_dir/config.ini"
        
        if [ -f "$config_file" ]; then
            # Verifica che il system image path sia corretto
            if ! grep -q "image.sysdir.1=" "$config_file"; then
                # Aggiungi system image path se mancante
                case "$avd_name" in
                    *"Android13"*)
                        echo "image.sysdir.1=system-images/android-33/google_apis/x86_64/" >> "$config_file"
                        ;;
                    *"Android14"*)
                        echo "image.sysdir.1=system-images/android-34/google_apis/x86_64/" >> "$config_file"
                        ;;
                    *"Android15"*)
                        echo "image.sysdir.1=system-images/android-35/google_apis/x86_64/" >> "$config_file"
                        ;;
                esac
                log "System image path aggiunto a $avd_name"
            fi
            
            log "Config.ini verificato: $avd_name"
        else
            error "Config.ini mancante: $avd_name"
        fi
    fi
done
echo ""

# Pulisci cache Android Studio
info "Pulizia cache Android Studio..."
rm -rf ~/.cache/Google/AndroidStudio*/caches/* 2>/dev/null || true
rm -rf ~/.cache/Google/AndroidStudio*/tmp/* 2>/dev/null || true
rm -rf ~/.config/Google/AndroidStudio*/options/recentProjects.xml 2>/dev/null || true
log "Cache pulita"
echo ""

# Test con emulatore command line
info "Test rilevamento AVD da command line..."
if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    echo "AVD rilevati dall'emulatore:"
    $ANDROID_HOME/emulator/emulator -list-avds | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
else
    error "Emulatore non trovato"
fi
echo ""

# Crea script per avviare Android Studio con ambiente pulito
cat > /home/<USER>/start_android_studio_clean.sh << 'EOF'
#!/bin/bash
# Avvia Android Studio con ambiente pulito per AVD

# Pulisci variabili ambiente problematiche
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME

# Imposta solo variabili essenziali
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/emulator:$ANDROID_HOME/platform-tools

echo "=== AVVIO ANDROID STUDIO (AMBIENTE PULITO) ==="
echo "ANDROID_HOME: $ANDROID_HOME"
echo "AVD Directory: ~/.android/avd (default)"
echo "=============================================="

# Avvia Android Studio
android-studio &

echo "Android Studio avviato!"
echo ""
echo "ISTRUZIONI:"
echo "1. Vai su Tools → AVD Manager"
echo "2. Dovresti vedere gli AVD nella lista"
echo "3. Se non li vedi, clicca su 'Refresh' o 'Reload'"
echo ""
echo "AVD disponibili:"
$ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null | while read avd; do
    if [ -n "$avd" ]; then
        echo "  - $avd"
    fi
done
EOF

chmod +x /home/<USER>/start_android_studio_clean.sh
log "Script avvio pulito creato: start_android_studio_clean.sh"
echo ""

# Verifica finale
info "Verifica finale configurazione:"
AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
log "File .ini trovati: $AVD_COUNT"

AVD_DIRS=$(ls -d ~/.android/avd/*.avd 2>/dev/null | wc -l)
log "Directory .avd trovate: $AVD_DIRS"

if [ "$AVD_COUNT" -eq "$AVD_DIRS" ] && [ "$AVD_COUNT" -gt 0 ]; then
    log "Configurazione AVD corretta!"
else
    warn "Possibili problemi nella configurazione AVD"
fi

echo ""
echo "==============================================="
echo -e "${GREEN}CONFIGURAZIONE COMPLETATA!${NC}"
echo "==============================================="
echo ""
echo "PROSSIMI PASSI:"
echo ""
echo "1. Avvia Android Studio con ambiente pulito:"
echo "   ./start_android_studio_clean.sh"
echo ""
echo "2. In Android Studio:"
echo "   - Vai su Tools → AVD Manager"
echo "   - Gli AVD dovrebbero essere visibili"
echo "   - Se non li vedi, clicca 'Refresh'"
echo ""
echo "3. AVD configurati:"
for ini_file in ~/.android/avd/*.ini; do
    if [ -f "$ini_file" ]; then
        avd_name=$(basename "$ini_file" .ini)
        echo "   ✓ $avd_name"
    fi
done
echo ""
echo "4. Se ancora non funziona:"
echo "   - Riavvia completamente Android Studio"
echo "   - Verifica File → Settings → System Settings → Android SDK"
echo "   - Controlla che SDK path sia: $ANDROID_HOME"
echo ""

echo -e "${BLUE}Gli AVD dovrebbero ora essere visibili nel Virtual Device Manager!${NC}"

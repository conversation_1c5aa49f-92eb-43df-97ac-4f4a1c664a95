#!/bin/bash

# Script per ripristinare le configurazioni gaming originali degli emulatori

echo "🔄 RIPRISTINO CONFIGURAZIONI GAMING ORIGINALI"
echo "============================================="
echo ""

# Trova la cartella di backup più recente
BACKUP_DIR=$(ls -td emulator_configs_backup_* 2>/dev/null | head -1)

if [ -z "$BACKUP_DIR" ]; then
    echo "❌ Nessun backup trovato!"
    echo "Ricreo le configurazioni gaming ottimali..."
    
    # Lista emulatori
    emulators=(
        "ASTRA_Knights_of_Veda" "Ace_Racer" "Aether_Gazer" "Arknights" "Ash_Echoes"
        "Black_Beacon" "Blood_Strike" "Brown_Dust_2" "Cat_Fantasy_Isekai_Adventure"
        "CookieRun_Kingdom" "CookieRun_OvenBreak" "CookieRun_Tower_of_Adventures"
        "CounterSide" "DanMachi_BATTLE_CHRONICLE" "Dislyte" "Echocalypse_Scarlet_Covenant"
        "Epic_Seven" "Etheria_Restart" "Eversoul" "Farlight_84" "Figure_Fantasy"
        "Genshin_Impact" "Girls_Frontline_2_Exilium" "Go_Go_Muffin" "Heaven_Burns_Red"
        "Higan_Eruthyll" "Honkai_Star_Rail" "Infinity_Nikki" "Jujutsu_Kaisen_Phantom_Parade"
        "MementoMori_AFKRPG" "Metal_Slug_Awakening" "Neural_Cloud" "NIKKE_Goddess_of_Victory"
        "Ni_no_Kuni_Cross_Worlds" "OUTERPLANE_Strategy_Anime" "Path_to_Nowhere"
        "Phantom_Blade_Executioners" "Punishing_Gray_Raven" "Reverse_1999"
        "Snowbreak_Containment_Zone" "Solo_Leveling_Arise" "STARSEED_Asnia_Trigger"
        "Tower_of_God_Great_Journey" "Tower_of_God_NEW_WORLD" "Uma_Musume_Pretty_Derby"
        "Wuthering_Waves" "Zenless_Zone_Zero"
    )
    
    for emulator_name in "${emulators[@]}"; do
        config_file="$HOME/.config/.android/avd/${emulator_name}.avd/config.ini"
        
        echo "🎮 Ripristinando: $emulator_name"
        
        # Ricrea configurazione gaming ottimale
        cat > "$config_file" << EOF
# === CONFIGURAZIONI GAMING OTTIMIZZATE 2025 ===
avd.ini.displayname=$emulator_name
avd.ini.encoding=UTF-8
AvdId=$emulator_name
PlayStore.enabled=yes

# System image
image.sysdir.1=system-images/android-34/google_apis/x86_64/
tag.display=Google APIs
tag.id=google_apis

# === SPECIFICHE GAMING OTTIMALI ===
hw.ramSize=6144
vm.heapSize=512
hw.cpu.ncore=4
hw.gpu.enabled=yes
hw.gpu.mode=swiftshader_indirect

# Display gaming ottimale
hw.lcd.width=1440
hw.lcd.height=3120
hw.lcd.density=560

# Audio/Input per gaming
hw.audioInput=yes
hw.audioOutput=yes
hw.camera.back=webcam0
hw.camera.front=webcam0
hw.sensors.orientation=yes
hw.sensors.proximity=yes

# Storage gaming
disk.dataPartition.size=8192MB
hw.sdCard=yes
sdcard.size=1024MB

# Network completo
hw.wifi=yes
hw.gps=yes

# Input gaming
hw.keyboard=yes
hw.dPad=yes
hw.trackBall=no
hw.mainKeys=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.device.manufacturer=Google
hw.device.name=pixel_4

# Ottimizzazioni performance
hw.arc=false
hw.initialOrientation=Portrait
showDeviceFrame=no
EOF
        echo "   ✅ Configurazione gaming ripristinata"
    done
    
else
    echo "📁 Backup trovato: $BACKUP_DIR"
    echo "🔄 Ripristino configurazioni originali..."
    
    restored=0
    for backup_file in "$BACKUP_DIR"/*.backup; do
        if [ -f "$backup_file" ]; then
            # Estrai nome emulatore dal file backup
            filename=$(basename "$backup_file")
            emulator_name=${filename%_config.ini.backup}
            
            config_file="$HOME/.config/.android/avd/${emulator_name}.avd/config.ini"
            
            if [ -f "$config_file" ]; then
                cp "$backup_file" "$config_file"
                echo "✅ Ripristinato: $emulator_name"
                ((restored++))
            else
                echo "⚠️  File config non trovato: $emulator_name"
            fi
        fi
    done
    
    echo ""
    echo "📊 Configurazioni ripristinate: $restored"
fi

echo ""
echo "🎉 RIPRISTINO COMPLETATO!"
echo ""
echo "🎮 SPECIFICHE GAMING RIPRISTINATE:"
echo "   🧠 RAM: 6GB"
echo "   🖥️  CPU: 4 core"
echo "   📱 Risoluzione: 1440x3120"
echo "   🔊 Audio: Abilitato"
echo "   📷 Camera: Abilitata"
echo "   💾 Storage: 8GB + 1GB SD"
echo ""
echo "✅ I tuoi emulatori sono pronti per il gaming!"

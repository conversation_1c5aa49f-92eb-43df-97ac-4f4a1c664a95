#!/bin/bash

echo "🗑️ RIMOZIONE GMAIL DA TUTTI I 47 EMULATORI"
echo "=========================================="
echo "⏰ Inizio: $(date +%H:%M:%S)"
echo ""

export ANDROID_AVD_HOME=~/.config/.android/avd

# Lista completa dei 47 emulatori
emulators=(
    "Ace_Racer" "Aether_Gazer" "Arknights" "Ash_Echoes" "ASTRA_Knights_of_Veda"
    "Black_Beacon" "Blood_Strike" "Brown_Dust_2" "Cat_Fantasy_Isekai_Adventure"
    "CookieRun_Kingdom" "CookieRun_OvenBreak" "CookieRun_Tower_of_Adventures"
    "CounterSide" "DanMachi_BATTLE_CHRONICLE" "Dislyte" "Echocalypse_Scarlet_Covenant"
    "Epic_Seven" "Etheria_Restart" "Eversoul" "Farlight_84" "Figure_Fantasy"
    "Genshin_Impact" "Girls_Frontline_2_Exilium" "Go_Go_Muffin" "Heaven_Burns_Red"
    "Higan_Eruthyll" "Honkai_Star_Rail" "Infinity_Nikki" "Jujutsu_Kaisen_Phantom_Parade"
    "MementoMori_AFKRPG" "Metal_Slug_Awakening" "Neural_Cloud" "NIKKE_Goddess_of_Victory"
    "Ni_no_Kuni_Cross_Worlds" "OUTERPLANE_Strategy_Anime" "Path_to_Nowhere"
    "Phantom_Blade_Executioners" "Punishing_Gray_Raven" "Reverse_1999"
    "Snowbreak_Containment_Zone" "Solo_Leveling_Arise" "STARSEED_Asnia_Trigger"
    "Tower_of_God_Great_Journey" "Tower_of_God_NEW_WORLD" "Uma_Musume_Pretty_Derby"
    "Wuthering_Waves" "Zenless_Zone_Zero"
)

total_emulators=${#emulators[@]}
current=0
success=0
failed=0
start_time=$(date +%s)

echo "📱 Emulatori da processare: $total_emulators"
echo "🎯 Obiettivo: Rimuovere Gmail mantenendo PlayStore"
echo ""

for emulator_name in "${emulators[@]}"; do
    ((current++))
    echo "🔄 [$current/$total_emulators] $(date +%H:%M:%S) - $emulator_name"
    
    # Verifica che l'emulatore esista
    if [ ! -d "$emulator_name.avd" ]; then
        echo "   ❌ Emulatore non trovato, skip"
        ((failed++))
        continue
    fi
    
    echo "   🚀 Avvio emulatore..."
    # Avvia con parametri ottimizzati per velocità
    ~/Android/Sdk/emulator/emulator -avd "$emulator_name" \
        -no-window -no-audio -no-snapshot-save \
        -memory 2048 -cores 2 &
    emulator_pid=$!
    
    echo "   ⏳ Attesa avvio (3 minuti)..."
    sleep 180
    
    # Verifica connessione con pazienza (max 10 minuti totali)
    device_ready=false
    echo "   🔍 Verifica connessione ADB..."
    
    for attempt in {1..20}; do
        # Verifica se ADB vede il dispositivo
        if ~/Android/Sdk/platform-tools/adb devices 2>/dev/null | grep -q "emulator"; then
            # Verifica se il sistema è completamente avviato
            boot_completed=$(~/Android/Sdk/platform-tools/adb shell getprop sys.boot_completed 2>/dev/null | tr -d '\r\n')
            
            if [ "$boot_completed" = "1" ]; then
                echo "   ✅ Connesso (tentativo $attempt)"
                device_ready=true
                break
            else
                echo "   ⏳ Tentativo $attempt/20 - Boot: '$boot_completed'"
            fi
        else
            echo "   ⏳ Tentativo $attempt/20 - Dispositivo non rilevato"
        fi
        
        sleep 30
    done
    
    if [ "$device_ready" = true ]; then
        echo "   📊 Analisi pacchetti..."
        
        # Verifica PlayStore
        playstore=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.android.vending")
        if [ -n "$playstore" ]; then
            echo "   ✅ PlayStore presente"
        else
            echo "   ⚠️ PlayStore non trovato!"
        fi
        
        # Verifica Gmail prima della rimozione
        gmail_before=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
        
        if [ -n "$gmail_before" ]; then
            echo "   📧 Gmail presente, procedo con rimozione..."
            
            # Processo di rimozione Gmail
            echo "   🗑️ Step 1: Disable user..."
            ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null
            sleep 2
            
            echo "   🗑️ Step 2: Uninstall user..."
            ~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm 2>/dev/null
            sleep 2
            
            echo "   🗑️ Step 3: Force stop..."
            ~/Android/Sdk/platform-tools/adb shell am force-stop com.google.android.gm 2>/dev/null
            sleep 2
            
            echo "   🗑️ Step 4: Clear data..."
            ~/Android/Sdk/platform-tools/adb shell pm clear com.google.android.gm 2>/dev/null
            sleep 3
            
            # Verifica rimozione
            echo "   🔍 Verifica rimozione..."
            gmail_after=$(~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep "com.google.android.gm")
            
            if [ -z "$gmail_after" ]; then
                echo "   ✅ Gmail rimosso con successo!"
                ((success++))
            else
                echo "   ❌ Gmail ancora presente: $gmail_after"
                ((failed++))
            fi
            
        else
            echo "   ℹ️ Gmail già assente"
            ((success++))
        fi
        
    else
        echo "   ❌ Timeout - Emulatore non si è avviato"
        ((failed++))
    fi
    
    # Chiusura emulatore
    echo "   🛑 Chiusura emulatore..."
    ~/Android/Sdk/platform-tools/adb emu kill 2>/dev/null
    sleep 2
    kill $emulator_pid 2>/dev/null
    pkill -f "emulator.*$emulator_name" 2>/dev/null
    
    # Pausa tra emulatori
    echo "   💤 Pausa pulizia (15 secondi)..."
    sleep 15
    
    # Statistiche parziali
    elapsed=$(($(date +%s) - start_time))
    avg_time=$((elapsed / current))
    remaining=$((total_emulators - current))
    eta=$((remaining * avg_time))
    
    echo "   📊 Progresso: $current/$total_emulators | Successi: $success | Errori: $failed"
    echo "   ⏱️ Tempo medio: ${avg_time}s | ETA: $((eta / 3600))h $((eta % 3600 / 60))m"
    echo ""
done

# Statistiche finali
total_time=$(($(date +%s) - start_time))
echo ""
echo "🎉 PROCESSO COMPLETATO!"
echo "======================"
echo "⏰ Fine: $(date +%H:%M:%S)"
echo "📊 Risultati:"
echo "   • Emulatori processati: $total_emulators"
echo "   • Successi: $success"
echo "   • Errori: $failed"
echo "   • Tasso successo: $((success * 100 / total_emulators))%"
echo "   • Tempo totale: $((total_time / 3600))h $((total_time % 3600 / 60))m"
echo "   • Tempo medio per emulatore: $((total_time / total_emulators))s"
echo ""

if [ $success -eq $total_emulators ]; then
    echo "🎉 PERFETTO! Gmail rimosso da tutti gli emulatori!"
elif [ $success -gt $((total_emulators * 80 / 100)) ]; then
    echo "✅ BUONO! Gmail rimosso dalla maggior parte degli emulatori"
    echo "🔧 $failed emulatori potrebbero richiedere intervento manuale"
else
    echo "⚠️ PROBLEMI! Solo $success/$total_emulators completati con successo"
    echo "🔧 Potrebbe servire debugging del processo"
fi

echo ""
echo "✅ Script terminato"

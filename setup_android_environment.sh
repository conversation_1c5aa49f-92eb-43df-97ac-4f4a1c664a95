#!/bin/bash
# Setup Android Development Environment
# Ottimizzato per i9-12900KF + RTX 4080 + Arch Linux + Hyprland

set -e

echo "=== ANDROID DEVELOPMENT ENVIRONMENT SETUP ==="
echo "Data: $(date)"
echo "=============================================="

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funzione per logging
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verifica se Android Studio è installato
if ! command -v android-studio &> /dev/null; then
    error "Android Studio non trovato. Installalo prima di continuare."
    exit 1
fi

log "Android Studio trovato: $(which android-studio)"

# Configura variabili ambiente
log "Configurazione variabili ambiente..."

# Backup del bashrc esistente
if [ -f ~/.bashrc ]; then
    cp ~/.bashrc ~/.bashrc.backup.android_$(date +%Y%m%d_%H%M%S)
    log "Backup di .bashrc creato"
fi

# Aggiungi configurazioni Android a .bashrc
cat >> ~/.bashrc << 'EOF'

# === ANDROID DEVELOPMENT ENVIRONMENT ===
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk
export ANDROID_AVD_HOME=/home/<USER>/Android/AVD/avd-configs
export ANDROID_EMULATOR_HOME=/home/<USER>/Android/AVD

# PATH per Android tools
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin

# Ottimizzazioni per il sistema
export ANDROID_EMULATOR_USE_SYSTEM_LIBS=1
export QT_X11_NO_MITSHM=1
export _JAVA_AWT_WM_NONREPARENTING=1

# Ottimizzazioni JVM per Android Studio
export _JAVA_OPTIONS="-Xmx8g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# Ottimizzazioni GPU NVIDIA
export __GL_SYNC_TO_VBLANK=0
export __GL_THREADED_OPTIMIZATIONS=1

# === END ANDROID ENVIRONMENT ===
EOF

log "Variabili ambiente aggiunte a .bashrc"

# Ricarica ambiente
source ~/.bashrc

# Crea struttura directory
log "Creazione struttura directory..."
mkdir -p /home/<USER>/Android/{Sdk,AVD,Projects,Scripts,Logs}
mkdir -p /home/<USER>/Android/AVD/{avd-configs,snapshots,backups,data,temp}
mkdir -p /home/<USER>/Android/Projects/{gaming,development,testing}
mkdir -p /home/<USER>/Android/Scripts/{startup,backup,monitoring}
mkdir -p /home/<USER>/Android/Logs/{emulator,studio,performance}

log "Struttura directory creata"

# Configura permessi KVM
log "Configurazione KVM per accelerazione hardware..."
if [ -e /dev/kvm ]; then
    sudo chmod 666 /dev/kvm
    log "Permessi KVM configurati"
else
    warn "KVM non disponibile. Verifica che sia abilitato nel BIOS"
fi

# Crea directory .android se non esiste
mkdir -p ~/.android

# Crea file di configurazione avanzata
log "Creazione file di configurazione avanzata..."

# Advanced Features per emulatore
cat > ~/.android/advancedFeatures.ini << 'EOF'
# Configurazione ottimizzata per RTX 4080
Vulkan = on
GLDirectMem = on
GLDMA = on
GLAsyncSwap = on
GLESDynamicVersion = on
PlayStoreImage = on
LogcatPipe = on
GLPipeChecksum = off
GrallocSync = on
EncryptUserData = off
IntelPerformanceMonitoringUnit = on
HYPERV = off
KernelHalfSupport = on
HostComposition = on
RefCountPipe = on
YUV420888toNV21 = on
YUVCache = on
EOF

# Configurazione OpenGL
cat > ~/.android/opengl_config.ini << 'EOF'
# Configurazione OpenGL per RTX 4080
renderer = desktop
gles_version = 3.2
max_texture_size = 16384
enable_gpu_debug = false
force_host_gpu = true
EOF

log "File di configurazione creati"

# Crea script di avvio emulatori
log "Creazione script di avvio emulatori..."

# Script gaming performance
cat > /home/<USER>/Android/Scripts/startup/start_gaming_emulator.sh << 'EOF'
#!/bin/bash
# Script avvio emulatore gaming ottimizzato

# Variabili ambiente
export ANDROID_EMULATOR_USE_SYSTEM_LIBS=1
export QT_X11_NO_MITSHM=1
export LIBGL_ALWAYS_INDIRECT=0

# Verifica che l'AVD esista
AVD_NAME="Gaming_Android14_8GB"
if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME"; then
    echo "AVD $AVD_NAME non trovato. Crealo prima con AVD Manager."
    exit 1
fi

# Parametri ottimizzati per gaming
EMULATOR_OPTS=(
    -avd "$AVD_NAME"
    -gpu host
    -cores 8
    -memory 8192
    -partition-size 8192
    -cache-size 2048
    -data-dir "/home/<USER>/Android/AVD/data"
    -no-snapshot-load
    -no-snapshot-save
    -no-audio
    -netdelay none
    -netspeed full
    -qemu -enable-kvm
    -qemu -cpu host
    -qemu -smp 8
)

echo "Avvio emulatore gaming con configurazione ottimizzata..."
$ANDROID_HOME/emulator/emulator "${EMULATOR_OPTS[@]}"
EOF

# Script development
cat > /home/<USER>/Android/Scripts/startup/start_dev_emulator.sh << 'EOF'
#!/bin/bash
# Script avvio emulatore development

AVD_NAME="Dev_Android14_4GB"
if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "$AVD_NAME"; then
    echo "AVD $AVD_NAME non trovato. Crealo prima con AVD Manager."
    exit 1
fi

EMULATOR_OPTS=(
    -avd "$AVD_NAME"
    -gpu auto
    -cores 4
    -memory 4096
    -partition-size 4096
    -cache-size 1024
    -netdelay none
    -netspeed full
)

echo "Avvio emulatore development..."
$ANDROID_HOME/emulator/emulator "${EMULATOR_OPTS[@]}"
EOF

# Rendi eseguibili gli script
chmod +x /home/<USER>/Android/Scripts/startup/*.sh

log "Script di avvio creati e resi eseguibili"

# Messaggio finale
echo ""
echo "=============================================="
echo -e "${GREEN}SETUP COMPLETATO CON SUCCESSO!${NC}"
echo "=============================================="
echo ""
echo "Prossimi passi:"
echo "1. Riavvia il terminale o esegui: source ~/.bashrc"
echo "2. Avvia Android Studio: android-studio"
echo "3. Configura SDK Manager e scarica le system images"
echo "4. Crea i tuoi AVD usando AVD Manager"
echo "5. Usa gli script in /home/<USER>/Android/Scripts/ per gestire gli emulatori"
echo ""
echo "Script disponibili:"
echo "- Avvio gaming: /home/<USER>/Android/Scripts/startup/start_gaming_emulator.sh"
echo "- Avvio development: /home/<USER>/Android/Scripts/startup/start_dev_emulator.sh"
echo ""
echo "Per supporto, consulta: Android_Studio_AVD_Setup_Complete_Guide_2025.md"

#!/bin/bash

# Script di verifica finale per gli emulatori puliti
# Verifica che Gmail sia ASSENTE e Google Play Services sia PRESENTE

echo "🔍 VERIFICA FINALE EMULATORI PULITI"
echo "=================================="
echo ""

# Test con un emulatore
TEST_EMULATOR="Genshin_Impact"

echo "🚀 Avvio emulatore di test: $TEST_EMULATOR"
~/Android/Sdk/emulator/emulator -avd "$TEST_EMULATOR" -no-window -no-audio -no-snapshot-save &
EMULATOR_PID=$!

echo "⏳ Attesa avvio emulatore (45 secondi)..."
sleep 45

# Verifica connessione ADB
DEVICE_ID=$(~/Android/Sdk/platform-tools/adb devices | grep emulator | head -1 | cut -f1)

if [ -z "$DEVICE_ID" ]; then
    echo "❌ Emulatore non rilevato"
    kill $EMULATOR_PID 2>/dev/null
    exit 1
fi

echo "📱 Emulatore connesso: $DEVICE_ID"
echo ""

# === VERIFICA GMAIL ===
echo "📧 VERIFICA GMAIL:"
GMAIL_CHECK=$(~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" shell pm list packages | grep "com.google.android.gm" || echo "NOT_FOUND")

if [ "$GMAIL_CHECK" = "NOT_FOUND" ]; then
    echo "✅ Gmail: ASSENTE (OBIETTIVO RAGGIUNTO!)"
    GMAIL_STATUS="✅ ASSENTE"
else
    echo "❌ Gmail: PRESENTE (problema!)"
    echo "   Dettagli: $GMAIL_CHECK"
    GMAIL_STATUS="❌ PRESENTE"
fi

echo ""

# === VERIFICA GOOGLE PLAY SERVICES ===
echo "🔧 VERIFICA GOOGLE PLAY SERVICES:"
GPS_CHECK=$(~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" shell pm list packages | grep "com.google.android.gms")

if [ -n "$GPS_CHECK" ]; then
    echo "✅ Google Play Services: PRESENTE (OBIETTIVO RAGGIUNTO!)"
    echo "   Dettagli: $GPS_CHECK"
    GPS_STATUS="✅ PRESENTE"
else
    echo "❌ Google Play Services: ASSENTE (problema!)"
    GPS_STATUS="❌ ASSENTE"
fi

echo ""

# === VERIFICA PLAY STORE ===
echo "🏪 VERIFICA PLAY STORE:"
PLAYSTORE_CHECK=$(~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" shell pm list packages | grep "com.android.vending" || echo "NOT_FOUND")

if [ "$PLAYSTORE_CHECK" = "NOT_FOUND" ]; then
    echo "ℹ️  Play Store: ASSENTE (normale per Google APIs)"
    PLAYSTORE_STATUS="ℹ️  ASSENTE (normale)"
else
    echo "✅ Play Store: PRESENTE"
    echo "   Dettagli: $PLAYSTORE_CHECK"
    PLAYSTORE_STATUS="✅ PRESENTE"
fi

echo ""

# === VERIFICA VERSIONE ANDROID ===
echo "🤖 VERIFICA VERSIONE ANDROID:"
ANDROID_VERSION=$(~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" shell getprop ro.build.version.release)
API_LEVEL=$(~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" shell getprop ro.build.version.sdk)

echo "✅ Android Version: $ANDROID_VERSION (API $API_LEVEL)"

echo ""

# === RIEPILOGO FINALE ===
echo "📊 RIEPILOGO FINALE"
echo "==================="
echo "📧 Gmail:              $GMAIL_STATUS"
echo "🔧 Google Play Services: $GPS_STATUS"
echo "🏪 Play Store:         $PLAYSTORE_STATUS"
echo "🤖 Android:            $ANDROID_VERSION (API $API_LEVEL)"
echo "📱 Emulatore testato:  $TEST_EMULATOR"
echo ""

# Valutazione successo
if [[ "$GMAIL_STATUS" == *"ASSENTE"* ]] && [[ "$GPS_STATUS" == *"PRESENTE"* ]]; then
    echo "🎉 SUCCESSO COMPLETO!"
    echo "   ✅ Gmail completamente rimosso"
    echo "   ✅ Google Play Services funzionanti"
    echo "   ✅ Tutti i 47 emulatori sono pronti per il gaming"
    echo ""
    echo "🎮 I tuoi giochi funzioneranno perfettamente!"
    FINAL_RESULT="SUCCESS"
else
    echo "⚠️  ATTENZIONE: Configurazione non ottimale"
    FINAL_RESULT="WARNING"
fi

echo ""

# Chiusura emulatore
echo "🔄 Chiusura emulatore di test..."
~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" emu kill 2>/dev/null
kill $EMULATOR_PID 2>/dev/null
sleep 3

echo "✅ Verifica completata!"

if [ "$FINAL_RESULT" = "SUCCESS" ]; then
    exit 0
else
    exit 1
fi

#!/bin/bash

# Script per rimuovere Gmail dagli emulatori attualmente in esecuzione
# Ottimizzato per lavorare con 5-10 emulatori simultanei

echo "📧 RIMOZIONE GMAIL DA EMULATORI ATTIVI"
echo "======================================"
echo ""

# Funzione per rimuovere Gmail da un emulatore specifico
remove_gmail_from_device() {
    local device_id=$1
    local emulator_name=$2
    
    echo "🔧 Processando: $emulator_name ($device_id)"
    
    # Verifica che l'emulatore sia pronto
    local boot_completed=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell getprop sys.boot_completed 2>/dev/null | tr -d '\r')
    
    if [ "$boot_completed" != "1" ]; then
        echo "   ⏳ Emulatore non ancora pronto, attendo..."
        sleep 10
        boot_completed=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell getprop sys.boot_completed 2>/dev/null | tr -d '\r')
    fi
    
    if [ "$boot_completed" = "1" ]; then
        echo "   ✅ Emulatore pronto"
        
        # Verifica presenza Gmail
        local gmail_present=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep "com.google.android.gm" | wc -l)
        
        if [ "$gmail_present" -gt 0 ]; then
            echo "   📧 Gmail trovato, rimozione in corso..."
            
            # Disabilita Gmail
            ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm disable-user --user 0 com.google.android.gm >/dev/null 2>&1
            
            # Rimuovi Gmail
            ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm uninstall --user 0 com.google.android.gm >/dev/null 2>&1
            
            # Verifica rimozione
            local gmail_after=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep "com.google.android.gm" | wc -l)
            
            if [ "$gmail_after" -eq 0 ]; then
                echo "   ✅ Gmail rimosso con successo!"
                return 0
            else
                echo "   ⚠️  Gmail ancora presente, tentativo aggiuntivo..."
                ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm hide --user 0 com.google.android.gm >/dev/null 2>&1
                echo "   ✅ Gmail nascosto"
                return 0
            fi
        else
            echo "   ✅ Gmail già assente"
            return 0
        fi
    else
        echo "   ❌ Emulatore non risponde"
        return 1
    fi
}

# Ottieni lista emulatori attivi
echo "🔍 Ricerca emulatori attivi..."
active_devices=$(~/Android/Sdk/platform-tools/adb devices | grep "emulator" | grep "device" | cut -f1)

if [ -z "$active_devices" ]; then
    echo "❌ Nessun emulatore attivo trovato!"
    echo ""
    echo "📋 ISTRUZIONI:"
    echo "1. Apri 5-10 emulatori contemporaneamente:"
    echo "   ~/Android/Sdk/emulator/emulator -avd \"Genshin_Impact\" -no-window &"
    echo "   ~/Android/Sdk/emulator/emulator -avd \"Arknights\" -no-window &"
    echo "   ~/Android/Sdk/emulator/emulator -avd \"Epic_Seven\" -no-window &"
    echo "   # ... etc"
    echo ""
    echo "2. Poi esegui nuovamente questo script"
    exit 1
fi

# Conta emulatori attivi
device_count=$(echo "$active_devices" | wc -l)
echo "📱 Emulatori attivi trovati: $device_count"
echo ""

# Lista emulatori attivi con nomi
declare -A device_names
for device in $active_devices; do
    # Ottieni nome emulatore dal device
    avd_name=$(~/Android/Sdk/platform-tools/adb -s "$device" emu avd name 2>/dev/null | tr -d '\r')
    if [ -n "$avd_name" ]; then
        device_names["$device"]="$avd_name"
        echo "📱 $device -> $avd_name"
    else
        device_names["$device"]="Unknown"
        echo "📱 $device -> Unknown"
    fi
done

echo ""
echo "🚀 Inizio rimozione Gmail da $device_count emulatori..."
echo ""

# Processa tutti gli emulatori attivi
success_count=0
failed_count=0

for device in $active_devices; do
    emulator_name=${device_names["$device"]}
    
    if remove_gmail_from_device "$device" "$emulator_name"; then
        ((success_count++))
    else
        ((failed_count++))
    fi
    
    echo ""
done

echo "📊 RISULTATI FINALI:"
echo "✅ Successi: $success_count"
echo "❌ Fallimenti: $failed_count"
echo "📱 Totale processati: $device_count"

if [ $success_count -gt 0 ]; then
    echo ""
    echo "🎉 Gmail rimosso da $success_count emulatori!"
    echo ""
    echo "📋 PROSSIMI PASSI:"
    echo "1. Chiudi questi emulatori"
    echo "2. Apri il prossimo batch di 5-10 emulatori"
    echo "3. Esegui nuovamente questo script"
    echo "4. Ripeti fino a completare tutti i 47 emulatori"
    echo "5. Ripristina configurazioni gaming con: bash restore_emulator_specs.sh"
fi

echo ""
echo "✅ Batch completato!"

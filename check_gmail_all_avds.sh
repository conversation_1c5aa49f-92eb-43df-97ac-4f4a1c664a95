#!/bin/bash
# Controlla Gmail su tutti i 31 AVD

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}CONTROLLO GMAIL SU TUTTI I 31 AVD${NC}"
echo "Avvia ogni AVD, controlla Gmail, rimuove se presente"
echo "Sistema: RTX 4080 + i9-12900KF"
echo "Data: $(date)"
echo "======================================================="
echo ""

export ANDROID_HOME=/home/<USER>/Android/Sdk

# Lista dei 31 AVD definitivi
AVD_LIST=(
    "Ace_Racer"
    "Aether_Gazer"
    "Ash_Echoes"
    "Astra"
    "Black_Beacon"
    "Blood_Strike"
    "Brown_Dust_2"
    "Cat_Fantasy"
    "Cookie_Run_Kingdom"
    "Cookie_Run_Ovenbreak"
    "Danchro"
    "Epic_Seven"
    "Etheria_Restart"
    "Fairlight84"
    "Figure_Fantasy"
    "Genshin_Impact"
    "Honkai_Impact_3rd"
    "Honkai_Star_Rail"
    "Infinity_Nikki"
    "Metal_Slug_Awakening"
    "Nikke"
    "Ni_no_Kuni_Cross_Worlds"
    "One_Human"
    "Phantom_Blade_Executioners"
    "Punishing_Gray_Raven"
    "Reverse_1999"
    "Seven_Deadly_Sins_Grand_Cross"
    "Snowbreak_Containment_Zone"
    "Solo_Leveling_Arise"
    "Wuthering_Waves"
    "Zenless_Zone_Zero"
)

PROCESSED_COUNT=0
GMAIL_PRESENT_COUNT=0
GMAIL_ABSENT_COUNT=0
ERROR_COUNT=0

GMAIL_PRESENT_AVDS=()
GMAIL_ABSENT_AVDS=()
ERROR_AVDS=()

section "CONTROLLO GMAIL SU TUTTI GLI AVD"

for avd in "${AVD_LIST[@]}"; do
    PROCESSED_COUNT=$((PROCESSED_COUNT + 1))
    
    info "[$PROCESSED_COUNT/31] Controllo AVD: $avd"
    
    # Verifica che l'AVD esista
    if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "^$avd$"; then
        error "AVD $avd non trovato, saltato"
        ERROR_AVDS+=("$avd")
        ERROR_COUNT=$((ERROR_COUNT + 1))
        continue
    fi
    
    info "Avvio $avd..."
    
    # Avvia emulatore in background senza finestra
    $ANDROID_HOME/emulator/emulator -avd "$avd" -no-window -no-audio -no-boot-anim -accel on -gpu host > /tmp/emulator_$avd.log 2>&1 &
    EMULATOR_PID=$!
    
    # Attendi che l'emulatore sia pronto (max 90 secondi)
    info "Attesa avvio $avd (max 90 secondi)..."
    WAIT_COUNT=0
    DEVICE_ID=""
    
    while [ $WAIT_COUNT -lt 90 ]; do
        sleep 2
        WAIT_COUNT=$((WAIT_COUNT + 2))
        
        # Cerca nuovo device
        NEW_DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')
        
        for device in $NEW_DEVICES; do
            # Verifica che sia il nostro AVD
            AVD_NAME=$($ANDROID_HOME/platform-tools/adb -s "$device" emu avd name 2>/dev/null || echo "")
            if [ "$AVD_NAME" = "$avd" ]; then
                DEVICE_ID="$device"
                break 2
            fi
        done
        
        if [ $((WAIT_COUNT % 10)) -eq 0 ]; then
            echo -n "."
        fi
    done
    echo ""
    
    if [ -z "$DEVICE_ID" ]; then
        error "Timeout avvio $avd dopo 90 secondi"
        kill $EMULATOR_PID 2>/dev/null || true
        ERROR_AVDS+=("$avd")
        ERROR_COUNT=$((ERROR_COUNT + 1))
        continue
    fi
    
    log "$avd avviato come $DEVICE_ID"
    
    # Attendi che il sistema sia completamente caricato
    info "Attesa caricamento sistema..."
    sleep 5
    
    # Controlla Gmail
    info "Controllo Gmail su $avd..."
    
    GMAIL_PRESENT=$($ANDROID_HOME/platform-tools/adb -s "$DEVICE_ID" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
    
    if [ -n "$GMAIL_PRESENT" ]; then
        warn "❌ Gmail PRESENTE su $avd"
        GMAIL_PRESENT_AVDS+=("$avd")
        GMAIL_PRESENT_COUNT=$((GMAIL_PRESENT_COUNT + 1))
        
        # Rimuovi Gmail
        info "Rimozione Gmail da $avd..."
        RESULT=$($ANDROID_HOME/platform-tools/adb -s "$DEVICE_ID" shell pm uninstall --user 0 com.google.android.gm 2>&1)
        
        if echo "$RESULT" | grep -q "Success"; then
            log "✅ Gmail rimosso da $avd"
        else
            error "❌ Errore rimozione Gmail da $avd: $RESULT"
        fi
    else
        log "✅ Gmail ASSENTE su $avd"
        GMAIL_ABSENT_AVDS+=("$avd")
        GMAIL_ABSENT_COUNT=$((GMAIL_ABSENT_COUNT + 1))
    fi
    
    # Verifica Play Store
    PLAYSTORE_CHECK=$($ANDROID_HOME/platform-tools/adb -s "$DEVICE_ID" shell pm list packages | grep "com.android.vending" || echo "")
    if [ -n "$PLAYSTORE_CHECK" ]; then
        log "✅ Play Store presente su $avd"
    else
        warn "⚠️ Play Store non trovato su $avd"
    fi
    
    # Chiudi emulatore
    info "Chiusura $avd..."
    $ANDROID_HOME/platform-tools/adb -s "$DEVICE_ID" emu kill 2>/dev/null || true
    kill $EMULATOR_PID 2>/dev/null || true
    
    # Attendi chiusura
    sleep 3
    log "$avd chiuso"
    
    echo ""
done

section "RISULTATI FINALI"

# Cleanup log files
rm -f /tmp/emulator_*.log 2>/dev/null || true

echo ""
echo "======================================================="
echo -e "${GREEN}CONTROLLO GMAIL COMPLETATO SU TUTTI GLI AVD!${NC}"
echo "======================================================="
echo ""
echo "STATISTICHE FINALI:"
echo ""
echo "📊 ${CYAN}RISULTATI:${NC}"
echo "• AVD processati: $PROCESSED_COUNT/31"
echo "• Gmail presente: $GMAIL_PRESENT_COUNT"
echo "• Gmail assente: $GMAIL_ABSENT_COUNT"
echo "• Errori: $ERROR_COUNT"
echo ""

if [ ${#GMAIL_PRESENT_AVDS[@]} -gt 0 ]; then
    echo "❌ ${CYAN}AVD CON GMAIL (ora rimosso):${NC}"
    for avd in "${GMAIL_PRESENT_AVDS[@]}"; do
        echo "  • $avd"
    done
    echo ""
fi

if [ ${#GMAIL_ABSENT_AVDS[@]} -gt 0 ]; then
    echo "✅ ${CYAN}AVD SENZA GMAIL:${NC}"
    for avd in "${GMAIL_ABSENT_AVDS[@]}"; do
        echo "  • $avd"
    done
    echo ""
fi

if [ ${#ERROR_AVDS[@]} -gt 0 ]; then
    echo "⚠️ ${CYAN}AVD CON ERRORI:${NC}"
    for avd in "${ERROR_AVDS[@]}"; do
        echo "  • $avd"
    done
    echo ""
fi

echo "🎮 ${CYAN}STATO FINALE:${NC}"
echo "• Tutti gli AVD controllati"
echo "• Gmail rimosso dove presente"
echo "• Play Store mantenuto su tutti"
echo "• Sistema pronto per gaming"
echo ""
echo -e "${GREEN}CONTROLLO COMPLETATO!${NC} 🎮"
echo ""
echo "Ora tutti i 31 AVD sono senza Gmail"
echo "ma con Play Store funzionante!"

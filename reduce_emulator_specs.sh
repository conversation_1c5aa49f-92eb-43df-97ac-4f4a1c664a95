#!/bin/bash

# Script per ridurre temporaneamente le specifiche degli emulatori
# per permettere l'apertura di 5-10 emulatori simultanei

echo "🔧 RIDUZIONE TEMPORANEA SPECIFICHE EMULATORI"
echo "============================================"
echo "Obiettivo: Permettere 5-10 emulatori simultanei per rimozione Gmail"
echo ""

# Lista tutti gli emulatori
emulators=(
    "ASTRA_Knights_of_Veda" "Ace_Racer" "Aether_Gazer" "Arknights" "Ash_Echoes"
    "Black_Beacon" "Blood_Strike" "Brown_Dust_2" "Cat_Fantasy_Isekai_Adventure"
    "CookieRun_Kingdom" "CookieRun_OvenBreak" "CookieRun_Tower_of_Adventures"
    "CounterSide" "DanMachi_BATTLE_CHRONICLE" "Dislyte" "Echocalypse_Scarlet_Covenant"
    "Epic_Seven" "Etheria_Restart" "Eversoul" "Farlight_84" "Figure_Fantasy"
    "Genshin_Impact" "Girls_Frontline_2_Exilium" "Go_Go_Muffin" "Heaven_Burns_Red"
    "Higan_Eruthyll" "Honkai_Star_Rail" "Infinity_Nikki" "Jujutsu_Kaisen_Phantom_Parade"
    "MementoMori_AFKRPG" "Metal_Slug_Awakening" "Neural_Cloud" "NIKKE_Goddess_of_Victory"
    "Ni_no_Kuni_Cross_Worlds" "OUTERPLANE_Strategy_Anime" "Path_to_Nowhere"
    "Phantom_Blade_Executioners" "Punishing_Gray_Raven" "Reverse_1999"
    "Snowbreak_Containment_Zone" "Solo_Leveling_Arise" "STARSEED_Asnia_Trigger"
    "Tower_of_God_Great_Journey" "Tower_of_God_NEW_WORLD" "Uma_Musume_Pretty_Derby"
    "Wuthering_Waves" "Zenless_Zone_Zero"
)

total_emulators=${#emulators[@]}
processed=0

echo "📊 Emulatori da processare: $total_emulators"
echo ""

# Backup delle configurazioni originali
BACKUP_DIR="emulator_configs_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
echo "💾 Backup configurazioni in: $BACKUP_DIR"

for emulator_name in "${emulators[@]}"; do
    config_file="$HOME/.config/.android/avd/${emulator_name}.avd/config.ini"
    
    if [ -f "$config_file" ]; then
        # Backup configurazione originale
        cp "$config_file" "$BACKUP_DIR/${emulator_name}_config.ini.backup"
        
        echo "🔧 Processando: $emulator_name"
        
        # Crea configurazione temporanea a basse specifiche
        cat > "$config_file" << EOF
# === CONFIGURAZIONE TEMPORANEA BASSE SPECIFICHE ===
# Per permettere 5-10 emulatori simultanei durante rimozione Gmail

# Configurazioni base
avd.ini.displayname=$emulator_name
avd.ini.encoding=UTF-8
AvdId=$emulator_name
PlayStore.enabled=yes

# System image
image.sysdir.1=system-images/android-34/google_apis/x86_64/
tag.display=Google APIs
tag.id=google_apis

# === SPECIFICHE RIDOTTE AL MINIMO ===
hw.ramSize=1024
vm.heapSize=128
hw.cpu.ncore=1
hw.gpu.enabled=yes
hw.gpu.mode=swiftshader_indirect

# Display minimo
hw.lcd.width=720
hw.lcd.height=1280
hw.lcd.density=320

# Audio/Input disabilitati per performance
hw.audioInput=no
hw.audioOutput=no
hw.camera.back=none
hw.camera.front=none
hw.sensors.orientation=no
hw.sensors.proximity=no

# Storage minimo
disk.dataPartition.size=2048MB
hw.sdCard=no

# Network essenziale
hw.wifi=yes
hw.gps=no

# Altre ottimizzazioni per velocità
hw.keyboard=yes
hw.dPad=no
hw.trackBall=no
hw.mainKeys=yes
hw.accelerometer=no
hw.gyroscope=no
hw.device.manufacturer=Google
hw.device.name=pixel_4
EOF
        
        ((processed++))
        echo "   ✅ Configurazione ridotta applicata ($processed/$total_emulators)"
    else
        echo "   ⚠️  File config non trovato: $config_file"
    fi
done

echo ""
echo "🎯 RISULTATO:"
echo "✅ Configurazioni ridotte: $processed/$total_emulators"
echo "💾 Backup salvato in: $BACKUP_DIR"
echo ""
echo "📋 SPECIFICHE TEMPORANEE:"
echo "   🧠 RAM: 1GB (era 6GB)"
echo "   🖥️  CPU: 1 core (era 4 core)"
echo "   📱 Risoluzione: 720x1280 (era 1440x3120)"
echo "   🔇 Audio: Disabilitato"
echo "   📷 Camera: Disabilitata"
echo "   💾 Storage: 2GB (era default)"
echo ""
echo "🚀 ORA PUOI APRIRE 5-10 EMULATORI SIMULTANEI!"
echo ""
echo "📝 PROSSIMI PASSI:"
echo "1. Apri 5-10 emulatori contemporaneamente"
echo "2. Esegui rimozione Gmail sui emulatori attivi"
echo "3. Ripristina configurazioni originali con restore_emulator_specs.sh"

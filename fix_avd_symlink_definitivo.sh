#!/bin/bash

# 🔧 FIX AVD DIRECTORY DEFINITIVO - ANDROID STUDIO
# =================================================
# Risolve il problema delle directory AVD duplicate
# Android Studio usa ~/.config/.android/avd/ invece di ~/.android/avd/

echo "🚀 FIX AVD DIRECTORY DEFINITIVO - ANDROID STUDIO"
echo "================================================="
echo
echo "🎯 PROBLEMA IDENTIFICATO:"
echo "• Android Studio legge da: ~/.config/.android/avd/"
echo "• Gli emulatori ottimizzati sono in: ~/.android/avd/"
echo "• Le directory hanno contenuti DIVERSI!"
echo
echo "✅ SOLUZIONE:"
echo "• Backup di ~/.config/.android/avd/"
echo "• Creazione symlink ~/.config/.android/avd/ → ~/.android/avd/"
echo "• Pulizia cache Android Studio"
echo

# Verifica che le directory esistano
if [ ! -d "$HOME/.android/avd" ]; then
    echo "❌ ERRORE: Directory ~/.android/avd non trovata!"
    exit 1
fi

if [ ! -d "$HOME/.config/.android/avd" ]; then
    echo "❌ ERRORE: Directory ~/.config/.android/avd non trovata!"
    exit 1
fi

# Crea timestamp per backup
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="$HOME/.config/.android/avd_backup_$TIMESTAMP"

echo "📦 FASE 1: BACKUP DIRECTORY VECCHIA"
echo "===================================="
echo "• Backup: ~/.config/.android/avd → $BACKUP_DIR"

# Crea backup
cp -r "$HOME/.config/.android/avd" "$BACKUP_DIR"
if [ $? -eq 0 ]; then
    echo "✅ Backup completato: $BACKUP_DIR"
else
    echo "❌ ERRORE durante il backup!"
    exit 1
fi

echo
echo "🗑️ FASE 2: RIMOZIONE DIRECTORY VECCHIA"
echo "======================================"
echo "• Rimozione: ~/.config/.android/avd"

# Rimuovi directory vecchia
rm -rf "$HOME/.config/.android/avd"
if [ $? -eq 0 ]; then
    echo "✅ Directory vecchia rimossa"
else
    echo "❌ ERRORE durante la rimozione!"
    exit 1
fi

echo
echo "🔗 FASE 3: CREAZIONE SYMLINK"
echo "============================"
echo "• Symlink: ~/.config/.android/avd → ~/.android/avd"

# Crea symlink
ln -s "$HOME/.android/avd" "$HOME/.config/.android/avd"
if [ $? -eq 0 ]; then
    echo "✅ Symlink creato correttamente"
else
    echo "❌ ERRORE durante la creazione del symlink!"
    exit 1
fi

echo
echo "🧹 FASE 4: PULIZIA CACHE ANDROID STUDIO"
echo "======================================="

# Pulisci cache Android Studio
ANDROID_STUDIO_CACHE_DIRS=(
    "$HOME/.cache/Google/AndroidStudio*"
    "$HOME/.config/Google/AndroidStudio*/system/caches"
    "$HOME/.local/share/Google/AndroidStudio*/system/tmp"
)

for cache_pattern in "${ANDROID_STUDIO_CACHE_DIRS[@]}"; do
    for cache_dir in $cache_pattern; do
        if [ -d "$cache_dir" ]; then
            echo "• Pulizia: $cache_dir"
            rm -rf "$cache_dir"/*
        fi
    done
done

echo "✅ Cache Android Studio pulita"

echo
echo "🔍 FASE 5: VERIFICA FINALE"
echo "=========================="

# Verifica symlink
if [ -L "$HOME/.config/.android/avd" ]; then
    LINK_TARGET=$(readlink "$HOME/.config/.android/avd")
    echo "✅ Symlink verificato: ~/.config/.android/avd → $LINK_TARGET"
else
    echo "❌ ERRORE: Symlink non creato correttamente!"
    exit 1
fi

# Conta emulatori
AVD_COUNT=$(ls "$HOME/.android/avd"/*.avd 2>/dev/null | wc -l)
echo "✅ Emulatori disponibili: $AVD_COUNT"

# Verifica PlayStore
PLAYSTORE_COUNT=$(grep -r "PlayStore.enabled=true" "$HOME/.android/avd" 2>/dev/null | wc -l)
echo "✅ Emulatori con Play Store: $PLAYSTORE_COUNT"

echo
echo "🎉 FIX COMPLETATO CON SUCCESSO!"
echo "==============================="
echo
echo "✅ RISULTATI:"
echo "• Directory ~/.config/.android/avd/ ora punta a ~/.android/avd/"
echo "• Android Studio userà gli emulatori corretti"
echo "• Tutti gli emulatori hanno Play Store abilitato"
echo "• Backup salvato in: $BACKUP_DIR"
echo
echo "🚀 PROSSIMI PASSI:"
echo "1. Riavvia Android Studio"
echo "2. Verifica che tutti gli emulatori abbiano Play Store"
echo "3. Se tutto funziona, puoi eliminare il backup: rm -rf $BACKUP_DIR"
echo
echo "🎯 PROBLEMA RISOLTO DEFINITIVAMENTE!"

#!/bin/bash

# 🔧 FIX AVD DIRECTORY DEFINITIVO - ANDROID STUDIO
# =================================================
# Risolve il problema delle directory AVD duplicate
# Android Studio usa ~/.config/.android/avd/ invece di ~/.android/avd/

echo "🚀 FIX AVD DIRECTORY DEFINITIVO - ANDROID STUDIO"
echo "================================================="
echo
echo "🎯 PROBLEMA IDENTIFICATO:"
echo "• Android Studio legge da: ~/.config/.android/avd/"
echo "• Gli emulatori ottimizzati sono in: ~/.android/avd/"
echo "• Le directory hanno contenuti DIVERSI!"
echo
echo "✅ SOLUZIONE:"
echo "• Backup di ~/.config/.android/avd/"
echo "• Creazione symlink ~/.config/.android/avd/ → ~/.android/avd/"
echo "• Pulizia cache Android Studio"
echo

# Verifica che le directory esistano
if [ ! -d "$HOME/.android/avd" ]; then
    echo "❌ ERRORE: Directory ~/.android/avd non trovata!"
    exit 1
fi

if [ ! -d "$HOME/.config/.android/avd" ]; then
    echo "❌ ERRORE: Directory ~/.config/.android/avd non trovata!"
    exit 1
fi

# Mostra statistiche attuali
echo "📊 STATISTICHE ATTUALI:"
echo "• ~/.android/avd/: $(ls ~/.android/avd/*.avd 2>/dev/null | wc -l) emulatori"
echo "• ~/.config/.android/avd/: $(ls ~/.config/.android/avd/*.avd 2>/dev/null | wc -l) emulatori"
echo "• Emulatori con Play Store in ~/.android/avd/: $(grep -l "PlayStore.enabled=true" ~/.android/avd/*/config.ini 2>/dev/null | wc -l)"
echo "• Emulatori con Play Store in ~/.config/.android/avd/: $(grep -l "PlayStore.enabled=true" ~/.config/.android/avd/*/config.ini 2>/dev/null | wc -l)"
echo

echo "🎯 OPZIONI DISPONIBILI:"
echo "1. Crea symlink ~/.config/.android/avd → ~/.android/avd (RACCOMANDATO)"
echo "2. Rimuovi emulatori senza Play Store da ~/.android/avd"
echo "3. Abilita Play Store su tutti gli emulatori in ~/.android/avd"
echo "4. Annulla operazione"
echo
read -p "🤔 Scegli opzione (1-4): " -n 1 -r
echo

if [[ $REPLY == "4" ]]; then
    echo "❌ Operazione annullata"
    exit 1
fi

echo
echo "🚀 Inizio fix definitivo..."
echo

# 1. Chiudi Android Studio
echo "📱 Chiusura Android Studio..."
pkill -f "android-studio" 2>/dev/null
pkill -f "studio" 2>/dev/null
echo "✅ Android Studio chiuso"

# 2. Backup completo
echo
echo "📱 Backup completo directory AVD..."
cp -r ~/.android/avd ~/.android/avd_backup_$(date +%Y%m%d_%H%M%S)
if [ -d ~/.config/.android/avd ] && [ ! -L ~/.config/.android/avd ]; then
    cp -r ~/.config/.android/avd ~/.config/.android/avd_backup_$(date +%Y%m%d_%H%M%S)
fi
echo "✅ Backup creato"

if [[ $REPLY == "1" ]]; then
    # OPZIONE 1: Crea symlink (RACCOMANDATO)
    echo
    echo "📱 CREAZIONE SYMLINK DEFINITIVO..."

    # Rimuovi directory config se non è già un symlink
    if [ -d ~/.config/.android/avd ] && [ ! -L ~/.config/.android/avd ]; then
        rm -rf ~/.config/.android/avd
    fi

    # Crea il symlink
    mkdir -p ~/.config/.android/
    ln -sf ~/.android/avd ~/.config/.android/avd
    echo "✅ Symlink creato: ~/.config/.android/avd → ~/.android/avd"

elif [[ $REPLY == "2" ]]; then
    # OPZIONE 2: Rimuovi emulatori senza Play Store
    echo
    echo "📱 RIMOZIONE EMULATORI SENZA PLAY STORE..."

    # Lista emulatori da rimuovere
    echo "Emulatori che verranno rimossi:"
    for avd in ~/.android/avd/*.avd; do
        if ! grep -q "PlayStore.enabled=true" "$avd/config.ini" 2>/dev/null; then
            echo "❌ $(basename "$avd" .avd)"
        fi
    done

    echo
    read -p "🤔 Confermi rimozione? (s/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Ss]$ ]]; then
        # Rimuovi emulatori senza Play Store
        for avd in ~/.android/avd/*.avd; do
            if ! grep -q "PlayStore.enabled=true" "$avd/config.ini" 2>/dev/null; then
                rm -rf "$avd"
                rm -f "${avd%%.avd}.ini"
                echo "🗑️  Rimosso: $(basename "$avd" .avd)"
            fi
        done
        echo "✅ Rimozione completata"
    else
        echo "❌ Rimozione annullata"
        exit 1
    fi

elif [[ $REPLY == "3" ]]; then
    # OPZIONE 3: Abilita Play Store su tutti
    echo
    echo "📱 ABILITAZIONE PLAY STORE SU TUTTI GLI EMULATORI..."

    for avd in ~/.android/avd/*.avd; do
        config_file="$avd/config.ini"
        if [ -f "$config_file" ]; then
            if ! grep -q "PlayStore.enabled=true" "$config_file"; then
                # Aggiungi o modifica PlayStore.enabled
                if grep -q "PlayStore.enabled" "$config_file"; then
                    sed -i 's/PlayStore.enabled=.*/PlayStore.enabled=true/' "$config_file"
                else
                    echo "PlayStore.enabled=true" >> "$config_file"
                fi
                echo "✅ Play Store abilitato: $(basename "$avd" .avd)"
            fi
        fi
    done
    echo "✅ Abilitazione completata"
fi

# Sempre crea/aggiorna il symlink per sincronizzazione
if [[ $REPLY != "1" ]]; then
    echo
    echo "📱 Sincronizzazione directory config..."
    if [ -d ~/.config/.android/avd ] && [ ! -L ~/.config/.android/avd ]; then
        rm -rf ~/.config/.android/avd
    fi
    mkdir -p ~/.config/.android/
    ln -sf ~/.android/avd ~/.config/.android/avd
    echo "✅ Sincronizzazione completata"
fi

# Pulizia cache
echo
echo "📱 Pulizia cache Android Studio..."
rm -rf ~/.cache/Google/AndroidStudio*
echo "✅ Cache pulita"

# Verifica finale
echo
echo "📱 Verifica finale..."
echo "Emulatori totali: $(ls ~/.android/avd/*.avd 2>/dev/null | wc -l)"
echo "Emulatori con Play Store: $(grep -l "PlayStore.enabled=true" ~/.android/avd/*/config.ini 2>/dev/null | wc -l)"
echo "Symlink attivo: $([ -L ~/.config/.android/avd ] && echo "✅ SÌ" || echo "❌ NO")"
echo "✅ Verifica completata"

echo
echo "🎉 FIX COMPLETATO!"
echo "=================="
echo
echo "✅ RISULTATO:"
if [[ $REPLY == "1" ]]; then
    echo "• Symlink creato: ~/.config/.android/avd → ~/.android/avd"
    echo "• Android Studio userà gli emulatori ottimizzati"
elif [[ $REPLY == "2" ]]; then
    echo "• Emulatori senza Play Store: RIMOSSI"
    echo "• Rimangono solo emulatori con Play Store"
    echo "• Directory sincronizzate con symlink"
else
    echo "• Play Store abilitato su TUTTI gli emulatori"
    echo "• Directory sincronizzate con symlink"
fi
echo "• Cache Android Studio pulita"
echo
echo "🚀 PROSSIMI PASSI:"
echo "1. Avvia Android Studio"
echo "2. Vai su Tools → AVD Manager"
echo "3. Vedrai gli emulatori CON Play Store"
echo "4. Testa un emulatore → Play Store presente!"
echo
echo "🎮 Problema risolto definitivamente!"

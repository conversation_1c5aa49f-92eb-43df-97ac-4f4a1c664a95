#!/bin/bash
# Pulizia finale - mantiene solo i 31 AVD definitivi con nomi puliti

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}PULIZIA FINALE AVD - SOLO 31 DEFINITIVI${NC}"
echo "Rimuove duplicati e mantiene solo AVD con nomi puliti"
echo "Data: $(date)"
echo "======================================================="
echo ""

export ANDROID_HOME=/home/<USER>/Android/Sdk

section "RIMOZIONE AVD DUPLICATI E OBSOLETI"

# Lista AVD da rimuovere (quelli con suffissi)
AVDS_TO_REMOVE=(
    "Ace_Racer_PlayStore_Test"
    "Aether_Gazer_PlayStore"
    "Ash_Echoes_PlayStore"
    "Astra_PlayStore"
    "Black_Beacon_PlayStore"
    "Blood_Strike_PlayStore_Test"
    "Brown_Dust_2_PlayStore"
    "Cat_Fantasy_PlayStore"
    "Cookie_Run_Ovenbreak_PlayStore"
    "Cookie_Run_PlayStore_Test"
    "Danchro_PlayStore"
    "Epic_Seven_PlayStore_Test"
    "Etheria_Restart_PlayStore"
    "Fairlight84_PlayStore"
    "Figure_Fantasy_PlayStore"
    "Genshin_Impact_PlayStore_Test"
    "Honkai_Impact_3rd_PlayStore"
    "Honkai_Star_Rail_PlayStore"
    "Infinity_Nikki_PlayStore"
    "Metal_Slug_Awakening_PlayStore"
    "Nikke_PlayStore"
    "Ni_no_Kuni_Cross_Worlds_PlayStore"
    "One_Human_PlayStore"
    "Phantom_Blade_Executioners_PlayStore"
    "Punishing_Gray_Raven_PlayStore"
    "Reverse_1999_PlayStore"
    "Seven_Deadly_Sins_Grand_Cross_PlayStore"
    "Snowbreak_Containment_Zone_PlayStore"
    "Solo_Leveling_Arise_PlayStore"
    "Wuthering_Waves_PlayStore"
    "Zenless_Zone_Zero_PlayStore"
)

info "Rimozione AVD duplicati e obsoleti..."

for avd in "${AVDS_TO_REMOVE[@]}"; do
    if $ANDROID_HOME/emulator/emulator -list-avds | grep -q "^$avd$"; then
        info "Rimozione: $avd"
        $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager delete avd -n "$avd" 2>/dev/null || true
        log "$avd rimosso"
    fi
done

section "VERIFICA AVD DEFINITIVI"

# Lista AVD definitivi che devono rimanere
FINAL_AVDS=(
    "Ace_Racer"
    "Aether_Gazer"
    "Ash_Echoes"
    "Astra"
    "Black_Beacon"
    "Blood_Strike"
    "Brown_Dust_2"
    "Cat_Fantasy"
    "Cookie_Run_Kingdom"
    "Cookie_Run_Ovenbreak"
    "Danchro"
    "Epic_Seven"
    "Etheria_Restart"
    "Fairlight84"
    "Figure_Fantasy"
    "Genshin_Impact"
    "Honkai_Impact_3rd"
    "Honkai_Star_Rail"
    "Infinity_Nikki"
    "Metal_Slug_Awakening"
    "Nikke"
    "Ni_no_Kuni_Cross_Worlds"
    "One_Human"
    "Phantom_Blade_Executioners"
    "Punishing_Gray_Raven"
    "Reverse_1999"
    "Seven_Deadly_Sins_Grand_Cross"
    "Snowbreak_Containment_Zone"
    "Solo_Leveling_Arise"
    "Wuthering_Waves"
    "Zenless_Zone_Zero"
)

info "Verifica presenza AVD definitivi..."

MISSING_AVDS=()
for avd in "${FINAL_AVDS[@]}"; do
    if $ANDROID_HOME/emulator/emulator -list-avds | grep -q "^$avd$"; then
        log "✓ $avd presente"
    else
        MISSING_AVDS+=("$avd")
        echo -e "${RED}[✗]${NC} $avd mancante"
    fi
done

# Conta AVD totali
TOTAL_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds | wc -l)
log "Totale AVD rimanenti: $TOTAL_AVDS"

# Se ci sono AVD mancanti, li creo
if [ ${#MISSING_AVDS[@]} -gt 0 ]; then
    section "CREAZIONE AVD MANCANTI"
    
    for avd in "${MISSING_AVDS[@]}"; do
        info "Creazione AVD mancante: $avd"
        
        # Determina configurazione secondo il gioco
        case "$avd" in
            "Genshin_Impact"|"Honkai_Star_Rail"|"Honkai_Impact_3rd"|"Zenless_Zone_Zero"|"Wuthering_Waves")
                # Gaming ultra high-end
                RAM="8192"
                CORES="8"
                STORAGE="48"
                GPU="host"
                DEVICE="pixel_7_pro"
                ;;
            "Aether_Gazer"|"Ash_Echoes"|"Infinity_Nikki"|"Nikke"|"Epic_Seven"|"Solo_Leveling_Arise"|"Punishing_Gray_Raven"|"Blood_Strike"|"Phantom_Blade_Executioners"|"Snowbreak_Containment_Zone"|"Ace_Racer")
                # Gaming high-end
                RAM="8192"
                CORES="8"
                STORAGE="32"
                GPU="host"
                DEVICE="pixel_7_pro"
                ;;
            "Brown_Dust_2"|"Danchro"|"Ni_no_Kuni_Cross_Worlds"|"Etheria_Restart"|"Figure_Fantasy"|"Reverse_1999"|"Seven_Deadly_Sins_Grand_Cross"|"Astra"|"Metal_Slug_Awakening"|"Black_Beacon"|"Fairlight84")
                # Gaming standard
                RAM="6144"
                CORES="6"
                STORAGE="24"
                GPU="host"
                DEVICE="pixel_7"
                ;;
            "Cookie_Run_Kingdom"|"Cookie_Run_Ovenbreak"|"Cat_Fantasy"|"One_Human")
                # Casual
                RAM="4096"
                CORES="4"
                STORAGE="16"
                GPU="auto"
                DEVICE="pixel_6"
                ;;
        esac
        
        # Crea AVD
        echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
            -n "$avd" \
            -k "system-images;android-34;google_apis_playstore;x86_64" \
            -d "$DEVICE" \
            --force
        
        # Configura
        if [ -d ~/.android/avd/$avd.avd ]; then
            cat >> ~/.android/avd/$avd.avd/config.ini << EOF

# === PLAY STORE GAMING OPTIMIZED FINAL ===
PlayStore.enabled=true
tag.id=google_apis_playstore
hw.ramSize=$RAM
hw.cpu.ncore=$CORES
vm.heapSize=$((RAM/16))
hw.gpu.enabled=yes
hw.gpu.mode=$GPU
hw.keyboard=yes
disk.dataPartition.size=${STORAGE}G
fastboot.forceColdBoot=yes
EOF
            log "$avd creato ($RAM MB RAM, $CORES cores)"
        fi
    done
fi

section "VERIFICA FINALE"

# Conta finale
FINAL_COUNT=$($ANDROID_HOME/emulator/emulator -list-avds | wc -l)
log "Totale AVD definitivi: $FINAL_COUNT"

# Lista finale ordinata
info "Lista finale AVD definitivi:"
$ANDROID_HOME/emulator/emulator -list-avds | sort | while read avd; do
    if [ -n "$avd" ]; then
        echo "  ✓ $avd"
    fi
done

# Spazio utilizzato
TOTAL_SIZE=$(du -sh ~/.android/avd 2>/dev/null | cut -f1)
info "Spazio utilizzato: $TOTAL_SIZE"

echo ""
echo "======================================================="
echo -e "${GREEN}PULIZIA COMPLETATA - 31 AVD DEFINITIVI!${NC}"
echo "======================================================="
echo ""
echo "RISULTATO FINALE:"
echo ""
echo "✅ ${CYAN}31 AVD DEFINITIVI CON NOMI PULITI:${NC}"
echo "• Ace_Racer (per Ace Racer)"
echo "• Genshin_Impact (per Genshin Impact)"
echo "• Blood_Strike (per Blood Strike)"
echo "• Epic_Seven (per Epic Seven)"
echo "• Cookie_Run_Kingdom (per Cookie Run Kingdom)"
echo "• Honkai_Star_Rail (per Honkai Star Rail)"
echo "• Wuthering_Waves (per Wuthering Waves)"
echo "• E tutti gli altri 24 giochi!"
echo ""
echo "🗑️ ${CYAN}RIMOSSI:${NC}"
echo "• Tutti gli AVD con suffissi '_PlayStore'"
echo "• Tutti gli AVD con suffissi '_Test'"
echo "• Duplicati e configurazioni obsolete"
echo ""
echo "✅ ${CYAN}CARATTERISTICHE FINALI:${NC}"
echo "• Play Store: Abilitato su tutti i 31 AVD"
echo "• Gmail: Da rimuovere dopo primo avvio"
echo "• Nomi: Puliti e corrispondenti ai giochi"
echo "• Performance: Ottimizzate per RTX 4080"
echo ""
echo "🎮 ${CYAN}UTILIZZO FINALE:${NC}"
echo ""
echo "1. ${YELLOW}Android Studio → Tools → Virtual Device Manager${NC}"
echo "2. ${YELLOW}Seleziona AVD con nome del gioco${NC}"
echo "3. ${YELLOW}Clicca Play (▶️) per avviare${NC}"
echo "4. ${YELLOW}Apri Play Store → Cerca gioco → Installa${NC}"
echo "5. ${YELLOW}Rimuovi Gmail: ./remove_gmail_from_emulators.sh${NC}"
echo ""
echo "SPAZIO UTILIZZATO: $TOTAL_SIZE"
echo "AVD TOTALI: $FINAL_COUNT"
echo ""
echo -e "${GREEN}SISTEMA GAMING ANDROID DEFINITIVO PRONTO!${NC} 🎮"
echo ""
echo "Ora hai esattamente 31 emulatori con nomi puliti,"
echo "Play Store funzionante e configurazioni ottimali!"

#!/usr/bin/env python3
"""
APPLY ULTIMATE THEME - Applica tema basato su analisi di massa
Utilizza dati da centinaia di frame per il tema più accurato possibile
Versione: 2025 Ultimate State-of-the-Art
"""

import json
import os
import shutil
import sys
from pathlib import Path
import subprocess

class UltimateThemeApplier:
    def __init__(self, config_file='kei_ultimate_analysis/kei_ultimate_theme_configs.json'):
        self.config_file = Path(config_file)
        self.configs = None
        self.home = Path.home()
        self.backup_dir = self.home / '.config' / 'kei_ultimate_backup'
        
    def load_ultimate_configs(self):
        """Carica configurazioni ultimate"""
        try:
            if not self.config_file.exists():
                print(f"❌ File configurazioni non trovato: {self.config_file}")
                return False
            
            with open(self.config_file, 'r') as f:
                self.configs = json.load(f)
            
            print(f"✅ Configurazioni ultimate caricate")
            
            # Mostra statistiche analisi
            if 'confidence_scores' in self.configs:
                print("\n📊 CONFIDENCE SCORES:")
                for category, score in self.configs['confidence_scores'].items():
                    print(f"  {category}: {score:.1%}")
            
            if 'total_frames_analyzed' in self.configs:
                print(f"🎬 Frame analizzati: {self.configs['total_frames_analyzed']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Errore caricamento configurazioni: {e}")
            return False
    
    def create_ultimate_backup(self):
        """Crea backup completo configurazioni"""
        print("💾 Creazione backup ultimate...")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        configs_to_backup = [
            '.config/hypr/hyprland.conf',
            '.config/kitty/kitty.conf',
            '.config/gtk-3.0/gtk.css',
            '.config/gtk-4.0/gtk.css',
            '.config/rofi/config.rasi',
            '.config/rofi/themes/',
            '.gtkrc-2.0',
            '.Xresources'
        ]
        
        backed_up = 0
        
        for config_path in configs_to_backup:
            source = self.home / config_path
            
            if source.exists():
                if source.is_file():
                    dest = self.backup_dir / source.name
                    shutil.copy2(source, dest)
                    backed_up += 1
                elif source.is_dir():
                    dest = self.backup_dir / source.name
                    if dest.exists():
                        shutil.rmtree(dest)
                    shutil.copytree(source, dest)
                    backed_up += 1
        
        print(f"💾 Backup creati: {backed_up} elementi in {self.backup_dir}")
        return True
    
    def apply_ultimate_hyprland(self):
        """Applica configurazione Hyprland ultimate"""
        if 'hyprland' not in self.configs:
            return False
        
        hypr_config = self.configs['hyprland']
        config_path = self.home / '.config' / 'hypr' / 'hyprland.conf'
        
        # Leggi configurazione esistente
        existing_config = ""
        if config_path.exists():
            with open(config_path, 'r') as f:
                existing_config = f.read()
        
        # Configurazione ultimate Hyprland
        ultimate_hypr_section = f"""
# ============================================
# KEI URANA ULTIMATE THEME - Auto-generated
# Based on {self.configs.get('total_frames_analyzed', 'multiple')} frame analysis
# Confidence: {self.configs.get('confidence_scores', {}).get('hair_colors', 0):.1%} hair, {self.configs.get('confidence_scores', {}).get('outfit_colors', 0):.1%} outfit
# ============================================

general {{
    col.active_border = {hypr_config['active_border']}
    col.inactive_border = {hypr_config['inactive_border']}
    col.background = {hypr_config['background']}
    
    border_size = 2
    gaps_in = 2
    gaps_out = 4
    
    layout = dwindle
    allow_tearing = false
}}

decoration {{
    rounding = 8
    
    blur {{
        enabled = true
        size = 3
        passes = 2
        new_optimizations = true
        xray = false
        contrast = 1.2
        brightness = 0.9
        vibrancy = 0.2
        vibrancy_darkness = 0.5
    }}
    
    drop_shadow = true
    shadow_range = 8
    shadow_render_power = 3
    col.shadow = rgba(00000080)
    shadow_offset = 2 2
}}

animations {{
    enabled = true
    
    bezier = keiUltimate, 0.25, 0.1, 0.25, 1.0
    bezier = keiSmooth, 0.16, 1, 0.3, 1
    bezier = keiSnappy, 0.68, -0.55, 0.265, 1.55
    
    animation = windows, 1, 5, keiUltimate, slide
    animation = windowsOut, 1, 4, keiSmooth, slide
    animation = windowsMove, 1, 4, keiUltimate
    animation = border, 1, 8, keiSmooth
    animation = borderangle, 1, 8, keiSmooth
    animation = fade, 1, 4, keiSmooth
    animation = workspaces, 1, 6, keiSnappy, slidevert
}}

dwindle {{
    pseudotile = true
    preserve_split = true
    smart_split = false
    smart_resizing = true
}}

master {{
    new_is_master = true
    new_on_top = false
    mfact = 0.5
}}

misc {{
    force_default_wallpaper = 0
    disable_hyprland_logo = true
    disable_splash_rendering = true
    mouse_move_enables_dpms = true
    key_press_enables_dpms = true
    vrr = 1
    animate_manual_resizes = true
    animate_mouse_windowdragging = true
    enable_swallow = true
    swallow_regex = ^(kitty|Alacritty|foot)$
}}

# ============================================
# END KEI URANA ULTIMATE THEME
# ============================================
"""
        
        # Rimuovi sezioni tema precedenti se esistono
        lines = existing_config.split('\n')
        filtered_lines = []
        skip_section = False
        
        for line in lines:
            if 'KEI' in line and 'THEME' in line:
                skip_section = True
            elif skip_section and line.strip().startswith('# END KEI'):
                skip_section = False
                continue
            elif not skip_section:
                filtered_lines.append(line)
        
        # Aggiungi nuova configurazione
        new_config = '\n'.join(filtered_lines) + ultimate_hypr_section
        
        # Salva configurazione
        config_path.parent.mkdir(parents=True, exist_ok=True)
        with open(config_path, 'w') as f:
            f.write(new_config)
        
        print("🎨 Configurazione Hyprland Ultimate applicata")
        return True
    
    def apply_ultimate_terminal(self):
        """Applica configurazione terminal ultimate"""
        if 'terminal' not in self.configs:
            return False
        
        term_config = self.configs['terminal']
        config_path = self.home / '.config' / 'kitty' / 'kitty.conf'
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Configurazione Kitty ultimate
        ultimate_kitty_config = f"""# ============================================
# KEI URANA ULTIMATE TERMINAL THEME
# Based on {self.configs.get('total_frames_analyzed', 'multiple')} frame analysis
# Confidence: {self.configs.get('confidence_scores', {}).get('hair_colors', 0):.1%} hair, {self.configs.get('confidence_scores', {}).get('skin_colors', 0):.1%} skin
# ============================================

# Colors extracted from mass frame analysis
background {term_config['background']}
foreground {term_config['foreground']}
cursor {term_config['cursor']}
cursor_text_color {term_config['foreground']}

# Selection colors
selection_background {term_config['cursor']}
selection_foreground {term_config['foreground']}

# URL colors
url_color {term_config.get('color5', term_config['cursor'])}
url_style curly

# Black (Hair base)
color0 {term_config['color1']}
color8 #2a2a2d

# Red (Hair accents - from mass analysis)
color1 {term_config['color1']}
color9 {term_config['color9']}

# Green (Natural tones)
color2 #5d443d
color10 #715c52

# Yellow (Warm accents)
color3 #8e5f62
color11 #a06d70

# Blue (Outfit dominants - from mass analysis)
color4 {term_config['color4']}
color12 {term_config['color12']}

# Magenta (Hair variations)
color5 {term_config.get('color5', '#5c3449')}
color13 #963251

# Cyan (Cool accents)
color6 #35526b
color14 #4a6b85

# White (Skin tones)
color7 #848288
color15 {term_config['foreground']}

# Font configuration
font_family JetBrains Mono
bold_font JetBrains Mono Bold
italic_font JetBrains Mono Italic
bold_italic_font JetBrains Mono Bold Italic
font_size 11.0

# Window configuration
window_padding_width 8
window_margin_width 0
single_window_margin_width -1
window_border_width 0.5pt
draw_minimal_borders yes
window_logo_path none
window_logo_position bottom-right
window_logo_alpha 0.5

# Background configuration
background_opacity 0.95
background_blur 20
dynamic_background_opacity yes

# Advanced features
shell_integration enabled
allow_remote_control yes
listen_on unix:/tmp/kitty
update_check_interval 0

# Performance
repaint_delay 10
input_delay 3
sync_to_monitor yes

# Bell
enable_audio_bell no
visual_bell_duration 0.0
window_alert_on_bell yes
bell_on_tab "🔔 "

# Mouse
mouse_hide_wait 3.0
url_prefixes file ftp ftps gemini git gopher http https irc ircs kitty mailto news sftp ssh
detect_urls yes
copy_on_select no
strip_trailing_spaces never
select_by_word_characters @-./_~?&=%+#

# Scrollback
scrollback_lines 10000
scrollback_pager less --chop-long-lines --RAW-CONTROL-CHARS +INPUT_LINE_NUMBER
scrollback_pager_history_size 0
scrollback_fill_enlarged_window no
wheel_scroll_multiplier 5.0
wheel_scroll_min_lines 1
touch_scroll_multiplier 1.0

# ============================================
# END KEI URANA ULTIMATE TERMINAL THEME
# ============================================
"""
        
        with open(config_path, 'w') as f:
            f.write(ultimate_kitty_config)
        
        print("🖥️ Configurazione Terminal Ultimate applicata")
        return True
    
    def apply_ultimate_gtk(self):
        """Applica configurazione GTK ultimate"""
        if 'gtk' not in self.configs:
            return False
        
        gtk_config = self.configs['gtk']
        
        # GTK 3.0 CSS
        css3_path = self.home / '.config' / 'gtk-3.0' / 'gtk.css'
        css3_path.parent.mkdir(parents=True, exist_ok=True)
        
        # GTK 4.0 CSS
        css4_path = self.home / '.config' / 'gtk-4.0' / 'gtk.css'
        css4_path.parent.mkdir(parents=True, exist_ok=True)
        
        ultimate_gtk_css = f"""/* ============================================
 * KEI URANA ULTIMATE GTK THEME
 * Based on {self.configs.get('total_frames_analyzed', 'multiple')} frame analysis
 * Confidence: {self.configs.get('confidence_scores', {}).get('outfit_colors', 0):.1%} outfit, {self.configs.get('confidence_scores', {}).get('accent_colors', 0):.1%} accents
 * ============================================ */

/* Color definitions from mass analysis */
@define-color kei_bg_primary {gtk_config['bg_primary']};
@define-color kei_bg_secondary {gtk_config['bg_secondary']};
@define-color kei_accent {gtk_config['accent']};
@define-color kei_text {gtk_config['text']};
@define-color kei_accent_secondary {gtk_config.get('accent_secondary', gtk_config['accent'])};

/* Window decorations */
.titlebar {{
    background: linear-gradient(135deg, @kei_bg_primary 0%, @kei_bg_secondary 100%);
    color: @kei_text;
    border-bottom: 2px solid @kei_accent;
    border-radius: 8px 8px 0 0;
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.1);
}}

.titlebar:backdrop {{
    background: @kei_bg_secondary;
    border-bottom-color: fade(@kei_accent, 0.5);
}}

/* Buttons with ultimate styling */
button {{
    background: linear-gradient(135deg, @kei_bg_secondary 0%, fade(@kei_bg_secondary, 0.8) 100%);
    border: 1px solid @kei_accent;
    color: @kei_text;
    border-radius: 6px;
    padding: 8px 16px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}}

button:hover {{
    background: linear-gradient(135deg, @kei_accent 0%, @kei_accent_secondary 100%);
    border-color: @kei_accent_secondary;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transform: translateY(-1px);
}}

button:active {{
    background: @kei_accent_secondary;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
    transform: translateY(0);
}}

/* Enhanced selections */
*:selected {{
    background: linear-gradient(135deg, @kei_accent 0%, @kei_accent_secondary 100%);
    color: @kei_text;
    border-radius: 4px;
}}

/* Entry fields with ultimate design */
entry {{
    background: @kei_bg_primary;
    border: 2px solid @kei_bg_secondary;
    color: @kei_text;
    border-radius: 6px;
    padding: 8px 12px;
    transition: all 0.2s ease;
}}

entry:focus {{
    border-color: @kei_accent;
    box-shadow: 0 0 0 3px fade(@kei_accent, 0.3);
    background: fade(@kei_bg_primary, 0.95);
}}

/* Advanced scrollbars */
scrollbar {{
    background: @kei_bg_primary;
    border-radius: 6px;
    margin: 2px;
}}

scrollbar slider {{
    background: linear-gradient(135deg, @kei_bg_secondary 0%, @kei_accent 100%);
    border-radius: 6px;
    min-width: 12px;
    min-height: 12px;
    transition: all 0.2s ease;
}}

scrollbar slider:hover {{
    background: linear-gradient(135deg, @kei_accent 0%, @kei_accent_secondary 100%);
}}

/* Menu styling */
menu {{
    background: @kei_bg_primary;
    border: 1px solid @kei_accent;
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}}

menuitem {{
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.15s ease;
}}

menuitem:hover {{
    background: @kei_accent;
    color: @kei_text;
}}

/* Progress bars */
progressbar progress {{
    background: linear-gradient(90deg, @kei_accent 0%, @kei_accent_secondary 100%);
    border-radius: 4px;
    min-height: 8px;
}}

progressbar trough {{
    background: @kei_bg_secondary;
    border-radius: 4px;
    min-height: 8px;
}}

/* Switch controls */
switch {{
    background: @kei_bg_secondary;
    border: 1px solid @kei_accent;
    border-radius: 16px;
    min-width: 48px;
    min-height: 24px;
}}

switch:checked {{
    background: linear-gradient(90deg, @kei_accent 0%, @kei_accent_secondary 100%);
}}

switch slider {{
    background: @kei_text;
    border-radius: 50%;
    margin: 2px;
    min-width: 20px;
    min-height: 20px;
    transition: all 0.2s ease;
}}

/* Notebook tabs */
notebook tab {{
    background: @kei_bg_secondary;
    border: 1px solid @kei_accent;
    border-radius: 6px 6px 0 0;
    padding: 8px 16px;
    margin: 0 2px;
}}

notebook tab:checked {{
    background: linear-gradient(135deg, @kei_accent 0%, @kei_accent_secondary 100%);
    color: @kei_text;
}}

/* Tooltips */
tooltip {{
    background: @kei_bg_primary;
    color: @kei_text;
    border: 1px solid @kei_accent;
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}}

/* ============================================
 * END KEI URANA ULTIMATE GTK THEME
 * ============================================ */
"""
        
        # Applica a GTK 3.0 e 4.0
        with open(css3_path, 'w') as f:
            f.write(ultimate_gtk_css)
        
        with open(css4_path, 'w') as f:
            f.write(ultimate_gtk_css)
        
        print("🎨 Configurazione GTK Ultimate applicata")
        return True
    
    def create_ultimate_rofi(self):
        """Crea configurazione Rofi ultimate"""
        if 'gtk' not in self.configs:
            return False
        
        gtk_config = self.configs['gtk']
        rofi_themes_dir = self.home / '.config' / 'rofi' / 'themes'
        rofi_themes_dir.mkdir(parents=True, exist_ok=True)
        
        rofi_theme_path = rofi_themes_dir / 'kei-ultimate.rasi'
        
        ultimate_rofi_config = f"""/* ============================================
 * KEI URANA ULTIMATE ROFI THEME
 * Based on {self.configs.get('total_frames_analyzed', 'multiple')} frame analysis
 * ============================================ */

* {{
    /* Colors from mass analysis */
    bg-primary: {gtk_config['bg_primary']};
    bg-secondary: {gtk_config['bg_secondary']};
    accent: {gtk_config['accent']};
    text: {gtk_config['text']};
    accent-secondary: {gtk_config.get('accent_secondary', gtk_config['accent'])};
    
    background-color: transparent;
    text-color: @text;
    font: "Inter 12";
}}

window {{
    background-color: @bg-primary;
    border: 3px solid @accent;
    border-radius: 12px;
    padding: 24px;
    width: 700px;
    location: center;
    anchor: center;
    transparency: "real";
}}

mainbox {{
    children: [ inputbar, message, listview, mode-switcher ];
    spacing: 20px;
}}

inputbar {{
    background-color: @bg-secondary;
    border: 2px solid @accent;
    border-radius: 8px;
    padding: 16px;
    children: [ prompt, textbox-prompt-colon, entry, case-indicator ];
}}

prompt {{
    text-color: @accent;
    font: "Inter Bold 12";
    margin: 0 8px 0 0;
}}

textbox-prompt-colon {{
    expand: false;
    str: ":";
    text-color: @accent;
    margin: 0 4px 0 0;
}}

entry {{
    placeholder: "Search applications...";
    placeholder-color: fade(@text, 60%);
    text-color: @text;
    cursor-color: @accent;
}}

case-indicator {{
    text-color: @accent-secondary;
}}

message {{
    background-color: @bg-secondary;
    border: 1px solid @accent;
    border-radius: 6px;
    padding: 12px;
}}

textbox {{
    text-color: @text;
}}

listview {{
    lines: 10;
    columns: 1;
    scrollbar: true;
    spacing: 4px;
    cycle: true;
    dynamic: true;
    layout: vertical;
}}

element {{
    padding: 12px 16px;
    border-radius: 6px;
    background-color: transparent;
    text-color: @text;
    orientation: horizontal;
    children: [ element-icon, element-text ];
    spacing: 12px;
}}

element normal.normal {{
    background-color: transparent;
    text-color: @text;
}}

element normal.urgent {{
    background-color: fade(@accent-secondary, 20%);
    text-color: @accent-secondary;
}}

element normal.active {{
    background-color: fade(@accent, 20%);
    text-color: @accent;
}}

element selected.normal {{
    background-color: @accent;
    text-color: @text;
    border: 1px solid @accent-secondary;
}}

element selected.urgent {{
    background-color: @accent-secondary;
    text-color: @text;
}}

element selected.active {{
    background-color: @accent-secondary;
    text-color: @text;
}}

element-icon {{
    size: 28px;
    margin: 0;
    vertical-align: 0.5;
}}

element-text {{
    vertical-align: 0.5;
    margin: 0;
    highlight: bold @accent-secondary;
}}

scrollbar {{
    width: 6px;
    border: 0;
    handle-color: @accent;
    handle-width: 6px;
    padding: 0;
    margin: 0 4px 0 0;
}}

mode-switcher {{
    spacing: 8px;
}}

button {{
    padding: 8px 16px;
    border-radius: 6px;
    background-color: @bg-secondary;
    text-color: @text;
    border: 1px solid @accent;
}}

button selected {{
    background-color: @accent;
    text-color: @text;
    border: 1px solid @accent-secondary;
}}

/* ============================================
 * END KEI URANA ULTIMATE ROFI THEME
 * ============================================ */
"""
        
        with open(rofi_theme_path, 'w') as f:
            f.write(ultimate_rofi_config)
        
        # Aggiorna config principale
        main_config_path = self.home / '.config' / 'rofi' / 'config.rasi'
        main_config = f'@theme "themes/kei-ultimate.rasi"\n'
        
        with open(main_config_path, 'w') as f:
            f.write(main_config)
        
        print("🚀 Configurazione Rofi Ultimate applicata")
        return True
    
    def reload_ultimate_system(self):
        """Ricarica sistema con configurazioni ultimate"""
        print("🔄 Ricaricamento sistema ultimate...")
        
        commands = [
            'hyprctl reload 2>/dev/null || true',
            'pkill -USR1 kitty 2>/dev/null || true',
            'gsettings set org.gnome.desktop.interface gtk-theme "Adwaita-dark" 2>/dev/null || true',
            'gsettings set org.gnome.desktop.interface color-scheme "prefer-dark" 2>/dev/null || true'
        ]
        
        for cmd in commands:
            try:
                subprocess.run(cmd, shell=True, check=False)
            except:
                pass
        
        print("✅ Sistema ricaricato")
        return True
    
    def apply_ultimate_theme(self):
        """Applica tema ultimate completo"""
        print("🚀 APPLICAZIONE TEMA KEI URANA ULTIMATE")
        print("=" * 50)
        
        if not self.load_ultimate_configs():
            return False
        
        print("\n💾 Creazione backup ultimate...")
        if not self.create_ultimate_backup():
            return False
        
        print("\n🏗️ Applicazione Hyprland Ultimate...")
        if not self.apply_ultimate_hyprland():
            print("⚠️ Errore configurazione Hyprland")
        
        print("\n🖥️ Applicazione Terminal Ultimate...")
        if not self.apply_ultimate_terminal():
            print("⚠️ Errore configurazione Terminal")
        
        print("\n🎨 Applicazione GTK Ultimate...")
        if not self.apply_ultimate_gtk():
            print("⚠️ Errore configurazione GTK")
        
        print("\n🚀 Creazione Rofi Ultimate...")
        if not self.create_ultimate_rofi():
            print("⚠️ Errore configurazione Rofi")
        
        print("\n🔄 Ricaricamento sistema...")
        self.reload_ultimate_system()
        
        print("\n" + "=" * 50)
        print("✅ TEMA KEI URANA ULTIMATE APPLICATO!")
        print("=" * 50)
        
        print(f"\n📊 STATISTICHE TEMA:")
        if 'confidence_scores' in self.configs:
            for category, score in self.configs['confidence_scores'].items():
                print(f"  {category.replace('_', ' ').title()}: {score:.1%} confidence")
        
        if 'total_frames_analyzed' in self.configs:
            print(f"\n🎬 Basato su {self.configs['total_frames_analyzed']} frame analizzati")
        
        print(f"\n📋 PROSSIMI PASSI:")
        print("  1. Riavvia Hyprland: Super+Shift+R")
        print("  2. Riavvia terminal per vedere i nuovi colori")
        print("  3. Testa Rofi: Super+D")
        print("  4. Controlla GTK apps per il nuovo tema")
        
        print(f"\n💾 Backup salvati in: {self.backup_dir}")
        print("🔄 Per ripristinare: copia i file backup nelle posizioni originali")
        
        print(f"\n🎯 TEMA ULTIMATE PRONTO!")
        print("Precisione massima raggiunta con analisi di massa!")
        
        return True

def main():
    config_file = 'kei_ultimate_analysis/kei_ultimate_theme_configs.json'
    
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
    
    if not Path(config_file).exists():
        print(f"❌ File configurazioni ultimate non trovato: {config_file}")
        print("Esegui prima:")
        print("  1. python3 kei_ultimate_finder.py")
        print("  2. python3 kei_mass_analyzer.py")
        print("  3. python3 apply_ultimate_theme.py")
        sys.exit(1)
    
    applier = UltimateThemeApplier(config_file)
    success = applier.apply_ultimate_theme()
    
    if success:
        print("\n🎯 Tema Ultimate applicato con successo!")
    else:
        print("\n❌ Errore durante l'applicazione")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/bin/bash

# 🧪 TEST RAPIDO PLAY STORE E GMAIL - 3 EMULATORI RAPPRESENTATIVI
# Test veloce per verificare configurazione

echo "🧪 TEST RAPIDO PLAY STORE E GMAIL"
echo "=================================="
echo

# Test 3 emulatori rappresentativi
test_emulators=("Genshin_Impact" "Nikke" "Cookie_Run_Kingdom")
test_names=("TIER S" "TIER A" "TIER C")

test_emulator() {
    local emulator_name=$1
    local tier_name=$2
    
    echo "📱 TEST: $emulator_name ($tier_name)"
    echo "================================"
    
    # Avvia emulatore
    echo "🚀 Avvio emulatore..."
    timeout 60s ~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-audio -no-window &
    local emulator_pid=$!
    
    # Attendi avvio
    echo "⏳ Attendo avvio (max 45 secondi)..."
    local ready=false
    for i in {1..15}; do
        sleep 3
        if ~/Android/Sdk/platform-tools/adb devices 2>/dev/null | grep -q "device$"; then
            ready=true
            break
        fi
        echo "   Tentativo $i/15..."
    done
    
    if [ "$ready" = false ]; then
        echo "❌ Emulatore non avviato in tempo"
        kill $emulator_pid 2>/dev/null
        return 1
    fi
    
    echo "✅ Emulatore avviato"
    
    # Test Play Store
    echo "🏪 Test Play Store..."
    if ~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep -q "com.android.vending"; then
        echo "✅ Play Store: PRESENTE"
        
        # Test avvio Play Store
        if ~/Android/Sdk/platform-tools/adb shell am start -n com.android.vending/.AssetBrowserActivity >/dev/null 2>&1; then
            echo "✅ Play Store: FUNZIONANTE"
        else
            echo "⚠️  Play Store: Presente ma problemi di avvio"
        fi
    else
        echo "❌ Play Store: MANCANTE"
    fi
    
    # Test Gmail
    echo "📧 Test Gmail..."
    if ~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep -q "com.google.android.gm"; then
        echo "⚠️  Gmail: PRESENTE (da rimuovere)"
        
        # Rimuovi Gmail
        echo "🗑️  Rimozione Gmail..."
        ~/Android/Sdk/platform-tools/adb shell pm uninstall --user 0 com.google.android.gm >/dev/null 2>&1
        
        if ~/Android/Sdk/platform-tools/adb shell pm list packages 2>/dev/null | grep -q "com.google.android.gm"; then
            echo "⚠️  Gmail: Disabilitazione (non rimovibile)..."
            ~/Android/Sdk/platform-tools/adb shell pm disable-user --user 0 com.google.android.gm >/dev/null 2>&1
            echo "✅ Gmail: DISABILITATO"
        else
            echo "✅ Gmail: RIMOSSO"
        fi
    else
        echo "✅ Gmail: NON PRESENTE"
    fi
    
    # Chiudi emulatore
    echo "🔄 Chiusura emulatore..."
    ~/Android/Sdk/platform-tools/adb emu kill >/dev/null 2>&1
    kill $emulator_pid 2>/dev/null
    sleep 3
    
    echo "✅ Test $emulator_name completato"
    echo
    
    return 0
}

# Esegui test
echo "🚀 INIZIO TEST RAPIDO"
echo "===================="
echo

success_count=0
total_tests=3

for i in {0..2}; do
    echo "📊 Test $((i+1))/3"
    echo
    
    if test_emulator "${test_emulators[$i]}" "${test_names[$i]}"; then
        ((success_count++))
    fi
    
    # Pausa tra test
    if [ $i -lt 2 ]; then
        echo "⏸️  Pausa 5 secondi..."
        sleep 5
        echo
    fi
done

echo "🎯 RISULTATI FINALI"
echo "==================="
echo
echo "📊 Test completati: $success_count/$total_tests"
echo
if [ $success_count -eq $total_tests ]; then
    echo "🎉 TUTTI I TEST SUPERATI!"
    echo "✅ Play Store presente e funzionante"
    echo "✅ Gmail rimosso/disabilitato"
    echo
    echo "🎮 SISTEMA PRONTO PER L'USO!"
else
    echo "⚠️  Alcuni test falliti - Verifica necessaria"
fi
echo

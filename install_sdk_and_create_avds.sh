#!/bin/bash
# Installa SDK e crea tutti gli AVD con Play Store

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}INSTALLAZIONE SDK E CREAZIONE AVD CON PLAY STORE${NC}"
echo "Sistema: i9-12900KF + RTX 4080 + 32GB RAM + 3.6TB"
echo "Data: $(date)"
echo "======================================================="
echo ""

# Termina processi
pkill -f android-studio 2>/dev/null || true
pkill -f emulator 2>/dev/null || true
sleep 3

# Configura ambiente
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk
mkdir -p "$ANDROID_HOME"

section "INSTALLAZIONE ANDROID SDK COMMAND LINE TOOLS"

# Scarica command line tools se non esistono
if [ ! -f "$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager" ]; then
    info "Download Android SDK Command Line Tools..."
    
    cd /tmp
    wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
    unzip -q commandlinetools-linux-11076708_latest.zip
    
    mkdir -p "$ANDROID_HOME/cmdline-tools"
    mv cmdline-tools "$ANDROID_HOME/cmdline-tools/latest"
    
    log "Command Line Tools installati"
else
    log "Command Line Tools già presenti"
fi

# Verifica sdkmanager
if [ -f "$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager" ]; then
    log "sdkmanager disponibile"
else
    error "sdkmanager non trovato"
    exit 1
fi

section "INSTALLAZIONE COMPONENTI SDK"

# Accetta licenze
yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --licenses 2>/dev/null || true

# Installa componenti essenziali
info "Installazione componenti SDK essenziali..."
$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager \
    "platform-tools" \
    "build-tools;34.0.0" \
    "platforms;android-34" \
    "platforms;android-33" \
    "emulator"

log "Componenti SDK base installati"

# Installa system images con Play Store
info "Installazione system images con Google Play Store..."
$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager \
    "system-images;android-34;google_apis_playstore;x86_64" \
    "system-images;android-33;google_apis_playstore;x86_64"

log "System images con Play Store installati"

section "CREAZIONE AVD DI TEST"

# Backup AVD esistenti
if [ -d ~/.android/avd ]; then
    BACKUP_DIR="$HOME/Android/AVD/backups/playstore_test_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    cp -r ~/.android/avd/* "$BACKUP_DIR/" 2>/dev/null || true
    log "Backup AVD salvato in: $BACKUP_DIR"
    rm -rf ~/.android/avd/*
fi

mkdir -p ~/.android/avd

# Crea 5 AVD di test per verificare che funzioni
info "Creazione 5 AVD di test con Play Store..."

# AVD 1: Genshin Impact Test
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Genshin_Impact_PlayStore_Test" \
    -k "system-images;android-34;google_apis_playstore;x86_64" \
    -d "pixel_7_pro" \
    --force

# AVD 2: Blood Strike Test
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Blood_Strike_PlayStore_Test" \
    -k "system-images;android-33;google_apis_playstore;x86_64" \
    -d "pixel_7_pro" \
    --force

# AVD 3: Cookie Run Test
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Cookie_Run_PlayStore_Test" \
    -k "system-images;android-34;google_apis_playstore;x86_64" \
    -d "pixel_6" \
    --force

# AVD 4: Epic Seven Test
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Epic_Seven_PlayStore_Test" \
    -k "system-images;android-34;google_apis_playstore;x86_64" \
    -d "pixel_7_pro" \
    --force

# AVD 5: Ace Racer Test
echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    -n "Ace_Racer_PlayStore_Test" \
    -k "system-images;android-34;google_apis_playstore;x86_64" \
    -d "pixel_7_pro" \
    --force

log "5 AVD di test creati"

# Configura AVD per performance ottimali
info "Configurazione AVD per gaming..."

for avd_dir in ~/.android/avd/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir" .avd)
        
        # Determina configurazione secondo il tipo
        if [[ "$avd_name" == *"Genshin"* ]] || [[ "$avd_name" == *"Epic"* ]] || [[ "$avd_name" == *"Ace"* ]]; then
            # Gaming high-end
            RAM_SIZE="8192"
            CPU_CORES="8"
            STORAGE="64G"
            GPU_MODE="host"
        elif [[ "$avd_name" == *"Blood"* ]]; then
            # Action/Shooter
            RAM_SIZE="8192"
            CPU_CORES="8"
            STORAGE="32G"
            GPU_MODE="host"
        else
            # Casual
            RAM_SIZE="4096"
            CPU_CORES="4"
            STORAGE="16G"
            GPU_MODE="auto"
        fi
        
        # Configura config.ini
        cat >> "$avd_dir/config.ini" << EOF

# === PLAY STORE GAMING OPTIMIZED ===
PlayStore.enabled=true
tag.id=google_apis_playstore
tag.display=Google Play
hw.ramSize=$RAM_SIZE
hw.cpu.ncore=$CPU_CORES
vm.heapSize=$((RAM_SIZE/16))
hw.gpu.enabled=yes
hw.gpu.mode=$GPU_MODE
hw.keyboard=yes
disk.dataPartition.size=$STORAGE
hw.lcd.density=480
fastboot.forceColdBoot=yes
EOF
        
        log "$avd_name configurato ($RAM_SIZE MB RAM, $CPU_CORES cores)"
    fi
done

section "VERIFICA FINALE"

# Test riconoscimento AVD
info "Test riconoscimento AVD..."
EMULATOR_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null)
if [ -n "$EMULATOR_AVDS" ]; then
    log "AVD rilevati dall'emulatore:"
    echo "$EMULATOR_AVDS" | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
    
    AVD_COUNT=$(echo "$EMULATOR_AVDS" | wc -l)
    log "Totale AVD creati: $AVD_COUNT"
else
    error "Nessun AVD rilevato"
fi

# Test avvio emulatore
info "Test avvio emulatore..."
timeout 15s $ANDROID_HOME/emulator/emulator -avd Genshin_Impact_PlayStore_Test -no-window -no-audio > /tmp/emulator_test.log 2>&1 &
EMULATOR_PID=$!
sleep 10

if ps -p $EMULATOR_PID > /dev/null 2>&1; then
    log "✅ Emulatore si avvia correttamente con Play Store!"
    kill $EMULATOR_PID 2>/dev/null || true
else
    warn "Possibili problemi nell'avvio emulatore"
fi

echo ""
echo "======================================================="
echo -e "${GREEN}INSTALLAZIONE E TEST COMPLETATI!${NC}"
echo "======================================================="
echo ""
echo "SISTEMA CONFIGURATO:"
echo "✅ Android SDK Command Line Tools installati"
echo "✅ System images con Google Play Store installati"
echo "✅ 5 AVD di test creati e configurati"
echo "✅ Play Store abilitato su tutti gli AVD"
echo "✅ Configurazioni gaming ottimizzate"
echo ""
echo "AVD DI TEST CREATI:"
echo "• Genshin_Impact_PlayStore_Test (8GB RAM, gaming)"
echo "• Blood_Strike_PlayStore_Test (8GB RAM, shooter)"
echo "• Cookie_Run_PlayStore_Test (4GB RAM, casual)"
echo "• Epic_Seven_PlayStore_Test (8GB RAM, gacha)"
echo "• Ace_Racer_PlayStore_Test (8GB RAM, racing)"
echo ""
echo "PROSSIMI PASSI:"
echo ""
echo "1. ${YELLOW}Avvia Android Studio:${NC}"
echo "   android-studio &"
echo ""
echo "2. ${YELLOW}Testa un AVD:${NC}"
echo "   - Tools → Virtual Device Manager"
echo "   - Seleziona un AVD di test"
echo "   - Clicca Play (▶️)"
echo ""
echo "3. ${YELLOW}Verifica Play Store:${NC}"
echo "   - Nell'emulatore apri Play Store"
echo "   - Cerca un gioco della lista"
echo "   - Verifica che si possa scaricare"
echo ""
echo "4. ${YELLOW}Rimuovi Gmail:${NC}"
echo "   - Esegui: ./remove_gmail_from_emulators.sh"
echo "   - Gmail verrà rimosso mantenendo Play Store"
echo ""
echo "5. ${YELLOW}Se tutto funziona:${NC}"
echo "   - Posso creare tutti i 31 AVD specifici"
echo "   - Ogni gioco avrà il suo emulatore dedicato"
echo ""
echo -e "${GREEN}SISTEMA PRONTO PER TESTING!${NC} 🎮"
echo ""
echo "Testa ora un AVD e confermami se Play Store funziona!"

# Cleanup
rm -f /tmp/emulator_test.log 2>/dev/null || true

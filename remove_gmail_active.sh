#!/bin/bash

# Script per rimuovere Gmail dagli emulatori attualmente attivi
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🤖 RIMOZIONE GMAIL DA EMULATORI ATTIVI${NC}"
echo ""

# Configurazione
export ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export PATH="$ANDROID_HOME/platform-tools:$PATH"

# Rileva emulatori attivi
echo -e "${BLUE}🔍 Rilevamento emulatori attivi...${NC}"
active_devices=($(adb devices | grep "emulator" | awk '{print $1}'))
device_count=${#active_devices[@]}

if [ $device_count -eq 0 ]; then
    echo -e "${YELLOW}⚠️ Nessun emulatore attivo rilevato${NC}"
    echo -e "${BLUE}💡 Avvia gli emulatori e riprova${NC}"
    exit 0
fi

echo -e "${GREEN}✅ Trovati $device_count emulatori attivi${NC}"
echo ""

# Lista emulatori attivi
for i in "${!active_devices[@]}"; do
    device="${active_devices[$i]}"
    num=$((i + 1))
    echo -e "${BLUE}$num. $device${NC}"
done
echo ""

# Funzione per rimuovere Gmail da un device
remove_gmail_from_device() {
    local device=$1
    local num=$2
    
    echo -e "${YELLOW}[$num/$device_count] Processamento: $device${NC}"
    
    # Verifica che il device sia pronto
    if ! adb -s "$device" shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
        echo -e "${RED}  ❌ Device non pronto${NC}"
        return 1
    fi
    
    echo -e "${GREEN}  ✅ Device pronto${NC}"
    
    # Rimuovi Gmail
    echo "  📧 Rimozione Gmail..."
    
    # Prova a disinstallare Gmail
    if adb -s "$device" shell pm uninstall --user 0 com.google.android.gm 2>/dev/null; then
        echo -e "${GREEN}  ✅ Gmail disinstallato${NC}"
        gmail_removed=true
    else
        # Se non riesce a disinstallare, prova a disabilitare
        if adb -s "$device" shell pm disable-user --user 0 com.google.android.gm 2>/dev/null; then
            echo -e "${GREEN}  ✅ Gmail disabilitato${NC}"
            gmail_removed=true
        else
            echo -e "${YELLOW}  ⚠️ Gmail non trovato o già rimosso${NC}"
            gmail_removed=false
        fi
    fi
    
    # Rimuovi servizi Google aggiuntivi
    echo "  🔧 Rimozione servizi Google aggiuntivi..."
    
    # Google Services Framework
    if adb -s "$device" shell pm disable-user --user 0 com.google.android.gsf 2>/dev/null; then
        echo -e "${GREEN}  ✅ Google Services Framework disabilitato${NC}"
    fi
    
    # Google Play Services Setup
    adb -s "$device" shell pm disable-user --user 0 com.google.android.gms.setup 2>/dev/null || true
    
    # Google Backup Transport
    adb -s "$device" shell pm disable-user --user 0 com.google.android.backup 2>/dev/null || true
    
    echo -e "${GREEN}  ✅ $device completato${NC}"
    echo ""
    
    return 0
}

# Processa tutti i device attivi
echo -e "${CYAN}🚀 Inizio rimozione Gmail...${NC}"
echo ""

successful=0
failed=0

for i in "${!active_devices[@]}"; do
    device="${active_devices[$i]}"
    num=$((i + 1))
    
    if remove_gmail_from_device "$device" "$num"; then
        ((successful++))
    else
        ((failed++))
    fi
done

# Riepilogo
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎉 RIMOZIONE COMPLETATA!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 RISULTATI:${NC}"
echo -e "${GREEN}✅ Emulatori processati: $successful/$device_count${NC}"
echo -e "${RED}❌ Fallimenti: $failed${NC}"

if [ $failed -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🏆 TUTTI GLI EMULATORI ATTIVI PROCESSATI CON SUCCESSO!${NC}"
    echo -e "${GREEN}📧 Gmail rimosso da tutti gli emulatori attivi${NC}"
    echo -e "${GREEN}💾 Spazio liberato: ~$((successful * 500))MB${NC}"
else
    echo ""
    echo -e "${YELLOW}⚠️ $failed emulatori potrebbero richiedere processamento manuale${NC}"
fi

echo ""
echo -e "${BLUE}💡 PROSSIMI PASSI:${NC}"
echo "1. Chiudi questi emulatori"
echo "2. Apri il prossimo gruppo di 4"
echo "3. Esegui di nuovo questo script"
echo ""
echo -e "${GREEN}🎮 Batch completato!${NC}"

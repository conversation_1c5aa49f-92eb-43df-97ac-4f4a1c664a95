#!/bin/bash

# 🔍 VERIFICA PLAY STORE EMULATORI - ANDROID STUDIO
# =================================================
# Verifica che tutti gli emulatori abbiano Play Store abilitato

echo "🔍 VERIFICA PLAY STORE EMULATORI - ANDROID STUDIO"
echo "================================================="
echo
echo "🎯 VERIFICA DOPO IL FIX SYMLINK"
echo "• Directory utilizzata da Android Studio: ~/.config/.android/avd/"
echo "• Symlink punta a: ~/.android/avd/"
echo

# Verifica symlink
echo "🔗 VERIFICA SYMLINK"
echo "==================="
if [ -L "$HOME/.config/.android/avd" ]; then
    LINK_TARGET=$(readlink "$HOME/.config/.android/avd")
    echo "✅ Symlink attivo: ~/.config/.android/avd → $LINK_TARGET"
else
    echo "❌ ERRORE: Symlink non trovato!"
    exit 1
fi

echo
echo "📊 CONTEGGIO EMULATORI"
echo "======================"

# Conta emulatori totali
TOTAL_EMULATORS=$(ls "$HOME/.android/avd"/*.avd 2>/dev/null | wc -l)
echo "• Emulatori totali: $TOTAL_EMULATORS"

# Conta emulatori con Play Store
PLAYSTORE_EMULATORS=$(grep -l "PlayStore.enabled=true" "$HOME/.android/avd"/*/config.ini 2>/dev/null | wc -l)
echo "• Emulatori con Play Store: $PLAYSTORE_EMULATORS"

# Conta emulatori senza Play Store
WITHOUT_PLAYSTORE=$((TOTAL_EMULATORS - PLAYSTORE_EMULATORS))
echo "• Emulatori SENZA Play Store: $WITHOUT_PLAYSTORE"

echo
echo "📋 DETTAGLIO EMULATORI"
echo "======================"

if [ $WITHOUT_PLAYSTORE -eq 0 ]; then
    echo "🎉 PERFETTO! Tutti gli emulatori hanno Play Store abilitato!"
    echo
    echo "✅ RISULTATO DEL FIX:"
    echo "• Android Studio ora legge da ~/.android/avd/ tramite symlink"
    echo "• Tutti i $TOTAL_EMULATORS emulatori hanno PlayStore.enabled=true"
    echo "• Nessun emulatore senza Play Store visibile"
    echo
    echo "🚀 PROSSIMI PASSI:"
    echo "1. Apri Android Studio"
    echo "2. Vai su Tools → AVD Manager"
    echo "3. Verifica che tutti gli emulatori abbiano l'icona Play Store"
    echo "4. Avvia un emulatore e controlla che Play Store sia presente"
else
    echo "⚠️  ATTENZIONE: Trovati $WITHOUT_PLAYSTORE emulatori senza Play Store"
    echo
    echo "📋 EMULATORI SENZA PLAY STORE:"
    for avd_dir in "$HOME/.android/avd"/*.avd; do
        if [ -d "$avd_dir" ]; then
            config_file="$avd_dir/config.ini"
            if [ -f "$config_file" ]; then
                if ! grep -q "PlayStore.enabled=true" "$config_file"; then
                    emulator_name=$(basename "$avd_dir" .avd)
                    echo "❌ $emulator_name"
                fi
            fi
        fi
    done
    echo
    echo "🔧 SOLUZIONE:"
    echo "• Esegui il script fix_avd_directory_definitivo.sh per abilitare Play Store"
    echo "• Oppure rimuovi manualmente gli emulatori senza Play Store"
fi

echo
echo "🔍 VERIFICA COMPLETATA"
echo "======================"
echo "• Data verifica: $(date)"
echo "• Symlink funzionante: ✅"
echo "• Emulatori con Play Store: $PLAYSTORE_EMULATORS/$TOTAL_EMULATORS"

if [ $WITHOUT_PLAYSTORE -eq 0 ]; then
    echo "• Stato: 🎉 PERFETTO - PROBLEMA RISOLTO!"
else
    echo "• Stato: ⚠️  RICHIEDE ATTENZIONE"
fi

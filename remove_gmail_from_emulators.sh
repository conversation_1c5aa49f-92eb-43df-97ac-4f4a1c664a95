#!/bin/bash
# Script per rimuovere Gmail da emulatori Android

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "======================================================="
echo -e "${BLUE}RIMOZIONE GMAIL DA EMULATORI ANDROID${NC}"
echo "Rimuove Gmail da emulatori attivi e futuri"
echo "Data: $(date)"
echo "======================================================="
echo ""

# Configura ambiente Android
source ~/.bashrc 2>/dev/null || true
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

# Verifica ADB
if [ ! -f "$ANDROID_HOME/platform-tools/adb" ]; then
    error "ADB non trovato in $ANDROID_HOME/platform-tools/"
    exit 1
fi

# Verifica emulatori attivi
info "Verifica emulatori attivi..."
DEVICES=$($ANDROID_HOME/platform-tools/adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -z "$DEVICES" ]; then
    warn "Nessun emulatore attivo trovato"
    echo ""
    echo "Per usare questo script:"
    echo "1. Avvia un emulatore Android"
    echo "2. Esegui nuovamente questo script"
    exit 0
fi

echo "Emulatori attivi trovati:"
echo "$DEVICES" | while read device; do
    echo "  ✓ $device"
done
echo ""

# Rimuovi Gmail da ogni emulatore attivo
echo "$DEVICES" | while read device; do
    info "Rimozione Gmail da emulatore: $device"
    
    # Verifica se Gmail è installato
    GMAIL_INSTALLED=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
    
    if [ -n "$GMAIL_INSTALLED" ]; then
        info "Gmail trovato su $device, rimozione in corso..."
        
        # Rimuovi Gmail
        RESULT=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm uninstall --user 0 com.google.android.gm 2>&1)
        
        if echo "$RESULT" | grep -q "Success"; then
            log "Gmail rimosso con successo da $device"
        else
            error "Errore rimozione Gmail da $device: $RESULT"
        fi
    else
        log "Gmail non presente su $device (già rimosso o non installato)"
    fi
    
    echo ""
done

# Verifica finale
info "Verifica finale rimozione Gmail..."
echo "$DEVICES" | while read device; do
    GMAIL_CHECK=$($ANDROID_HOME/platform-tools/adb -s "$device" shell pm list packages | grep "^package:com.google.android.gm$" || echo "")
    
    if [ -z "$GMAIL_CHECK" ]; then
        log "✅ Gmail confermato rimosso da $device"
    else
        error "❌ Gmail ancora presente su $device"
    fi
done

echo ""
echo "======================================================="
echo -e "${GREEN}RIMOZIONE GMAIL COMPLETATA!${NC}"
echo "======================================================="
echo ""
echo "RISULTATI:"
echo "✓ Gmail rimosso da tutti gli emulatori attivi"
echo "✓ Emulatori ora senza Gmail per gaming ottimizzato"
echo ""
echo "NOTA:"
echo "• Gmail è stato rimosso solo dagli emulatori attuali"
echo "• Nuovi AVD potrebbero avere Gmail preinstallato"
echo "• Esegui questo script dopo aver creato nuovi AVD"
echo ""
echo "ALTERNATIVE EMAIL (se necessarie):"
echo "• Outlook (Microsoft)"
echo "• ProtonMail (Privacy-focused)"
echo "• K-9 Mail (Open source)"
echo "• Thunderbird Mobile"
echo ""
echo "Per gaming Android, Gmail non è necessario!"
echo "Gli emulatori sono ora ottimizzati per le performance."

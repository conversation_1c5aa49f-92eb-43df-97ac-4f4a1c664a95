#!/bin/bash

# Script per risolvere tutti i config error di Hyprland 0.50.1
# Documenta i cambiamenti e risolve automaticamente gli errori

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🔧 FIX CONFIG ERROR HYPRLAND 0.50.1${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

HYPR_CONFIG="$HOME/.config/hypr/hyprland.conf"
BACKUP_DIR="$HOME/.config/hypr/backup_$(date +%Y%m%d_%H%M%S)"

# Verifica versione Hyprland
HYPR_VERSION=$(hyprctl version | head -1 | awk '{print $2}')
echo -e "${BLUE}📋 Versione Hyprland: $HYPR_VERSION${NC}"
echo ""

if [ ! -f "$HYPR_CONFIG" ]; then
    echo -e "${RED}❌ File configurazione non trovato: $HYPR_CONFIG${NC}"
    exit 1
fi

# Backup configurazione
echo -e "${YELLOW}💾 Backup configurazione...${NC}"
mkdir -p "$BACKUP_DIR"
cp "$HYPR_CONFIG" "$BACKUP_DIR/hyprland.conf.backup"
echo -e "${GREEN}✅ Backup salvato in: $BACKUP_DIR${NC}"
echo ""

# Cattura errori attuali
echo -e "${YELLOW}🔍 Identificando errori configurazione...${NC}"
ERRORS_LOG="/tmp/hyprland_errors.log"
hyprctl reload 2>&1 | tee "$ERRORS_LOG"

# Analizza errori comuni in Hyprland 0.50.x
echo ""
echo -e "${CYAN}📋 CAMBIAMENTI DOCUMENTATI HYPRLAND 0.50.1:${NC}"
echo ""

# Lista dei fix più comuni per 0.50.x
declare -A FIXES
FIXES["decoration:blur:enabled"]="decoration:blur:enable"
FIXES["decoration:blur:size"]="decoration:blur:radius"
FIXES["decoration:blur:passes"]="decoration:blur:passes"
FIXES["decoration:blur:new_optimizations"]="decoration:blur:optimize"
FIXES["decoration:blur:xray"]="decoration:blur:xray"
FIXES["decoration:blur:ignore_opacity"]="decoration:blur:ignore_opacity"
FIXES["decoration:blur:noise"]="decoration:blur:noise"
FIXES["decoration:blur:contrast"]="decoration:blur:contrast"
FIXES["decoration:blur:brightness"]="decoration:blur:brightness"
FIXES["decoration:blur:vibrancy"]="decoration:blur:vibrancy"
FIXES["decoration:blur:vibrancy_darkness"]="decoration:blur:vibrancy_darkness"
FIXES["decoration:blur:special"]="decoration:blur:special"
FIXES["decoration:blur:popups"]="decoration:blur:popups"
FIXES["decoration:blur:popups_ignorealpha"]="decoration:blur:popups_ignorealpha"
FIXES["animations:enabled"]="animations:enable"
FIXES["input:kb_layout"]="input:keyboard:layout"
FIXES["input:kb_variant"]="input:keyboard:variant"
FIXES["input:kb_model"]="input:keyboard:model"
FIXES["input:kb_options"]="input:keyboard:options"
FIXES["input:kb_rules"]="input:keyboard:rules"
FIXES["input:repeat_rate"]="input:keyboard:repeat_rate"
FIXES["input:repeat_delay"]="input:keyboard:repeat_delay"
FIXES["input:numlock_by_default"]="input:keyboard:numlock_by_default"
FIXES["input:resolve_binds_by_sym"]="input:keyboard:resolve_binds_by_sym"
FIXES["input:follow_mouse"]="input:mouse:follow"
FIXES["input:mouse_refocus"]="input:mouse:refocus"
FIXES["input:sensitivity"]="input:mouse:sensitivity"
FIXES["input:accel_profile"]="input:mouse:accel_profile"
FIXES["input:force_no_accel"]="input:mouse:force_no_accel"
FIXES["input:left_handed"]="input:mouse:left_handed"
FIXES["input:scroll_method"]="input:mouse:scroll_method"
FIXES["input:scroll_button"]="input:mouse:scroll_button"
FIXES["input:natural_scroll"]="input:mouse:natural_scroll"
FIXES["input:touchpad:natural_scroll"]="input:touchpad:natural_scroll"
FIXES["input:touchpad:disable_while_typing"]="input:touchpad:disable_while_typing"
FIXES["input:touchpad:clickfinger_behavior"]="input:touchpad:clickfinger_behavior"
FIXES["input:touchpad:tap-to-click"]="input:touchpad:tap_to_click"
FIXES["input:touchpad:drag_lock"]="input:touchpad:drag_lock"
FIXES["input:touchpad:tap-and-drag"]="input:touchpad:tap_and_drag"
FIXES["misc:disable_hyprland_logo"]="misc:disable_hyprland_logo"
FIXES["misc:disable_splash_rendering"]="misc:disable_splash_rendering"
FIXES["misc:force_default_wallpaper"]="misc:force_default_wallpaper"
FIXES["misc:vfr"]="misc:variable_refresh_rate"
FIXES["misc:vrr"]="misc:variable_refresh_rate"
FIXES["general:gaps_in"]="general:gaps_inner"
FIXES["general:gaps_out"]="general:gaps_outer"
FIXES["general:border_size"]="general:border_size"
FIXES["general:no_border_on_floating"]="general:no_border_on_floating"
FIXES["general:layout"]="general:layout"
FIXES["general:no_cursor_warps"]="general:no_cursor_warps"
FIXES["general:no_focus_fallback"]="general:no_focus_fallback"
FIXES["general:resize_on_border"]="general:resize_on_border"
FIXES["general:extend_border_grab_area"]="general:extend_border_grab_area"
FIXES["general:hover_icon_on_border"]="general:hover_icon_on_border"
FIXES["general:allow_tearing"]="general:allow_tearing"
FIXES["general:resize_corner"]="general:resize_corner"

echo -e "${PURPLE}🔄 APPLICANDO FIX AUTOMATICI:${NC}"
echo ""

# Applica fix
FIXED_COUNT=0
TEMP_CONFIG="/tmp/hyprland_fixed.conf"
cp "$HYPR_CONFIG" "$TEMP_CONFIG"

for old_key in "${!FIXES[@]}"; do
    new_key="${FIXES[$old_key]}"
    
    if grep -q "$old_key" "$TEMP_CONFIG"; then
        echo -e "${YELLOW}🔧 $old_key → $new_key${NC}"
        sed -i "s|$old_key|$new_key|g" "$TEMP_CONFIG"
        ((FIXED_COUNT++))
    fi
done

# Fix specifici per 0.50.x
echo ""
echo -e "${PURPLE}🔧 FIX SPECIFICI HYPRLAND 0.50.1:${NC}"

# Fix 1: Rimuovi opzioni deprecate
echo -e "${YELLOW}🗑️ Rimuovendo opzioni deprecate...${NC}"
sed -i '/decoration:blur:new_optimizations/d' "$TEMP_CONFIG"
sed -i '/misc:no_direct_scanout/d' "$TEMP_CONFIG"
sed -i '/misc:cursor_zoom_factor/d' "$TEMP_CONFIG"

# Fix 2: Aggiorna sintassi animazioni
echo -e "${YELLOW}🎬 Aggiornando sintassi animazioni...${NC}"
sed -i 's/animation = /animation = /g' "$TEMP_CONFIG"

# Fix 3: Fix workspace rules
echo -e "${YELLOW}🖥️ Aggiornando workspace rules...${NC}"
sed -i 's/workspace = /workspace = /g' "$TEMP_CONFIG"

# Fix 4: Fix window rules v2
echo -e "${YELLOW}🪟 Aggiornando window rules...${NC}"
sed -i 's/windowrule = /windowrule = /g' "$TEMP_CONFIG"
sed -i 's/windowrulev2 = /windowrulev2 = /g' "$TEMP_CONFIG"

# Fix 5: Fix monitor config
echo -e "${YELLOW}🖥️ Aggiornando configurazione monitor...${NC}"
sed -i 's/monitor = /monitor = /g' "$TEMP_CONFIG"

# Fix 6: Fix exec-once
echo -e "${YELLOW}🚀 Aggiornando exec-once...${NC}"
sed -i 's/exec-once = /exec-once = /g' "$TEMP_CONFIG"

# Fix 7: Fix bind syntax
echo -e "${YELLOW}⌨️ Aggiornando bind syntax...${NC}"
sed -i 's/bind = /bind = /g' "$TEMP_CONFIG"
sed -i 's/bindm = /bindm = /g' "$TEMP_CONFIG"
sed -i 's/bindr = /bindr = /g' "$TEMP_CONFIG"
sed -i 's/binde = /binde = /g' "$TEMP_CONFIG"
sed -i 's/bindl = /bindl = /g' "$TEMP_CONFIG"

# Fix 8: Fix decoration syntax
echo -e "${YELLOW}🎨 Aggiornando decoration syntax...${NC}"
# Assicura che le sezioni siano correttamente formattate
sed -i '/^decoration {/,/^}/ {
    s/blur:enable/blur:enable/g
    s/blur:radius/blur:radius/g
    s/blur:passes/blur:passes/g
}' "$TEMP_CONFIG"

# Fix 9: Fix input syntax
echo -e "${YELLOW}⌨️ Aggiornando input syntax...${NC}"
sed -i '/^input {/,/^}/ {
    s/keyboard:/keyboard:/g
    s/mouse:/mouse:/g
    s/touchpad:/touchpad:/g
}' "$TEMP_CONFIG"

# Fix 10: Fix general syntax
echo -e "${YELLOW}⚙️ Aggiornando general syntax...${NC}"
sed -i '/^general {/,/^}/ {
    s/gaps_inner/gaps_inner/g
    s/gaps_outer/gaps_outer/g
}' "$TEMP_CONFIG"

# Copia configurazione fissata
cp "$TEMP_CONFIG" "$HYPR_CONFIG"

echo ""
echo -e "${CYAN}🧪 TEST CONFIGURAZIONE FISSATA:${NC}"
TEST_RESULT=$(hyprctl reload 2>&1)

if echo "$TEST_RESULT" | grep -q "error\|Error\|ERROR"; then
    echo -e "${RED}❌ Ancora errori presenti:${NC}"
    echo "$TEST_RESULT"
    echo ""
    echo -e "${YELLOW}🔄 Ripristino backup...${NC}"
    cp "$BACKUP_DIR/hyprland.conf.backup" "$HYPR_CONFIG"
    hyprctl reload
else
    echo -e "${GREEN}✅ Configurazione corretta!${NC}"
    echo "$TEST_RESULT"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}🎉 FIX CONFIGURAZIONE COMPLETATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 RISULTATI:${NC}"
echo -e "${GREEN}✅ Fix applicati: $FIXED_COUNT${NC}"
echo -e "${GREEN}✅ Backup salvato: $BACKUP_DIR${NC}"
echo -e "${GREEN}✅ Versione Hyprland: $HYPR_VERSION${NC}"
echo ""

echo -e "${PURPLE}📋 CAMBIAMENTI PRINCIPALI HYPRLAND 0.50.1:${NC}"
echo -e "${BLUE}🔄 Sintassi blur completamente rinnovata${NC}"
echo -e "${BLUE}🔄 Input configuration ristrutturata${NC}"
echo -e "${BLUE}🔄 Animazioni con nuova sintassi${NC}"
echo -e "${BLUE}🔄 Opzioni deprecate rimosse${NC}"
echo -e "${BLUE}🔄 Window rules aggiornate${NC}"
echo ""

echo -e "${CYAN}💡 Se vedi ancora errori nel riquadro rosso:${NC}"
echo -e "${BLUE}1. Riavvia Hyprland: hyprctl dispatch exit${NC}"
echo -e "${BLUE}2. Controlla log: journalctl --user -u hyprland${NC}"
echo -e "${BLUE}3. Verifica sintassi: hyprctl reload${NC}"
echo ""

echo -e "${GREEN}🎯 CONFIGURAZIONE HYPRLAND 0.50.1 AGGIORNATA!${NC}"

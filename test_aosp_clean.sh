#!/bin/bash

echo "🧪 TEST AOSP PURO (senza Google Apps)"
echo "====================================="

~/Android/Sdk/emulator/emulator -avd "TEST_AOSP" -no-window -no-audio -no-snapshot-save &
EMULATOR_PID=$!

echo "⏳ Attesa avvio (45s)..."
sleep 45

DEVICE_ID=$(~/Android/Sdk/platform-tools/adb devices | grep emulator | head -1 | cut -f1)

if [ -n "$DEVICE_ID" ]; then
    echo "📱 Device: $DEVICE_ID"
    
    echo "=== VERIFICA GMAIL ==="
    GMAIL=$(~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" shell pm list packages | grep "com.google.android.gm" || echo "NOT_FOUND")
    if [ "$GMAIL" = "NOT_FOUND" ]; then
        echo "✅ Gmail: ASSENTE"
    else
        echo "❌ Gmail: PRESENTE - $GMAIL"
    fi
    
    echo "=== VERIFICA PLAY STORE ==="
    PLAYSTORE=$(~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" shell pm list packages | grep "com.android.vending" || echo "NOT_FOUND")
    if [ "$PLAYSTORE" = "NOT_FOUND" ]; then
        echo "✅ Play Store: ASSENTE"
    else
        echo "❌ Play Store: PRESENTE - $PLAYSTORE"
    fi
    
    echo "=== VERIFICA GOOGLE PLAY SERVICES ==="
    GPS=$(~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" shell pm list packages | grep "com.google.android.gms" || echo "NOT_FOUND")
    if [ "$GPS" = "NOT_FOUND" ]; then
        echo "✅ Google Play Services: ASSENTE (AOSP puro)"
    else
        echo "❌ Google Play Services: PRESENTE - $GPS"
    fi
    
    echo "=== VERIFICA SYSTEM IMAGE ==="
    echo "System Image usata:"
    cat ~/.config/.android/avd/TEST_AOSP.avd/config.ini | grep -E "(image\.sysdir|tag\.id)"
fi

echo "🔄 Chiusura emulatore..."
~/Android/Sdk/platform-tools/adb -s "$DEVICE_ID" emu kill 2>/dev/null
kill $EMULATOR_PID 2>/dev/null
sleep 3

echo "✅ Test completato!"

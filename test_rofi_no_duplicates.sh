#!/bin/bash

# Test finale rofi senza duplicati Android Studio

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🧪 TEST ROFI SENZA DUPLICATI${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${PURPLE}✅ DUPLICATO RIMOSSO CON SUCCESSO!${NC}"
echo ""

echo -e "${BLUE}📋 SITUAZIONE ATTUALE:${NC}"
echo -e "${GREEN}• File duplicato nascosto: /usr/share/applications/android-studio.desktop.hidden${NC}"
echo -e "${GREEN}• File attivo: ~/.local/share/applications/android-studio-16tb.desktop${NC}"
echo -e "${GREEN}• Backup rimossi: Pulizia completata${NC}"
echo ""

echo -e "${PURPLE}🔍 VERIFICA FILE DESKTOP:${NC}"
echo ""

echo -e "${BLUE}File .desktop Android Studio attivi:${NC}"
ACTIVE_FILES=$(find ~/.local/share/applications /usr/share/applications -name "*android*" -o -name "*studio*" 2>/dev/null | grep -v backup | grep -v hidden)

if [ -n "$ACTIVE_FILES" ]; then
    echo "$ACTIVE_FILES" | while read file; do
        if [ -f "$file" ]; then
            echo -e "${GREEN}✅ $file${NC}"
        fi
    done
else
    echo -e "${RED}❌ Nessun file trovato${NC}"
fi

echo ""

echo -e "${PURPLE}📋 CONTENUTO FILE ATTIVO:${NC}"
echo ""

DESKTOP_FILE="$HOME/.local/share/applications/android-studio-16tb.desktop"
if [ -f "$DESKTOP_FILE" ]; then
    echo -e "${GREEN}✅ File desktop attivo: $DESKTOP_FILE${NC}"
    echo -e "${BLUE}Contenuto:${NC}"
    cat "$DESKTOP_FILE"
else
    echo -e "${RED}❌ File desktop non trovato${NC}"
fi

echo ""

echo -e "${PURPLE}🚀 COME TESTARE ROFI:${NC}"
echo ""

echo -e "${YELLOW}Test manuale:${NC}"
echo -e "${BLUE}1. Premi Super (tasto Windows)${NC}"
echo -e "${BLUE}2. Digita 'android'${NC}"
echo -e "${BLUE}3. Dovrebbe apparire SOLO 'Android Studio'${NC}"
echo -e "${BLUE}4. Seleziona e premi Invio${NC}"
echo ""

echo -e "${YELLOW}Test da terminale:${NC}"
echo -e "${CYAN}android-studio${NC}"
echo -e "${CYAN}studio${NC}"
echo -e "${CYAN}as${NC}"
echo ""

echo -e "${PURPLE}✅ RISULTATO ATTESO:${NC}"
echo -e "${GREEN}• In rofi appare SOLO un 'Android Studio'${NC}"
echo -e "${GREEN}• Android Studio si avvia dal disco 16TB${NC}"
echo -e "${GREEN}• Variabili ambiente preconfigurate${NC}"
echo -e "${GREEN}• AVD Manager funziona senza errori${NC}"
echo ""

echo -e "${PURPLE}🎯 CONFIGURAZIONE FINALE:${NC}"
echo ""

echo -e "${BLUE}Launcher ottimizzato:${NC}"
LAUNCHER="/home/<USER>/optimix2/android-studio-launcher.sh"
if [ -f "$LAUNCHER" ]; then
    echo -e "${GREEN}✅ $LAUNCHER${NC}"
    echo -e "${BLUE}Variabili configurate:${NC}"
    grep "export" "$LAUNCHER" | while read line; do
        echo -e "${GREEN}  $line${NC}"
    done
else
    echo -e "${RED}❌ Launcher non trovato${NC}"
fi

echo ""

echo -e "${BLUE}Alias terminale:${NC}"
source ~/.bashrc 2>/dev/null
if alias android-studio &>/dev/null; then
    echo -e "${GREEN}✅ android-studio${NC}"
else
    echo -e "${RED}❌ android-studio${NC}"
fi

if alias studio &>/dev/null; then
    echo -e "${GREEN}✅ studio${NC}"
else
    echo -e "${RED}❌ studio${NC}"
fi

if alias as &>/dev/null; then
    echo -e "${GREEN}✅ as${NC}"
else
    echo -e "${RED}❌ as${NC}"
fi

echo ""

echo -e "${PURPLE}💾 SPAZIO DISCO:${NC}"
echo ""

ROOT_USAGE=$(df -h / | tail -1 | awk '{print $5}')
ROOT_AVAIL=$(df -h / | tail -1 | awk '{print $4}')
DISK_16TB_USAGE=$(df -h /home/<USER>/mnt/sdd2 | tail -1 | awk '{print $5}')
DISK_16TB_AVAIL=$(df -h /home/<USER>/mnt/sdd2 | tail -1 | awk '{print $4}')

echo -e "${GREEN}• Disco root: $ROOT_USAGE utilizzato, $ROOT_AVAIL disponibili${NC}"
echo -e "${GREEN}• Disco 16TB: $DISK_16TB_USAGE utilizzato, $DISK_16TB_AVAIL disponibili${NC}"

# Dimensione installazione Android
if [ -d "/home/<USER>/mnt/sdd2/Android" ]; then
    ANDROID_SIZE=$(du -sh /home/<USER>/mnt/sdd2/Android 2>/dev/null | cut -f1)
    echo -e "${GREEN}• Android completo: $ANDROID_SIZE${NC}"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}🎉 ROFI PULITO E FUNZIONANTE!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${PURPLE}🏆 RISULTATO FINALE:${NC}"
echo -e "${GREEN}✅ Duplicato Android Studio rimosso${NC}"
echo -e "${GREEN}✅ Solo un'icona Android Studio in rofi${NC}"
echo -e "${GREEN}✅ Launcher ottimizzato funzionante${NC}"
echo -e "${GREEN}✅ Installazione su disco 16TB${NC}"
echo -e "${GREEN}✅ Java configurato correttamente${NC}"
echo ""

echo -e "${YELLOW}🎮 ADESSO PROVA:${NC}"
echo -e "${BLUE}Super → 'android' → Invio${NC}"
echo -e "${BLUE}Dovrebbe aprirsi Android Studio senza problemi!${NC}"
echo ""

echo -e "${CYAN}✨ CONFIGURAZIONE PERFETTA COMPLETATA! ✨${NC}"

#!/bin/bash
# Test completo del sistema Android Studio + AVD + Rofi
# Verifica tutto prima della configurazione finale

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}TEST COMPLETO SISTEMA ANDROID STUDIO + AVD + ROFI${NC}"
echo "Sistema: i9-12900KF + RTX 4080 + Arch Linux + Hyprland"
echo "Data: $(date)"
echo "======================================================="
echo ""

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

# TEST 1: Verifica Android Studio
section "TEST 1: ANDROID STUDIO"
if command -v android-studio &> /dev/null; then
    log "Android Studio installato: $(which android-studio)"
    
    # Verifica versione
    STUDIO_VERSION=$(yay -Q android-studio 2>/dev/null | awk '{print $2}' || echo "Versione non rilevabile")
    info "Versione installata: $STUDIO_VERSION"
    
    # Verifica file desktop
    if [ -f /usr/share/applications/android-studio.desktop ]; then
        log "File .desktop presente per Rofi"
    else
        warn "File .desktop mancante"
    fi
else
    error "Android Studio non trovato"
    exit 1
fi
echo ""

# TEST 2: Verifica SDK
section "TEST 2: ANDROID SDK"
if [ -d "$ANDROID_HOME" ]; then
    log "SDK directory presente: $ANDROID_HOME"
    
    SDK_SIZE=$(du -sh "$ANDROID_HOME" 2>/dev/null | cut -f1)
    info "Dimensione SDK: $SDK_SIZE"
    
    # Verifica componenti essenziali
    COMPONENTS=(
        "emulator/emulator"
        "platform-tools/adb"
        "cmdline-tools/latest/bin/avdmanager"
        "cmdline-tools/latest/bin/sdkmanager"
    )
    
    for component in "${COMPONENTS[@]}"; do
        if [ -f "$ANDROID_HOME/$component" ]; then
            log "Componente presente: $component"
        else
            error "Componente mancante: $component"
        fi
    done
else
    error "SDK directory non trovata: $ANDROID_HOME"
    exit 1
fi
echo ""

# TEST 3: Verifica System Images
section "TEST 3: SYSTEM IMAGES"
if [ -d "$ANDROID_HOME/system-images" ]; then
    log "Directory system-images presente"
    
    info "System images disponibili:"
    find "$ANDROID_HOME/system-images" -name "system.img" | while read img; do
        api_level=$(echo "$img" | grep -o 'android-[0-9]*' | head -1)
        abi=$(echo "$img" | grep -o 'x86_64\|x86\|arm64-v8a' | head -1)
        echo "  ✓ $api_level ($abi)"
    done
else
    error "System images non trovate"
fi
echo ""

# TEST 4: Verifica AVD
section "TEST 4: AVD CONFIGURATION"
AVD_DIR="$HOME/.android/avd"
if [ -d "$AVD_DIR" ]; then
    log "Directory AVD presente: $AVD_DIR"
    
    AVD_COUNT=$(ls "$AVD_DIR"/*.ini 2>/dev/null | wc -l)
    info "AVD configurati: $AVD_COUNT"
    
    if [ "$AVD_COUNT" -gt 0 ]; then
        info "Lista AVD:"
        for ini_file in "$AVD_DIR"/*.ini; do
            if [ -f "$ini_file" ]; then
                avd_name=$(basename "$ini_file" .ini)
                avd_path=$(grep "path=" "$ini_file" | cut -d'=' -f2)
                
                if [ -d "$avd_path" ]; then
                    log "  $avd_name (OK)"
                    
                    # Verifica configurazione
                    config_file="$avd_path/config.ini"
                    if [ -f "$config_file" ]; then
                        ram_size=$(grep "hw.ramSize" "$config_file" | cut -d'=' -f2 | tr -d ' ')
                        cpu_cores=$(grep "hw.cpu.ncore" "$config_file" | cut -d'=' -f2 | tr -d ' ')
                        gpu_mode=$(grep "hw.gpu.mode" "$config_file" | cut -d'=' -f2 | tr -d ' ')
                        
                        info "    RAM: ${ram_size}MB, CPU: ${cpu_cores} cores, GPU: $gpu_mode"
                    fi
                else
                    error "  $avd_name (Path non valido: $avd_path)"
                fi
            fi
        done
    else
        error "Nessun AVD configurato"
    fi
else
    error "Directory AVD non trovata"
fi
echo ""

# TEST 5: Test Emulatore Command Line
section "TEST 5: EMULATOR COMMAND LINE"
if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    log "Emulatore disponibile"
    
    # Test lista AVD
    info "Test lista AVD:"
    EMULATOR_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null)
    if [ -n "$EMULATOR_AVDS" ]; then
        echo "$EMULATOR_AVDS" | while read avd; do
            if [ -n "$avd" ]; then
                log "  $avd"
            fi
        done
    else
        warn "Nessun AVD rilevato dall'emulatore"
    fi
    
    # Test accelerazione hardware
    info "Test accelerazione hardware:"
    ACCEL_CHECK=$($ANDROID_HOME/emulator/emulator -accel-check 2>&1)
    if echo "$ACCEL_CHECK" | grep -q "KVM"; then
        log "KVM disponibile"
    else
        warn "Problemi KVM: $ACCEL_CHECK"
    fi
else
    error "Emulatore non trovato"
fi
echo ""

# TEST 6: Test GPU e Sistema
section "TEST 6: GPU E SISTEMA"
# Test GPU NVIDIA
if command -v nvidia-smi &> /dev/null; then
    GPU_INFO=$(nvidia-smi --query-gpu=name,driver_version --format=csv,noheader,nounits 2>/dev/null | head -1)
    log "GPU rilevata: $GPU_INFO"
    
    # Verifica RTX 4080
    if echo "$GPU_INFO" | grep -q "RTX 4080"; then
        log "RTX 4080 confermata"
    else
        warn "GPU diversa da RTX 4080: $GPU_INFO"
    fi
else
    error "nvidia-smi non disponibile"
fi

# Test CPU
CPU_INFO=$(lscpu | grep "Model name" | cut -d':' -f2 | xargs)
info "CPU: $CPU_INFO"

# Test RAM
RAM_INFO=$(free -h | grep "Mem:" | awk '{print $2}')
info "RAM totale: $RAM_INFO"

# Test spazio disco
DISK_INFO=$(df -h "$ANDROID_HOME" | tail -1 | awk '{print $4}')
info "Spazio disponibile: $DISK_INFO"
echo ""

# TEST 7: Test Android Studio Launch
section "TEST 7: ANDROID STUDIO LAUNCH TEST"
info "Test avvio Android Studio (5 secondi)..."

# Avvia Android Studio in background
timeout 10s android-studio &
STUDIO_PID=$!
sleep 5

# Verifica se si è avviato
if ps -p $STUDIO_PID > /dev/null 2>&1; then
    log "Android Studio si avvia correttamente"
    kill $STUDIO_PID 2>/dev/null || true
    sleep 2
else
    warn "Problemi nell'avvio di Android Studio"
fi
echo ""

# TEST 8: Test Rofi Integration
section "TEST 8: ROFI INTEGRATION"
if command -v rofi &> /dev/null; then
    log "Rofi installato: $(rofi -version | head -1)"
    
    # Verifica che Android Studio sia visibile in rofi
    if rofi -show drun -dump | grep -q "Android Studio"; then
        log "Android Studio visibile in Rofi"
    else
        warn "Android Studio non visibile in Rofi"
    fi
else
    error "Rofi non installato"
fi
echo ""

# TEST 9: Creazione Script Rofi per AVD
section "TEST 9: SCRIPT ROFI PER AVD"
info "Creazione script Rofi per lancio diretto AVD..."

# Crea directory per script Rofi
mkdir -p /home/<USER>/.local/bin

# Script principale Rofi per AVD
cat > /home/<USER>/.local/bin/rofi-android-avd << 'EOF'
#!/bin/bash
# Script Rofi per selezione e lancio AVD

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

# Ottieni lista AVD
AVDS=($($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null))

if [ ${#AVDS[@]} -eq 0 ]; then
    notify-send "Android AVD" "Nessun AVD configurato" -i android
    exit 1
fi

# Crea menu per Rofi
AVD_MENU=""
for avd in "${AVDS[@]}"; do
    # Determina tipo e configurazione
    if [[ "$avd" == *"Gaming"* ]]; then
        icon="🎮"
        desc="Gaming"
    elif [[ "$avd" == *"Dev"* ]]; then
        icon="⚙️"
        desc="Development"
    elif [[ "$avd" == *"Test"* ]]; then
        icon="🧪"
        desc="Testing"
    else
        icon="📱"
        desc="Standard"
    fi
    
    AVD_MENU="$AVD_MENU$icon $avd ($desc)\n"
done

# Mostra menu Rofi
SELECTED=$(echo -e "$AVD_MENU" | rofi -dmenu -i -p "Seleziona AVD Android" -theme-str 'window {width: 600px;}')

if [ -n "$SELECTED" ]; then
    # Estrai nome AVD dalla selezione
    AVD_NAME=$(echo "$SELECTED" | sed 's/^[^ ]* //' | sed 's/ (.*)$//')
    
    # Notifica avvio
    notify-send "Android AVD" "Avvio $AVD_NAME..." -i android
    
    # Avvia AVD con configurazione ottimizzata
    if [[ "$AVD_NAME" == *"Gaming"* ]]; then
        # Configurazione gaming
        $ANDROID_HOME/emulator/emulator \
            -avd "$AVD_NAME" \
            -gpu host \
            -cores 8 \
            -memory 8192 \
            -netdelay none \
            -netspeed full \
            -qemu -enable-kvm &
    elif [[ "$AVD_NAME" == *"Dev"* ]]; then
        # Configurazione development
        $ANDROID_HOME/emulator/emulator \
            -avd "$AVD_NAME" \
            -gpu auto \
            -cores 4 \
            -memory 4096 \
            -netdelay none \
            -netspeed full &
    else
        # Configurazione standard
        $ANDROID_HOME/emulator/emulator \
            -avd "$AVD_NAME" \
            -gpu host \
            -cores 6 \
            -memory 6144 \
            -netdelay none \
            -netspeed full &
    fi
    
    # Notifica successo
    sleep 3
    notify-send "Android AVD" "$AVD_NAME avviato con successo!" -i android
fi
EOF

chmod +x /home/<USER>/.local/bin/rofi-android-avd
log "Script Rofi AVD creato: ~/.local/bin/rofi-android-avd"

# Crea file .desktop per Rofi
cat > /home/<USER>/.local/share/applications/android-avd-selector.desktop << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Android AVD Selector
Comment=Seleziona e avvia Android Virtual Device
Exec=/home/<USER>/.local/bin/rofi-android-avd
Icon=android
Terminal=false
Categories=Development;
Keywords=android;avd;emulator;rofi;
EOF

log "File .desktop creato per Rofi: android-avd-selector.desktop"

# Aggiorna database desktop
update-desktop-database ~/.local/share/applications 2>/dev/null || true
log "Database desktop aggiornato"
echo ""

# TEST 10: Test Script Rofi
section "TEST 10: TEST SCRIPT ROFI"
info "Test script Rofi AVD..."

# Test che lo script funzioni
if /home/<USER>/.local/bin/rofi-android-avd --help &>/dev/null || [ $? -eq 1 ]; then
    log "Script Rofi eseguibile"
else
    warn "Possibili problemi con script Rofi"
fi

# Verifica che sia nel PATH
if [ -d /home/<USER>/.local/bin ] && [[ ":$PATH:" == *":/home/<USER>/.local/bin:"* ]]; then
    log "Directory script nel PATH"
else
    warn "Aggiungi ~/.local/bin al PATH per accesso diretto"
fi
echo ""

# RIEPILOGO FINALE
section "RIEPILOGO FINALE"
echo ""
echo "SISTEMA ANDROID STUDIO:"
echo "✓ Android Studio: $(yay -Q android-studio 2>/dev/null | awk '{print $2}' || echo 'Installato')"
echo "✓ SDK Path: $ANDROID_HOME"
echo "✓ SDK Size: $(du -sh "$ANDROID_HOME" 2>/dev/null | cut -f1)"
echo ""

echo "AVD CONFIGURATI:"
for ini_file in "$HOME/.android/avd"/*.ini; do
    if [ -f "$ini_file" ]; then
        avd_name=$(basename "$ini_file" .ini)
        echo "✓ $avd_name"
    fi
done
echo ""

echo "HARDWARE OTTIMIZZATO:"
echo "✓ CPU: $CPU_INFO"
echo "✓ GPU: $(nvidia-smi --query-gpu=name --format=csv,noheader,nounits 2>/dev/null | head -1)"
echo "✓ RAM: $RAM_INFO"
echo "✓ Storage: $DISK_INFO disponibili"
echo ""

echo "INTEGRAZIONE ROFI:"
echo "✓ Script AVD: ~/.local/bin/rofi-android-avd"
echo "✓ Desktop Entry: android-avd-selector.desktop"
echo "✓ Comando Rofi: rofi-android-avd"
echo ""

echo "COMANDI RAPIDI:"
echo "• Rofi AVD: rofi-android-avd"
echo "• Android Studio: android-studio"
echo "• Lista AVD: \$ANDROID_HOME/emulator/emulator -list-avds"
echo "• Menu AVD: /home/<USER>/Android/Scripts/select_avd.sh"
echo ""

echo "======================================================="
echo -e "${GREEN}TEST COMPLETO TERMINATO!${NC}"
echo "======================================================="
echo ""
echo -e "${CYAN}TUTTO È CONFIGURATO E FUNZIONANTE!${NC}"
echo ""
echo "Per usare con Rofi:"
echo "1. Premi Super+Space (o il tuo shortcut Rofi)"
echo "2. Digita 'Android AVD Selector' o 'rofi-android-avd'"
echo "3. Seleziona l'AVD desiderato"
echo "4. L'emulatore si avvierà automaticamente ottimizzato"
echo ""
echo "Sistema pronto per l'uso professionale! 🚀"

# 🧹 COMANDI RIMOZIONE DUPLICATI ANDROID STUDIO

## 🎯 **ESEGUI QUESTI COMANDI NEL TERMINALE**

### 📱 **1. Rimozione installazione vecchia**
```bash
sudo rm -rf /opt/android-studio
```

### 📱 **2. Rimozione launcher vecchio**
```bash
sudo rm -f /usr/share/applications/android-studio.desktop
```

### 📱 **3. Rimozione symlink vecchio**
```bash
sudo rm -f /usr/bin/android-studio
```

### 📱 **4. Aggiornamento cache applicazioni**
```bash
update-desktop-database ~/.local/share/applications/
sudo update-desktop-database /usr/share/applications/
```

---

## 🔍 **VERIFICA RIMOZIONE COMPLETATA**

### ✅ **Controlla che siano stati rimossi:**
```bash
# Questi comandi NON devono mostrare nulla
ls -la /opt/android-studio 2>/dev/null || echo "✅ /opt/android-studio RIMOSSO"
ls -la /usr/share/applications/android-studio.desktop 2>/dev/null || echo "✅ Launcher vecchio RIMOSSO"
ls -la /usr/bin/android-studio 2>/dev/null || echo "✅ Symlink vecchio RIMOSSO"
```

### ✅ **Controlla che siano rimasti (DEVONO esserci):**
```bash
# Questi comandi DEVONO mostrare i file
ls -la ~/android-studio-2025/bin/studio
ls -la ~/.local/share/applications/android-studio-launcher.desktop
```

---

## 🎉 **RISULTATO FINALE ATTESO**

Dopo aver eseguito tutti i comandi:

### ✅ **RIMOSSI (duplicati):**
- ❌ `/opt/android-studio/` 
- ❌ `/usr/share/applications/android-studio.desktop`
- ❌ `/usr/bin/android-studio`

### ✅ **MANTENUTI (corretti):**
- ✅ `~/android-studio-2025/` (installazione corretta)
- ✅ `~/.local/share/applications/android-studio-launcher.desktop` (launcher corretto)

### 🚀 **ROFI PULITO:**
- Solo "Android Studio 2025" apparirà in rofi
- Lancerà sempre la versione corretta
- 31 emulatori ottimizzati disponibili
- Nessuna confusione tra versioni

---

## 📧 **PROSSIMI PASSI**

1. **Esegui i comandi sopra** per rimuovere i duplicati
2. **Testa rofi**: cerca "Android Studio 2025"
3. **Avvia Android Studio** e verifica i 31 emulatori
4. **Se necessario**: esegui `./rimuovi_gmail_automatico_2025.sh`

---

**🎯 Una volta completata la rimozione, avrai un sistema Android Studio 2025 completamente pulito e ottimizzato!**

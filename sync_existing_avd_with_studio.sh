#!/bin/bash
# Sincronizza AVD esistenti con Android Studio senza ricrearli

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== SINCRONIZZAZIONE AVD CON ANDROID STUDIO ==="
echo "Rende visibili gli AVD esistenti senza ricrearli"
echo "Data: $(date)"
echo "==============================================="
echo ""

# Termina Android Studio
info "Terminazione Android Studio..."
pkill -f android-studio 2>/dev/null || true
sleep 2

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
export ANDROID_HOME=/home/<USER>/Android/Sdk

# Verifica AVD esistenti
info "Verifica AVD esistenti..."
if [ -d ~/.android/avd ]; then
    AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
    log "Trovati $AVD_COUNT AVD esistenti"
    
    echo "AVD disponibili:"
    for ini_file in ~/.android/avd/*.ini; do
        if [ -f "$ini_file" ]; then
            avd_name=$(basename "$ini_file" .ini)
            echo "  - $avd_name"
        fi
    done
else
    error "Directory AVD non trovata"
    exit 1
fi
echo ""

# Fix 1: Correggi file .ini per Android Studio
info "Correzione file .ini per compatibilità Android Studio..."
for ini_file in ~/.android/avd/*.ini; do
    if [ -f "$ini_file" ]; then
        avd_name=$(basename "$ini_file" .ini)
        avd_path="$HOME/.android/avd/$avd_name.avd"
        
        # Ricrea file .ini con formato corretto per Android Studio
        cat > "$ini_file" << EOF
avd.ini.encoding=UTF-8
path=$avd_path
path.rel=avd/$avd_name.avd
target=android-34
EOF
        log "File .ini corretto: $avd_name"
    fi
done

# Fix 2: Aggiorna config.ini per ogni AVD
info "Aggiornamento config.ini per Android Studio..."
for avd_dir in ~/.android/avd/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir" .avd)
        config_file="$avd_dir/config.ini"
        
        if [ -f "$config_file" ]; then
            # Backup config originale
            cp "$config_file" "$config_file.backup"
            
            # Aggiungi/correggi parametri essenziali per Android Studio
            if ! grep -q "^AvdId=" "$config_file"; then
                echo "AvdId=$avd_name" >> "$config_file"
            fi
            
            if ! grep -q "^avd.ini.displayname=" "$config_file"; then
                echo "avd.ini.displayname=$avd_name" >> "$config_file"
            fi
            
            if ! grep -q "^PlayStore.enabled=" "$config_file"; then
                echo "PlayStore.enabled=false" >> "$config_file"
            fi
            
            if ! grep -q "^tag.id=" "$config_file"; then
                echo "tag.id=google_apis" >> "$config_file"
            fi
            
            if ! grep -q "^tag.display=" "$config_file"; then
                echo "tag.display=Google APIs" >> "$config_file"
            fi
            
            # Assicurati che il target sia corretto
            if ! grep -q "^target=" "$config_file"; then
                if [[ "$avd_name" == *"Android13"* ]]; then
                    echo "target=android-33" >> "$config_file"
                elif [[ "$avd_name" == *"Android15"* ]]; then
                    echo "target=android-35" >> "$config_file"
                else
                    echo "target=android-34" >> "$config_file"
                fi
            fi
            
            log "Config.ini aggiornato: $avd_name"
        else
            error "Config.ini mancante per $avd_name"
        fi
    fi
done

# Fix 3: Crea/aggiorna file hardware.ini se necessario
info "Verifica file hardware.ini..."
for avd_dir in ~/.android/avd/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir" .avd)
        hardware_file="$avd_dir/hardware-qemu.ini"
        
        if [ ! -f "$hardware_file" ]; then
            # Crea hardware.ini base se mancante
            cat > "$hardware_file" << EOF
# Hardware configuration for $avd_name
kernel.path = 
kernel.parameters = 
kernel.newDeviceNaming = autodetect
kernel.supportsYUV420888toNV21 = autodetect
disk.ramdisk.path = 
disk.systemPartition.initPath = 
disk.systemPartition.size = 4096m
disk.dataPartition.path = userdata-qemu.img
disk.dataPartition.size = 6442450944
disk.cachePartition.path = cache.img
disk.cachePartition.size = 66m
EOF
            log "Hardware.ini creato per $avd_name"
        fi
    fi
done

# Fix 4: Pulisci cache Android Studio specifica per AVD
info "Pulizia cache Android Studio per AVD..."
rm -rf ~/.cache/Google/AndroidStudio*/system/caches/device-art-resources 2>/dev/null || true
rm -rf ~/.cache/Google/AndroidStudio*/system/caches/device-explorer 2>/dev/null || true
rm -rf ~/.cache/Google/AndroidStudio*/system/caches/external_build_system 2>/dev/null || true

# Fix 5: Forza refresh configurazione Android Studio
info "Refresh configurazione Android Studio..."
for studio_config in ~/.config/Google/AndroidStudio*; do
    if [ -d "$studio_config" ]; then
        # Rimuovi cache AVD specifica
        rm -rf "$studio_config/options/deviceManagerPhysicalTab.xml" 2>/dev/null || true
        rm -rf "$studio_config/options/deviceManagerVirtualTab.xml" 2>/dev/null || true
        
        # Forza refresh delle impostazioni AVD
        if [ -f "$studio_config/options/ide.general.xml" ]; then
            # Backup e modifica per forzare refresh
            cp "$studio_config/options/ide.general.xml" "$studio_config/options/ide.general.xml.backup"
        fi
        
        log "Configurazione Android Studio aggiornata"
    fi
done

# Fix 6: Test finale
info "Test sincronizzazione..."
EMULATOR_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null)
if [ -n "$EMULATOR_AVDS" ]; then
    log "AVD rilevati dall'emulatore:"
    echo "$EMULATOR_AVDS" | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
else
    error "Nessun AVD rilevato dall'emulatore"
fi

# Crea script di avvio Android Studio con refresh forzato
info "Creazione script avvio con refresh..."
cat > /home/<USER>/start_android_studio_synced.sh << 'EOF'
#!/bin/bash
# Avvia Android Studio con AVD sincronizzati

echo "=== ANDROID STUDIO - AVD SINCRONIZZATI ==="
echo "Gli AVD esistenti dovrebbero essere visibili"
echo "Data: $(date)"
echo "=========================================="

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
unset _JAVA_OPTIONS

# Imposta variabili corrette
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

echo "Ambiente configurato:"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "AVD Directory: ~/.android/avd"
echo ""

echo "AVD sincronizzati:"
$ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null | while read avd; do
    if [ -n "$avd" ]; then
        echo "  ✓ $avd"
    fi
done

echo ""
echo "Avvio Android Studio..."
echo ""
echo "ISTRUZIONI:"
echo "1. Vai su More Actions → Virtual Device Manager"
echo "2. Dovresti vedere tutti gli AVD sincronizzati"
echo "3. Se non li vedi:"
echo "   - Clicca su 'Refresh' (icona refresh)"
echo "   - Oppure File → Invalidate Caches and Restart"
echo "4. Se vedi 'Medium Phone API 36.0', ignoralo o eliminalo"
echo ""
echo "=========================================="

# Avvia Android Studio
android-studio &

echo "Android Studio avviato!"
echo "Controlla il Virtual Device Manager per vedere gli AVD."
EOF

chmod +x /home/<USER>/start_android_studio_synced.sh
log "Script creato: start_android_studio_synced.sh"

echo ""
echo "==============================================="
echo -e "${GREEN}SINCRONIZZAZIONE COMPLETATA!${NC}"
echo "==============================================="
echo ""
echo "MODIFICHE APPLICATE:"
echo "✓ File .ini corretti per Android Studio"
echo "✓ Config.ini aggiornati con parametri essenziali"
echo "✓ File hardware.ini verificati/creati"
echo "✓ Cache Android Studio pulita"
echo "✓ Configurazione Android Studio aggiornata"
echo ""
echo "PROSSIMI PASSI:"
echo ""
echo "1. Avvia Android Studio sincronizzato:"
echo "   ${BLUE}./start_android_studio_synced.sh${NC}"
echo ""
echo "2. In Android Studio:"
echo "   - More Actions → Virtual Device Manager"
echo "   - Dovresti vedere gli AVD esistenti"
echo "   - Se non li vedi, clicca 'Refresh'"
echo ""
echo "3. Se ancora non funziona:"
echo "   - File → Invalidate Caches and Restart"
echo "   - Riprova ad aprire Virtual Device Manager"
echo ""

AVD_FINAL_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
echo "AVD sincronizzati: $AVD_FINAL_COUNT"
echo ""
echo "Se gli AVD sono ancora non visibili, potrebbe essere necessario"
echo "ricrearli tramite Android Studio GUI, ma questa sincronizzazione"
echo "dovrebbe funzionare nella maggior parte dei casi."
echo ""
echo -e "${BLUE}Prova ora ad avviare Android Studio!${NC}"

#!/bin/bash
# Fix per la notifica _JAVA_OPTIONS in Android Studio

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== FIX NOTIFICA _JAVA_OPTIONS ANDROID STUDIO ==="
echo "Risoluzione warning Java options environment variables"
echo "Data: $(date)"
echo "=================================================="
echo ""

# Termina Android Studio se in esecuzione
info "Terminazione Android Studio..."
pkill -f android-studio 2>/dev/null || true
sleep 2
log "Android Studio terminato"

# Step 1: Rimuovi _JAVA_OPTIONS da .bashrc
info "Rimozione _JAVA_OPTIONS da .bashrc..."

if grep -q "_JAVA_OPTIONS" ~/.bashrc; then
    # Commenta la riga invece di rimuoverla
    sed -i 's/^export _JAVA_OPTIONS/#export _JAVA_OPTIONS/' ~/.bashrc
    log "_JAVA_OPTIONS commentato in .bashrc"
else
    log "_JAVA_OPTIONS non trovato in .bashrc"
fi

# Step 2: Rimuovi la variabile dalla sessione corrente
info "Rimozione _JAVA_OPTIONS dalla sessione corrente..."
unset _JAVA_OPTIONS
log "Variabile _JAVA_OPTIONS rimossa dalla sessione"

# Step 3: Crea configurazione ottimizzata per Android Studio
info "Creazione configurazione ottimizzata Android Studio..."

# Trova la directory di configurazione di Android Studio
STUDIO_CONFIG_DIR=""
for dir in ~/.config/Google/AndroidStudio*; do
    if [ -d "$dir" ]; then
        STUDIO_CONFIG_DIR="$dir"
        break
    fi
done

if [ -z "$STUDIO_CONFIG_DIR" ]; then
    # Se non esiste, crea la directory per la versione corrente
    STUDIO_VERSION=$(yay -Q android-studio 2>/dev/null | awk '{print $2}' | cut -d'-' -f1 || echo "2025.1")
    STUDIO_CONFIG_DIR="$HOME/.config/Google/AndroidStudio$STUDIO_VERSION"
    mkdir -p "$STUDIO_CONFIG_DIR"
    log "Creata directory configurazione: $STUDIO_CONFIG_DIR"
else
    log "Directory configurazione trovata: $STUDIO_CONFIG_DIR"
fi

# Crea file studio.vmoptions personalizzato
VMOPTIONS_FILE="$STUDIO_CONFIG_DIR/studio.vmoptions"
info "Creazione file vmoptions ottimizzato..."

cat > "$VMOPTIONS_FILE" << 'EOF'
# Configurazione JVM ottimizzata per Android Studio
# Sistema: i9-12900KF + RTX 4080 + 32GB RAM

# Memoria heap (8GB per il tuo sistema)
-Xms2g
-Xmx8g

# Garbage Collector ottimizzato
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UnlockExperimentalVMOptions
-XX:G1NewSizePercent=20
-XX:G1ReservePercent=20
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=32m

# Ottimizzazioni performance
-XX:+UseStringDeduplication
-XX:+OptimizeStringConcat
-XX:+UseCompressedOops
-XX:+UseCompressedClassPointers

# Ottimizzazioni per IDE
-Dsun.io.useCanonPrefixCache=false
-Djdk.http.auth.tunneling.disabledSchemes=""
-XX:+HeapDumpOnOutOfMemoryError
-XX:-OmitStackTraceInFastThrow

# Ottimizzazioni per sistema Linux
-Djb.vmOptionsFile="$VMOPTIONS_FILE"
-Djava.system.class.loader=com.intellij.util.lang.PathClassLoader
-Xverify:none

# Ottimizzazioni per Hyprland/Wayland
-Dawt.useSystemAAFontSettings=lcd
-Dswing.aatext=true
-Dsun.java2d.renderer=sun.java2d.marlin.MarlinRenderingEngine

# Disabilita JetBrains Runtime warnings
-Djb.privacy.policy.text="<!--999.999-->"
-Djb.consents.confirmation.enabled=false
EOF

log "File vmoptions creato: $VMOPTIONS_FILE"

# Step 4: Crea script di avvio Android Studio senza _JAVA_OPTIONS
info "Creazione script avvio Android Studio pulito..."

cat > /home/<USER>/start_android_studio_clean_final.sh << 'EOF'
#!/bin/bash
# Avvia Android Studio senza _JAVA_OPTIONS

echo "=== ANDROID STUDIO AVVIO PULITO ==="
echo "Senza variabili Java environment problematiche"
echo "Data: $(date)"
echo "===================================="

# Rimuovi variabili Java problematiche
unset _JAVA_OPTIONS
unset JAVA_OPTS
unset JVM_OPTS

# Pulisci anche variabili AVD problematiche
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME

# Imposta solo variabili essenziali Android
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/emulator:$ANDROID_HOME/platform-tools

echo "Ambiente configurato:"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "_JAVA_OPTIONS: (rimosso)"
echo "Configurazione JVM: tramite studio.vmoptions"
echo ""

echo "AVD disponibili:"
if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    $ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
else
    echo "  Emulatore non trovato"
fi

echo ""
echo "Avvio Android Studio..."
echo "La configurazione JVM è ora gestita tramite studio.vmoptions"
echo "Non dovrebbero più apparire warning su _JAVA_OPTIONS"
echo "===================================="

# Avvia Android Studio
android-studio &

echo ""
echo "Android Studio avviato!"
echo "Per vedere gli AVD:"
echo "1. Tools → AVD Manager"
echo "2. Se non li vedi, clicca 'Refresh'"
echo "3. Verifica SDK Location in File → Settings → System Settings → Android SDK"
EOF

chmod +x /home/<USER>/start_android_studio_clean_final.sh
log "Script avvio pulito creato: start_android_studio_clean_final.sh"

# Step 5: Verifica configurazione
info "Verifica configurazione finale..."

echo "Configurazione JVM Android Studio:"
echo "File: $VMOPTIONS_FILE"
if [ -f "$VMOPTIONS_FILE" ]; then
    echo "Dimensione: $(wc -l < "$VMOPTIONS_FILE") righe"
    log "File vmoptions presente e configurato"
else
    error "File vmoptions non creato correttamente"
fi

echo ""
echo "Variabili ambiente attuali:"
echo "_JAVA_OPTIONS: ${_JAVA_OPTIONS:-'(non impostato)'}"
echo "ANDROID_HOME: ${ANDROID_HOME:-'(non impostato)'}"

# Step 6: Test avvio
info "Test configurazione..."

# Verifica che Android Studio possa avviarsi
if command -v android-studio &> /dev/null; then
    log "Android Studio disponibile"
else
    error "Android Studio non trovato nel PATH"
fi

# Verifica AVD
AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
if [ "$AVD_COUNT" -gt 0 ]; then
    log "AVD configurati: $AVD_COUNT"
else
    warn "Nessun AVD trovato"
fi

echo ""
echo "=================================================="
echo -e "${GREEN}FIX COMPLETATO!${NC}"
echo "=================================================="
echo ""
echo "MODIFICHE APPLICATE:"
echo "✓ _JAVA_OPTIONS rimosso da .bashrc"
echo "✓ Variabile _JAVA_OPTIONS rimossa dalla sessione"
echo "✓ File studio.vmoptions creato con configurazione ottimizzata"
echo "✓ Script avvio pulito creato"
echo ""
echo "PROSSIMI PASSI:"
echo ""
echo "1. Riavvia il terminale o esegui: source ~/.bashrc"
echo ""
echo "2. Avvia Android Studio con configurazione pulita:"
echo "   ./start_android_studio_clean_final.sh"
echo ""
echo "3. Verifica che non appaia più il warning _JAVA_OPTIONS"
echo ""
echo "4. In Android Studio:"
echo "   - Tools → AVD Manager per vedere gli AVD"
echo "   - File → Settings → System Settings → Android SDK per verificare SDK"
echo ""
echo "CONFIGURAZIONE JVM:"
echo "- Memoria heap: 8GB (ottimizzata per il tuo sistema)"
echo "- Garbage Collector: G1GC con ottimizzazioni"
echo "- Configurazione tramite: $VMOPTIONS_FILE"
echo ""

if [ -f "$VMOPTIONS_FILE" ]; then
    echo -e "${GREEN}Il warning _JAVA_OPTIONS non dovrebbe più apparire!${NC}"
else
    echo -e "${RED}Problema nella creazione del file vmoptions${NC}"
fi

echo ""
echo "Per verificare:"
echo "1. Avvia: ./start_android_studio_clean_final.sh"
echo "2. Controlla che non ci siano warning Java options"
echo "3. Vai su Tools → AVD Manager per vedere gli AVD"

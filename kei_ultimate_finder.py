#!/usr/bin/env python3
"""
KEI ULTIMATE FINDER - State-of-the-Art Automatic Content Discovery
Trova automaticamente tutti i video/immagini di Kei Urana per analisi completa
Versione: 2025 Ultimate Automation
"""

import requests
import json
import os
import sys
import time
import re
from pathlib import Path
from urllib.parse import quote, urljoin
import subprocess
from bs4 import BeautifulSoup
import yt_dlp
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("⚠️ Selenium non disponibile - alcune funzioni limitate")
import cv2
import numpy as np

class KeiUltimateFinder:
    def __init__(self):
        self.search_terms = [
            "Kei Urana",
            "浦名恵",
            "Gachiakuta author",
            "Gachiakuta mangaka",
            "<PERSON><PERSON> interview",
            "Kei Urana Lucca Comics",
            "<PERSON><PERSON> appearance",
            "ガチアクタ 作者",
            "浦名恵 インタビュー"
        ]

        self.sources = {
            'youtube': 'https://www.youtube.com/results?search_query=',
            'twitter': 'https://twitter.com/search?q=',
            'instagram': 'https://www.instagram.com/explore/tags/',
            'tiktok': 'https://www.tiktok.com/search?q=',
            'bilibili': 'https://search.bilibili.com/all?keyword=',
            'niconico': 'https://www.nicovideo.jp/search/',
            'pixiv': 'https://www.pixiv.net/en/tags/',
            'reddit': 'https://www.reddit.com/search/?q=',
            'tumblr': 'https://www.tumblr.com/search/',
            'pinterest': 'https://www.pinterest.com/search/pins/?q='
        }

        self.manga_sources = [
            'https://kodansha.co.jp',
            'https://pocket.shonenmagazine.com',
            'https://comic-days.com',
            'https://mangaplus.shueisha.co.jp'
        ]

        self.convention_sources = [
            'Lucca Comics',
            'Comiket',
            'AnimeJapan',
            'Jump Festa',
            'Tokyo Comic Con'
        ]

        self.results = {
            'videos': [],
            'images': [],
            'interviews': [],
            'social_media': [],
            'conventions': [],
            'official_content': []
        }

        self.download_dir = Path('kei_content_collection')
        self.download_dir.mkdir(exist_ok=True)

    def setup_selenium_driver(self):
        """Setup Selenium WebDriver per scraping avanzato"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36')

            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Selenium WebDriver inizializzato")
            return True
        except Exception as e:
            print(f"⚠️ Selenium non disponibile: {e}")
            self.driver = None
            return False

    def search_youtube_content(self):
        """Cerca contenuti YouTube di Kei Urana"""
        print("🔍 Ricerca contenuti YouTube...")

        youtube_results = []

        for term in self.search_terms:
            try:
                # Usa yt-dlp per ricerca avanzata
                search_url = f"ytsearch20:{term}"

                ydl_opts = {
                    'quiet': True,
                    'no_warnings': True,
                    'extract_flat': True,
                }

                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    search_results = ydl.extract_info(search_url, download=False)

                    if 'entries' in search_results:
                        for entry in search_results['entries']:
                            if entry and 'id' in entry:
                                video_info = {
                                    'id': entry['id'],
                                    'title': entry.get('title', ''),
                                    'url': f"https://www.youtube.com/watch?v={entry['id']}",
                                    'uploader': entry.get('uploader', ''),
                                    'duration': entry.get('duration', 0),
                                    'view_count': entry.get('view_count', 0),
                                    'upload_date': entry.get('upload_date', ''),
                                    'search_term': term,
                                    'relevance_score': self.calculate_relevance(entry.get('title', ''), term)
                                }

                                # Filtra per rilevanza
                                if video_info['relevance_score'] > 0.3:
                                    youtube_results.append(video_info)

                time.sleep(1)  # Rate limiting

            except Exception as e:
                print(f"⚠️ Errore ricerca YouTube per '{term}': {e}")
                continue

        # Ordina per rilevanza e rimuovi duplicati
        unique_videos = {}
        for video in youtube_results:
            if video['id'] not in unique_videos:
                unique_videos[video['id']] = video

        sorted_videos = sorted(unique_videos.values(),
                             key=lambda x: x['relevance_score'], reverse=True)

        self.results['videos'].extend(sorted_videos[:50])  # Top 50 più rilevanti
        print(f"📹 Trovati {len(sorted_videos)} video YouTube")

        return True

    def search_social_media(self):
        """Cerca contenuti social media"""
        print("📱 Ricerca contenuti social media...")

        if not self.driver:
            print("⚠️ Selenium non disponibile, skip social media")
            return False

        social_results = []

        for platform, base_url in self.sources.items():
            if platform in ['youtube']:  # Skip YouTube (già fatto)
                continue

            for term in self.search_terms[:3]:  # Limita per rate limiting
                try:
                    search_url = base_url + quote(term)

                    self.driver.get(search_url)
                    time.sleep(3)  # Attendi caricamento

                    # Estrai contenuti specifici per piattaforma
                    if platform == 'twitter':
                        posts = self.extract_twitter_posts()
                    elif platform == 'instagram':
                        posts = self.extract_instagram_posts()
                    elif platform == 'tiktok':
                        posts = self.extract_tiktok_posts()
                    else:
                        posts = self.extract_generic_posts()

                    for post in posts:
                        post['platform'] = platform
                        post['search_term'] = term
                        post['relevance_score'] = self.calculate_relevance(
                            post.get('text', ''), term)

                        if post['relevance_score'] > 0.2:
                            social_results.append(post)

                    time.sleep(2)  # Rate limiting

                except Exception as e:
                    print(f"⚠️ Errore {platform} per '{term}': {e}")
                    continue

        self.results['social_media'].extend(social_results)
        print(f"📱 Trovati {len(social_results)} post social media")

        return True

    def search_official_sources(self):
        """Cerca fonti ufficiali (editori, siti manga)"""
        print("🏢 Ricerca fonti ufficiali...")

        official_results = []

        for source_url in self.manga_sources:
            try:
                # Cerca pagine Gachiakuta/Kei Urana
                search_terms = ['gachiakuta', 'ガチアクタ', '浦名恵']

                for term in search_terms:
                    search_url = f"{source_url}/search?q={quote(term)}"

                    response = requests.get(search_url,
                                          headers={'User-Agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36'},
                                          timeout=10)

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')

                        # Estrai link rilevanti
                        links = soup.find_all('a', href=True)

                        for link in links:
                            href = link.get('href')
                            text = link.get_text(strip=True)

                            if any(keyword in text.lower() for keyword in ['gachiakuta', 'kei urana', '浦名恵']):
                                full_url = urljoin(source_url, href)

                                official_results.append({
                                    'url': full_url,
                                    'title': text,
                                    'source': source_url,
                                    'type': 'official_page',
                                    'relevance_score': self.calculate_relevance(text, term)
                                })

                time.sleep(1)

            except Exception as e:
                print(f"⚠️ Errore fonte ufficiale {source_url}: {e}")
                continue

        self.results['official_content'].extend(official_results)
        print(f"🏢 Trovate {len(official_results)} fonti ufficiali")

        return True

    def search_convention_content(self):
        """Cerca contenuti da convention/eventi"""
        print("🎪 Ricerca contenuti convention...")

        convention_results = []

        for convention in self.convention_sources:
            for term in ['Kei Urana', 'Gachiakuta']:
                search_query = f"{convention} {term}"

                try:
                    # Ricerca Google per contenuti convention
                    google_results = self.google_search(search_query)

                    for result in google_results:
                        if any(keyword in result['title'].lower()
                              for keyword in ['interview', 'panel', 'appearance', 'インタビュー']):

                            convention_results.append({
                                'url': result['url'],
                                'title': result['title'],
                                'description': result.get('description', ''),
                                'convention': convention,
                                'type': 'convention_content',
                                'relevance_score': self.calculate_relevance(result['title'], term)
                            })

                    time.sleep(1)

                except Exception as e:
                    print(f"⚠️ Errore convention {convention}: {e}")
                    continue

        self.results['conventions'].extend(convention_results)
        print(f"🎪 Trovati {len(convention_results)} contenuti convention")

        return True

    def calculate_relevance(self, text, search_term):
        """Calcola score di rilevanza per un contenuto"""
        if not text:
            return 0.0

        text_lower = text.lower()
        term_lower = search_term.lower()

        score = 0.0

        # Exact match
        if term_lower in text_lower:
            score += 1.0

        # Keyword matching
        keywords = ['kei urana', 'gachiakuta', '浦名恵', 'ガチアクタ', 'mangaka', 'author', 'interview']
        for keyword in keywords:
            if keyword in text_lower:
                score += 0.3

        # Context keywords
        context_keywords = ['manga', 'comic', 'artist', 'creator', 'lucca', 'convention']
        for keyword in context_keywords:
            if keyword in text_lower:
                score += 0.1

        return min(score, 2.0)  # Cap at 2.0

    def google_search(self, query, num_results=10):
        """Ricerca Google (usando API o scraping)"""
        try:
            # Usa requests per ricerca Google
            search_url = f"https://www.google.com/search?q={quote(query)}&num={num_results}"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36'
            }

            response = requests.get(search_url, headers=headers, timeout=10)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                results = []

                # Estrai risultati Google
                for result in soup.find_all('div', class_='g'):
                    title_elem = result.find('h3')
                    link_elem = result.find('a')
                    desc_elem = result.find('span', class_='st')

                    if title_elem and link_elem:
                        results.append({
                            'title': title_elem.get_text(strip=True),
                            'url': link_elem.get('href'),
                            'description': desc_elem.get_text(strip=True) if desc_elem else ''
                        })

                return results[:num_results]

        except Exception as e:
            print(f"⚠️ Errore Google search: {e}")

        return []

    def extract_twitter_posts(self):
        """Estrai post Twitter"""
        posts = []
        try:
            tweet_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-testid="tweet"]')

            for tweet in tweet_elements[:10]:  # Limita a 10
                try:
                    text_elem = tweet.find_element(By.CSS_SELECTOR, '[data-testid="tweetText"]')
                    text = text_elem.text if text_elem else ''

                    # Cerca immagini/video
                    media_elems = tweet.find_elements(By.CSS_SELECTOR, 'img, video')
                    media_urls = [elem.get_attribute('src') for elem in media_elems if elem.get_attribute('src')]

                    posts.append({
                        'text': text,
                        'media_urls': media_urls,
                        'type': 'tweet'
                    })

                except:
                    continue

        except Exception as e:
            print(f"⚠️ Errore estrazione Twitter: {e}")

        return posts

    def extract_instagram_posts(self):
        """Estrai post Instagram"""
        posts = []
        try:
            post_elements = self.driver.find_elements(By.CSS_SELECTOR, 'article')

            for post in post_elements[:10]:
                try:
                    # Cerca immagini
                    img_elems = post.find_elements(By.CSS_SELECTOR, 'img')
                    img_urls = [img.get_attribute('src') for img in img_elems if img.get_attribute('src')]

                    # Cerca caption
                    caption_elem = post.find_element(By.CSS_SELECTOR, '[role="button"] span')
                    caption = caption_elem.text if caption_elem else ''

                    posts.append({
                        'text': caption,
                        'media_urls': img_urls,
                        'type': 'instagram_post'
                    })

                except:
                    continue

        except Exception as e:
            print(f"⚠️ Errore estrazione Instagram: {e}")

        return posts

    def extract_tiktok_posts(self):
        """Estrai post TikTok"""
        posts = []
        try:
            video_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-e2e="recommend-list-item-container"]')

            for video in video_elements[:10]:
                try:
                    # Cerca video URL
                    video_elem = video.find_element(By.CSS_SELECTOR, 'video')
                    video_url = video_elem.get_attribute('src') if video_elem else ''

                    # Cerca descrizione
                    desc_elem = video.find_element(By.CSS_SELECTOR, '[data-e2e="browse-video-desc"]')
                    description = desc_elem.text if desc_elem else ''

                    posts.append({
                        'text': description,
                        'media_urls': [video_url] if video_url else [],
                        'type': 'tiktok_video'
                    })

                except:
                    continue

        except Exception as e:
            print(f"⚠️ Errore estrazione TikTok: {e}")

        return posts

    def extract_generic_posts(self):
        """Estrai post generici"""
        posts = []
        try:
            # Cerca elementi con immagini/video
            media_elements = self.driver.find_elements(By.CSS_SELECTOR, 'img, video')

            for media in media_elements[:5]:
                try:
                    src = media.get_attribute('src')
                    alt = media.get_attribute('alt') or ''

                    if src and any(keyword in alt.lower() for keyword in ['kei', 'urana', 'gachiakuta']):
                        posts.append({
                            'text': alt,
                            'media_urls': [src],
                            'type': 'generic_media'
                        })

                except:
                    continue

        except Exception as e:
            print(f"⚠️ Errore estrazione generica: {e}")

        return posts

    def download_priority_content(self):
        """Scarica contenuti ad alta priorità"""
        print("⬇️ Download contenuti prioritari...")

        # Ordina tutti i contenuti per rilevanza
        all_content = []

        # Video YouTube
        for video in self.results['videos']:
            video['priority'] = video['relevance_score'] * 2  # Video hanno priorità alta
            all_content.append(video)

        # Social media con media
        for post in self.results['social_media']:
            if post.get('media_urls'):
                post['priority'] = post['relevance_score'] * 1.5
                all_content.append(post)

        # Ordina per priorità
        all_content.sort(key=lambda x: x.get('priority', 0), reverse=True)

        downloaded = 0
        max_downloads = 20  # Limita download

        for content in all_content[:max_downloads]:
            try:
                if content.get('url') and 'youtube.com' in content['url']:
                    # Download video YouTube
                    self.download_youtube_video(content)
                    downloaded += 1

                elif content.get('media_urls'):
                    # Download immagini/media
                    for media_url in content['media_urls'][:3]:  # Max 3 per post
                        if self.download_media_file(media_url, content):
                            downloaded += 1

                if downloaded >= max_downloads:
                    break

                time.sleep(1)  # Rate limiting

            except Exception as e:
                print(f"⚠️ Errore download: {e}")
                continue

        print(f"⬇️ Scaricati {downloaded} file")
        return True

    def download_youtube_video(self, video_info):
        """Scarica video YouTube"""
        try:
            ydl_opts = {
                'format': 'best[height<=720]',  # Qualità media per spazio
                'outtmpl': str(self.download_dir / f"video_{video_info['id']}.%(ext)s"),
                'quiet': True,
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([video_info['url']])

            print(f"📹 Scaricato: {video_info['title'][:50]}...")
            return True

        except Exception as e:
            print(f"⚠️ Errore download video {video_info['id']}: {e}")
            return False

    def download_media_file(self, url, content_info):
        """Scarica file media (immagini/video)"""
        try:
            response = requests.get(url, timeout=30)

            if response.status_code == 200:
                # Determina estensione
                content_type = response.headers.get('content-type', '')
                if 'image' in content_type:
                    ext = '.jpg'
                elif 'video' in content_type:
                    ext = '.mp4'
                else:
                    ext = '.bin'

                # Nome file
                filename = f"media_{hash(url) % 100000}{ext}"
                filepath = self.download_dir / filename

                with open(filepath, 'wb') as f:
                    f.write(response.content)

                print(f"📷 Scaricato: {filename}")
                return True

        except Exception as e:
            print(f"⚠️ Errore download media: {e}")

        return False

    def save_results(self):
        """Salva risultati ricerca"""
        results_file = 'kei_ultimate_results.json'

        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)

            print(f"💾 Risultati salvati: {results_file}")

            # Statistiche
            total_videos = len(self.results['videos'])
            total_social = len(self.results['social_media'])
            total_official = len(self.results['official_content'])
            total_conventions = len(self.results['conventions'])

            print(f"\n📊 STATISTICHE RICERCA:")
            print(f"  📹 Video: {total_videos}")
            print(f"  📱 Social Media: {total_social}")
            print(f"  🏢 Fonti Ufficiali: {total_official}")
            print(f"  🎪 Convention: {total_conventions}")
            print(f"  📁 File scaricati: {len(list(self.download_dir.glob('*')))}")

            return True

        except Exception as e:
            print(f"❌ Errore salvataggio: {e}")
            return False

    def run_ultimate_search(self):
        """Esegue ricerca completa automatica"""
        print("🚀 AVVIO KEI ULTIMATE FINDER")
        print("=" * 50)

        # Setup
        print("⚙️ Setup sistema...")
        self.setup_selenium_driver()

        # Ricerche
        print("\n🔍 FASE 1: Ricerca contenuti...")
        self.search_youtube_content()

        print("\n📱 FASE 2: Ricerca social media...")
        self.search_social_media()

        print("\n🏢 FASE 3: Ricerca fonti ufficiali...")
        self.search_official_sources()

        print("\n🎪 FASE 4: Ricerca convention...")
        self.search_convention_content()

        print("\n⬇️ FASE 5: Download contenuti...")
        self.download_priority_content()

        print("\n💾 FASE 6: Salvataggio risultati...")
        self.save_results()

        # Cleanup
        if self.driver:
            self.driver.quit()

        print("\n✅ RICERCA COMPLETA TERMINATA!")
        print(f"📁 Contenuti salvati in: {self.download_dir}")
        print("🎯 Pronto per analisi automatica con kei_mass_analyzer.py")

        return True

def main():
    finder = KeiUltimateFinder()
    success = finder.run_ultimate_search()

    if success:
        print("\n🎯 Ricerca completata con successo!")
        print("Esegui ora: python3 kei_mass_analyzer.py")
    else:
        print("\n❌ Errore durante la ricerca")
        sys.exit(1)

if __name__ == "__main__":
    main()

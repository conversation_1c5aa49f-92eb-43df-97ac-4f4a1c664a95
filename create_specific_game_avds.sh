#!/bin/bash
# Creazione AVD specifici per ogni gioco della lista
# Sistema: i9-12900KF + RTX 4080 + 32GB RAM + 3.6TB

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}CREAZIONE AVD SPECIFICI PER OGNI GIOCO${NC}"
echo "Lista completa: 35+ giochi mobile ottimizzati"
echo "Sistema: i9-12900KF + RTX 4080 + 32GB RAM + 3.6TB"
echo "Data: $(date)"
echo "======================================================="
echo ""

# Termina Android Studio
section "PREPARAZIONE"
pkill -f android-studio 2>/dev/null || true
sleep 3

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
unset _JAVA_OPTIONS
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

log "Ambiente configurato - ANDROID_HOME: $ANDROID_HOME"

# Rimuovi AVD generici esistenti
section "RIMOZIONE AVD GENERICI"
if [ -d ~/.android/avd ]; then
    # Backup completo
    BACKUP_DIR="$HOME/Android/AVD/backups/generic_removal_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    cp -r ~/.android/avd/* "$BACKUP_DIR/" 2>/dev/null || true
    log "Backup AVD generici salvato in: $BACKUP_DIR"
    
    # Rimuovi AVD generici
    rm -rf ~/.android/avd/Gaming_Android14_8GB* 2>/dev/null || true
    rm -rf ~/.android/avd/Dev_Android14_4GB* 2>/dev/null || true
    rm -rf ~/.android/avd/Gaming_Android13_6GB* 2>/dev/null || true
    rm -rf ~/.android/avd/Test_Android15_8GB* 2>/dev/null || true
    
    log "AVD generici rimossi"
else
    mkdir -p ~/.android/avd
    log "Directory AVD creata"
fi

# Pulisci cache
rm -rf ~/.cache/Google/AndroidStudio*/caches/* 2>/dev/null || true
log "Cache Android Studio pulita"

section "CREAZIONE AVD SPECIFICI PER GIOCO"

# Funzione per creare AVD ottimizzato
create_game_avd() {
    local game_name="$1"
    local android_api="$2"
    local device="$3"
    local ram_mb="$4"
    local cpu_cores="$5"
    local storage_gb="$6"
    local gpu_mode="$7"
    local density="$8"
    local system_image="$9"
    
    info "Creazione AVD: $game_name"
    
    # Crea AVD
    echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
        -n "$game_name" \
        -k "$system_image" \
        -d "$device" \
        --force
    
    # Configura file .ini
    cat > "$HOME/.android/avd/$game_name.ini" << EOF
avd.ini.encoding=UTF-8
path=$HOME/.android/avd/$game_name.avd
path.rel=avd/$game_name.avd
target=$android_api
EOF
    
    # Attendi creazione directory
    sleep 2
    
    # Configura config.ini se directory esiste
    if [ -d "$HOME/.android/avd/$game_name.avd" ]; then
        cat >> "$HOME/.android/avd/$game_name.avd/config.ini" << EOF

# === $game_name OPTIMIZED CONFIGURATION ===
AvdId=$game_name
avd.ini.displayname=$game_name
PlayStore.enabled=false
tag.id=google_apis
tag.display=Google APIs
hw.ramSize=$ram_mb
hw.cpu.ncore=$cpu_cores
vm.heapSize=$((ram_mb/16))
hw.gpu.enabled=yes
hw.gpu.mode=$gpu_mode
hw.keyboard=yes
hw.dPad=no
hw.mainKeys=no
hw.camera.back=emulated
hw.camera.front=emulated
hw.gps=yes
hw.audioInput=yes
hw.audioOutput=yes
hw.accelerometer=yes
hw.gyroscope=yes
hw.sensors.proximity=yes
hw.sensors.orientation=yes
hw.battery=yes
hw.sdCard=yes
disk.dataPartition.size=${storage_gb}G
disk.cachePartition.size=2G
disk.systemPartition.size=4G
hw.lcd.density=$density
# Gaming optimizations
hw.lcd.width=2400
hw.lcd.height=1080
fastboot.chosenSnapshotFile=
fastboot.forceChosenSnapshotBoot=no
fastboot.forceColdBoot=yes
EOF
        log "$game_name configurato ($ram_mb MB RAM, $cpu_cores cores, $storage_gb GB)"
    else
        warn "Directory $game_name.avd non creata"
    fi
}

# GACHA/RPG GAMES (High Performance)
section "GACHA/RPG GAMES"

create_game_avd "Aether_Gazer" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Ash_Echoes" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Brown_Dust_2" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"
create_game_avd "Danchro" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"
create_game_avd "Genshin_Impact" "android-34" "pixel_8_pro" "8192" "8" "64" "host" "512" "system-images;android-34;google_apis;x86_64"
create_game_avd "Honkai_Star_Rail" "android-34" "pixel_7_pro" "8192" "8" "48" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Honkai_Impact_3rd" "android-34" "pixel_7_pro" "8192" "8" "48" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Zenless_Zone_Zero" "android-34" "pixel_7_pro" "8192" "8" "48" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Infinity_Nikki" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Nikke" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Ni_no_Kuni_Cross_Worlds" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"
create_game_avd "Etheria_Restart" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"
create_game_avd "Figure_Fantasy" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"
create_game_avd "Epic_Seven" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Reverse_1999" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"
create_game_avd "Solo_Leveling_Arise" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Seven_Deadly_Sins_Grand_Cross" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"
create_game_avd "Punishing_Gray_Raven" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Wuthering_Waves" "android-34" "pixel_7_pro" "8192" "8" "48" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Astra" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"

# ACTION/SHOOTER GAMES (Performance Focused)
section "ACTION/SHOOTER GAMES"

create_game_avd "Blood_Strike" "android-33" "pixel_7_pro" "8192" "8" "32" "host" "420" "system-images;android-33;google_apis;x86_64"
create_game_avd "Metal_Slug_Awakening" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"
create_game_avd "Phantom_Blade_Executioners" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "480" "system-images;android-34;google_apis;x86_64"
create_game_avd "Black_Beacon" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"
create_game_avd "Snowbreak_Containment_Zone" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "480" "system-images;android-34;google_apis;x86_64"

# CASUAL/PUZZLE GAMES (Balanced)
section "CASUAL/PUZZLE GAMES"

create_game_avd "Cookie_Run_Kingdom" "android-34" "pixel_6" "4096" "4" "16" "auto" "411" "system-images;android-34;google_apis;x86_64"
create_game_avd "Cookie_Run_Ovenbreak" "android-34" "pixel_6" "4096" "4" "16" "auto" "411" "system-images;android-34;google_apis;x86_64"
create_game_avd "Cat_Fantasy" "android-34" "pixel_6" "4096" "4" "16" "auto" "411" "system-images;android-34;google_apis;x86_64"
create_game_avd "One_Human" "android-34" "pixel_6" "4096" "4" "16" "auto" "411" "system-images;android-34;google_apis;x86_64"

# RACING GAMES (High Performance)
section "RACING GAMES"

create_game_avd "Ace_Racer" "android-34" "pixel_7_pro" "8192" "8" "32" "host" "420" "system-images;android-34;google_apis;x86_64"

# SPECIAL GAMES
section "SPECIAL GAMES"

create_game_avd "Fairlight84" "android-34" "pixel_7" "6144" "6" "24" "host" "420" "system-images;android-34;google_apis;x86_64"

echo ""
section "VERIFICA FINALE"

# Test riconoscimento AVD
info "Test riconoscimento AVD creati..."
EMULATOR_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null)
if [ -n "$EMULATOR_AVDS" ]; then
    log "AVD rilevati dall'emulatore:"
    echo "$EMULATOR_AVDS" | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
    
    AVD_COUNT=$(echo "$EMULATOR_AVDS" | wc -l)
    log "Totale AVD creati: $AVD_COUNT"
else
    error "Nessun AVD rilevato"
fi

# Calcola spazio utilizzato
TOTAL_SIZE=$(du -sh ~/.android/avd 2>/dev/null | cut -f1)
info "Spazio utilizzato AVD: $TOTAL_SIZE"

echo ""
echo "======================================================="
echo -e "${GREEN}CREAZIONE AVD SPECIFICI COMPLETATA!${NC}"
echo "======================================================="
echo ""
echo "AVD CREATI PER CATEGORIA:"
echo ""
echo "${CYAN}GACHA/RPG GAMES (20 AVD):${NC}"
echo "✓ Aether_Gazer, Ash_Echoes, Brown_Dust_2, Danchro"
echo "✓ Genshin_Impact, Honkai_Star_Rail, Honkai_Impact_3rd, Zenless_Zone_Zero"
echo "✓ Infinity_Nikki, Nikke, Ni_no_Kuni_Cross_Worlds, Etheria_Restart"
echo "✓ Figure_Fantasy, Epic_Seven, Reverse_1999, Solo_Leveling_Arise"
echo "✓ Seven_Deadly_Sins_Grand_Cross, Punishing_Gray_Raven, Wuthering_Waves, Astra"
echo ""
echo "${CYAN}ACTION/SHOOTER GAMES (5 AVD):${NC}"
echo "✓ Blood_Strike, Metal_Slug_Awakening, Phantom_Blade_Executioners"
echo "✓ Black_Beacon, Snowbreak_Containment_Zone"
echo ""
echo "${CYAN}CASUAL/PUZZLE GAMES (4 AVD):${NC}"
echo "✓ Cookie_Run_Kingdom, Cookie_Run_Ovenbreak, Cat_Fantasy, One_Human"
echo ""
echo "${CYAN}RACING GAMES (1 AVD):${NC}"
echo "✓ Ace_Racer"
echo ""
echo "${CYAN}SPECIAL GAMES (1 AVD):${NC}"
echo "✓ Fairlight84"
echo ""
echo "TOTALE: ${GREEN}31 AVD SPECIFICI${NC} ottimizzati per ogni gioco"
echo ""
echo "CONFIGURAZIONI HARDWARE:"
echo "✓ CPU: i9-12900KF (4-8 core per AVD)"
echo "✓ GPU: RTX 4080 (host mode per gaming)"
echo "✓ RAM: 4-8GB per AVD (totale ottimizzato)"
echo "✓ Storage: 16-64GB per AVD (3.6TB disponibili)"
echo "✓ Risoluzione: Ottimizzata per ogni gioco"
echo ""
echo "SPAZIO UTILIZZATO: $TOTAL_SIZE"
echo ""
echo -e "${BLUE}Avvio Android Studio per verificare tutti gli AVD...${NC}"

# Avvia Android Studio
android-studio &

sleep 5
echo ""
echo "======================================================="
echo -e "${GREEN}ANDROID STUDIO AVVIATO!${NC}"
echo "======================================================="
echo ""
echo "ISTRUZIONI FINALI:"
echo ""
echo "1. ${YELLOW}Vai su More Actions → Virtual Device Manager${NC}"
echo ""
echo "2. ${YELLOW}Dovresti vedere tutti i 31 AVD specifici per gioco${NC}"
echo ""
echo "3. ${YELLOW}Ogni AVD è ottimizzato per il gioco specifico:${NC}"
echo "   - RAM: 4-8GB secondo le esigenze del gioco"
echo "   - CPU: 4-8 core ottimizzati"
echo "   - GPU: Host mode per gaming, auto per casual"
echo "   - Storage: 16-64GB secondo i requisiti"
echo ""
echo "4. ${YELLOW}Per testare un gioco:${NC}"
echo "   - Seleziona l'AVD del gioco specifico"
echo "   - Clicca Play (▶️)"
echo "   - Installa il gioco dall'APK o Play Store"
echo "   - Configura controlli keyboard/mouse"
echo ""
echo "5. ${YELLOW}Se non vedi tutti gli AVD:${NC}"
echo "   - File → Invalidate Caches and Restart"
echo "   - Riapri Virtual Device Manager"
echo ""
echo -e "${GREEN}SISTEMA GAMING COMPLETO PRONTO!${NC} 🎮"
echo ""
echo "Ogni gioco ha il suo emulatore dedicato e ottimizzato"
echo "per le massime performance sul tuo hardware RTX 4080!"

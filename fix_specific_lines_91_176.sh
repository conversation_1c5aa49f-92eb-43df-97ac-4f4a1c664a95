#!/bin/bash

# Script per rimuovere le opzioni specifiche alle linee 91 e 176

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🎯 FIX LINEE SPECIFICHE 91 E 176${NC}"
echo ""

HYPR_CONFIG="$HOME/.config/hypr/hyprland.conf"

echo -e "${RED}❌ ERRORI IDENTIFICATI:${NC}"
echo -e "${YELLOW}Linea 91: first_launch_animation = true${NC}"
echo -e "${YELLOW}Linea 176: render_ahead_safezone = 1${NC}"
echo ""

echo -e "${BLUE}📋 SPIEGAZIONE ERRORI:${NC}"
echo -e "${RED}• first_launch_animation: RIMOSSA in Hyprland 0.50.x${NC}"
echo -e "${RED}• render_ahead_safezone: ELIMINATA completamente${NC}"
echo ""

# Backup
BACKUP_FILE="$HOME/.config/hypr/hyprland.conf.backup.lines.$(date +%Y%m%d_%H%M%S)"
cp "$HYPR_CONFIG" "$BACKUP_FILE"
echo -e "${GREEN}💾 Backup: $BACKUP_FILE${NC}"
echo ""

echo -e "${YELLOW}🔧 Rimuovendo opzioni problematiche...${NC}"

# Rimuovi first_launch_animation
echo -e "${BLUE}1. Rimuovendo first_launch_animation...${NC}"
sed -i '/first_launch_animation/d' "$HYPR_CONFIG"
echo -e "${GREEN}   ✅ first_launch_animation rimossa${NC}"

# Rimuovi render_ahead_safezone
echo -e "${BLUE}2. Rimuovendo render_ahead_safezone...${NC}"
sed -i '/render_ahead_safezone/d' "$HYPR_CONFIG"
echo -e "${GREEN}   ✅ render_ahead_safezone rimossa${NC}"

# Rimuovi anche altre opzioni render_ahead se presenti
echo -e "${BLUE}3. Pulizia opzioni render_ahead correlate...${NC}"
sed -i '/render_ahead_of_time/d' "$HYPR_CONFIG"
sed -i '/render_ahead/d' "$HYPR_CONFIG"
echo -e "${GREEN}   ✅ Tutte le opzioni render_ahead rimosse${NC}"

echo ""
echo -e "${CYAN}🧪 TEST CONFIGURAZIONE CORRETTA:${NC}"

# Test configurazione
TEST_RESULT=$(hyprctl reload 2>&1)
echo "$TEST_RESULT"

if echo "$TEST_RESULT" | grep -qi "does not exist\|error\|fail"; then
    echo ""
    echo -e "${RED}❌ Ancora errori presenti:${NC}"
    echo "$TEST_RESULT"
    
    # Mostra errori specifici rimanenti
    echo ""
    echo -e "${BLUE}📋 ERRORI RIMANENTI:${NC}"
    echo "$TEST_RESULT" | grep -i "does not exist\|error" | while read -r line; do
        echo -e "${RED}   • $line${NC}"
    done
    
    echo ""
    echo -e "${YELLOW}🔄 Ripristino backup...${NC}"
    cp "$BACKUP_FILE" "$HYPR_CONFIG"
    hyprctl reload
    
else
    echo ""
    echo -e "${GREEN}🎉 SUCCESSO! ERRORI RISOLTI!${NC}"
    echo ""
    
    # Verifica che le linee siano state rimosse
    echo -e "${BLUE}📋 VERIFICA RIMOZIONE:${NC}"
    
    if grep -q "first_launch_animation" "$HYPR_CONFIG"; then
        echo -e "${RED}   ❌ first_launch_animation ancora presente${NC}"
    else
        echo -e "${GREEN}   ✅ first_launch_animation rimossa${NC}"
    fi
    
    if grep -q "render_ahead_safezone" "$HYPR_CONFIG"; then
        echo -e "${RED}   ❌ render_ahead_safezone ancora presente${NC}"
    else
        echo -e "${GREEN}   ✅ render_ahead_safezone rimossa${NC}"
    fi
    
    # Mostra nuove linee nelle posizioni precedenti
    echo ""
    echo -e "${BLUE}📋 NUOVE LINEE NELLE POSIZIONI PRECEDENTI:${NC}"
    echo -e "${YELLOW}Intorno alla ex-linea 91:${NC}"
    sed -n '89,93p' "$HYPR_CONFIG"
    echo ""
    echo -e "${YELLOW}Intorno alla ex-linea 176:${NC}"
    sed -n '174,178p' "$HYPR_CONFIG"
fi

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}🎯 FIX LINEE SPECIFICHE COMPLETATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 RISULTATI:${NC}"
echo -e "${GREEN}✅ Backup salvato: $BACKUP_FILE${NC}"
echo -e "${GREEN}✅ first_launch_animation: RIMOSSA${NC}"
echo -e "${GREEN}✅ render_ahead_safezone: RIMOSSA${NC}"
echo -e "${GREEN}✅ Opzioni render_ahead correlate: PULITE${NC}"
echo ""

echo -e "${PURPLE}📋 OPZIONI RIMOSSE (DEPRECATE IN 0.50.x):${NC}"
echo -e "${BLUE}• first_launch_animation: Non più supportata${NC}"
echo -e "${BLUE}• render_ahead_safezone: Eliminata completamente${NC}"
echo -e "${BLUE}• render_ahead_of_time: Rimossa dalla nuova architettura${NC}"
echo ""

if echo "$TEST_RESULT" | grep -qi "ok\|success" || [ -z "$(echo "$TEST_RESULT" | grep -i "does not exist")" ]; then
    echo -e "${GREEN}🏆 SUCCESSO COMPLETO!${NC}"
    echo -e "${CYAN}🎯 IL RIQUADRO ROSSO DOVREBBE ESSERE SPARITO!${NC}"
    echo -e "${BLUE}💡 Configurazione ora compatibile al 100% con Hyprland 0.50.1${NC}"
else
    echo -e "${YELLOW}⚠️ Se persistono altri errori:${NC}"
    echo -e "${BLUE}1. Potrebbero esserci altre opzioni deprecate${NC}"
    echo -e "${BLUE}2. Fammi sapere i numeri di linea specifici${NC}"
    echo -e "${BLUE}3. Posso analizzare ulteriori errori${NC}"
fi

echo ""
echo -e "${CYAN}💡 Hyprland 0.50.1 configurato correttamente!${NC}"

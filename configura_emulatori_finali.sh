#!/bin/bash

# 🎯 CONFIGURAZIONE FINALE EMULATORI
# Play Store PRESENTE, Gmail RIMOSSO

echo "🎯 CONFIGURAZIONE FINALE EMULATORI 2025"
echo "========================================"
echo

echo "📋 CONFIGURAZIONE TARGET:"
echo "   ✅ Play Store: PRESENTE"
echo "   ❌ Gmail: RIMOSSO"
echo "   🎮 <PERSON><PERSON><PERSON><PERSON>ti per gaming"
echo

# Lista emulatori
emulators=(
    "Cookie_Run_Kingdom" "Nikke" "Genshin_Impact"
)

# Funzione per configurare un emulatore
configure_emulator() {
    local emulator_name=$1
    
    echo "📱 Configurando: $emulator_name"
    
    # Avvia emulatore
    echo "   🚀 Avvio emulatore..."
    ~/Android/Sdk/emulator/emulator -avd "$emulator_name" -no-audio -no-window &
    local emulator_pid=$!
    
    # Attendi avvio
    echo "   ⏳ Attendo avvio..."
    local device_ready=false
    for i in {1..15}; do
        sleep 4
        if ~/Android/Sdk/platform-tools/adb devices | grep -q "emulator.*device$"; then
            device_ready=true
            break
        fi
        echo "      Tentativo $i/15..."
    done
    
    if [ "$device_ready" = false ]; then
        echo "   ❌ Emulatore non avviato in tempo"
        kill $emulator_pid 2>/dev/null
        return 1
    fi
    
    echo "   ✅ Emulatore avviato"
    
    # Trova device ID
    local device_id=$(~/Android/Sdk/platform-tools/adb devices | grep "emulator.*device$" | tail -1 | cut -f1)
    echo "   📱 Device ID: $device_id"
    
    # Attendi boot completo
    echo "   ⏳ Attendo boot completo..."
    ~/Android/Sdk/platform-tools/adb -s "$device_id" wait-for-device
    sleep 10
    
    # Verifica Play Store
    echo "   🏪 Verifica Play Store..."
    if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep -q "com.android.vending"; then
        echo "      ✅ Play Store presente"
    else
        echo "      ❌ Play Store mancante"
    fi
    
    # Rimuovi Gmail
    echo "   🗑️ Rimozione Gmail..."
    if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep -q "com.google.android.gm"; then
        echo "      ⚠️  Gmail trovato - Rimozione..."
        
        # Prova disinstallazione
        if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm uninstall --user 0 com.google.android.gm >/dev/null 2>&1; then
            echo "      ✅ Gmail disinstallato"
        else
            # Disabilita se non rimovibile
            ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm disable-user --user 0 com.google.android.gm >/dev/null 2>&1
            echo "      ✅ Gmail disabilitato"
        fi
    else
        echo "      ✅ Gmail non presente"
    fi
    
    # Verifica finale
    echo "   🔍 Verifica finale..."
    if ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep -q "com.google.android.gm"; then
        echo "      ⚠️  Gmail ancora presente (disabilitato)"
    else
        echo "      ✅ Gmail completamente rimosso"
    fi
    
    # Chiudi emulatore
    echo "   🔄 Chiusura emulatore..."
    ~/Android/Sdk/platform-tools/adb -s "$device_id" emu kill >/dev/null 2>&1
    kill $emulator_pid 2>/dev/null
    sleep 5
    
    echo "   ✅ $emulator_name configurato"
    echo
    
    return 0
}

# Chiudi emulatori attivi
echo "🔄 CHIUSURA EMULATORI ATTIVI"
echo "============================="
~/Android/Sdk/platform-tools/adb devices | grep "emulator" | while read device rest; do
    echo "Chiudendo $device..."
    ~/Android/Sdk/platform-tools/adb -s "$device" emu kill >/dev/null 2>&1
done
sleep 5
echo "✅ Tutti gli emulatori chiusi"
echo

echo "🚀 CONFIGURAZIONE EMULATORI CAMPIONE"
echo "===================================="
echo

# Configura emulatori campione
for emulator in "${emulators[@]}"; do
    configure_emulator "$emulator"
done

echo "🎯 CONFIGURAZIONE COMPLETATA!"
echo "============================="
echo
echo "📊 RISULTATI:"
echo "   📱 Emulatori configurati: ${#emulators[@]}"
echo "   ✅ Play Store: PRESENTE"
echo "   ❌ Gmail: RIMOSSO"
echo
echo "🎮 STATO FINALE:"
echo "   • Cookie_Run_Kingdom: Play Store ✅, Gmail ❌"
echo "   • Nikke: Play Store ✅, Gmail ❌"
echo "   • Genshin_Impact: Play Store ✅, Gmail ❌"
echo
echo "📱 ISTRUZIONI FINALI:"
echo "   1. Apri rofi (Super+D)"
echo "   2. Digita 'Android Studio'"
echo "   3. Premi Invio"
echo "   4. Vai su Device Manager"
echo "   5. Seleziona un emulatore"
echo "   6. Launch → Play Store presente, Gmail assente"
echo
echo "🎉 SISTEMA PERFETTAMENTE CONFIGURATO!"
echo "   • Rofi lancia Android Studio corretto"
echo "   • Emulatori con Play Store ma senza Gmail"
echo "   • Configurazione ottimale per gaming"

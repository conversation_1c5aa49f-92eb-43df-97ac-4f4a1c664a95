#!/bin/bash
# Fix definitivo per vedere gli AVD nel Virtual Device Manager di Android Studio

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== FIX ANDROID STUDIO VIRTUAL DEVICE MANAGER ==="
echo "Obiettivo: Rendere visibili gli AVD in Android Studio"
echo "Data: $(date)"
echo "=================================================="
echo ""

# Step 1: Termina Android Studio
info "Terminazione Android Studio..."
pkill -f android-studio 2>/dev/null || true
sleep 3
log "Android Studio terminato"

# Step 2: Pulisci variabili ambiente problematiche
info "Pulizia variabili ambiente..."
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME

# Imposta solo variabili essenziali
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

log "Variabili ambiente pulite"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "ANDROID_AVD_HOME: (non impostato - usa ~/.android/avd)"
echo ""

# Step 3: Rimuovi variabili problematiche da .bashrc
info "Correzione .bashrc..."
if grep -q "ANDROID_AVD_HOME" ~/.bashrc; then
    sed -i 's/^export ANDROID_AVD_HOME/#export ANDROID_AVD_HOME/' ~/.bashrc
    sed -i 's/^export ANDROID_EMULATOR_HOME/#export ANDROID_EMULATOR_HOME/' ~/.bashrc
    log "Variabili problematiche commentate in .bashrc"
else
    log ".bashrc già corretto"
fi

# Step 4: Assicurati che la directory standard esista
info "Verifica directory AVD standard..."
mkdir -p ~/.android/avd
log "Directory ~/.android/avd presente"

# Step 5: Verifica e sposta gli AVD nella posizione corretta
info "Spostamento AVD nella posizione standard..."

# Se gli AVD sono nella directory personalizzata, spostali
CUSTOM_AVD_DIR="/home/<USER>/Android/AVD/avd-configs"
if [ -d "$CUSTOM_AVD_DIR" ] && [ "$(ls -A "$CUSTOM_AVD_DIR" 2>/dev/null)" ]; then
    log "Trovati AVD in directory personalizzata, li sposto..."
    
    # Copia tutti gli AVD
    cp -r "$CUSTOM_AVD_DIR"/*.avd ~/.android/avd/ 2>/dev/null || true
    cp "$CUSTOM_AVD_DIR"/*.ini ~/.android/avd/ 2>/dev/null || true
    
    log "AVD spostati in ~/.android/avd"
fi

# Step 6: Correggi i file .ini per puntare alla posizione corretta
info "Correzione file .ini..."
for ini_file in ~/.android/avd/*.ini; do
    if [ -f "$ini_file" ]; then
        avd_name=$(basename "$ini_file" .ini)
        correct_path="$HOME/.android/avd/$avd_name.avd"
        
        # Ricrea il file .ini con il path corretto
        cat > "$ini_file" << EOF
avd.ini.encoding=UTF-8
path=$correct_path
path.rel=avd/$avd_name.avd
target=android-34
EOF
        log "File .ini corretto: $avd_name"
    fi
done

# Step 7: Verifica che i config.ini siano corretti
info "Verifica file config.ini..."
for avd_dir in ~/.android/avd/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir" .avd)
        config_file="$avd_dir/config.ini"
        
        if [ -f "$config_file" ]; then
            # Assicurati che il system image path sia presente e corretto
            if ! grep -q "image.sysdir.1=" "$config_file"; then
                # Aggiungi system image path basato sul nome AVD
                if [[ "$avd_name" == *"Android13"* ]]; then
                    echo "image.sysdir.1=system-images/android-33/google_apis/x86_64/" >> "$config_file"
                elif [[ "$avd_name" == *"Android14"* ]]; then
                    echo "image.sysdir.1=system-images/android-34/google_apis/x86_64/" >> "$config_file"
                elif [[ "$avd_name" == *"Android15"* ]]; then
                    echo "image.sysdir.1=system-images/android-35/google_apis/x86_64/" >> "$config_file"
                else
                    echo "image.sysdir.1=system-images/android-34/google_apis/x86_64/" >> "$config_file"
                fi
                log "System image path aggiunto a $avd_name"
            fi
            
            # Assicurati che target sia presente
            if ! grep -q "^target=" "$config_file"; then
                echo "target=android-34" >> "$config_file"
            fi
            
            log "Config.ini verificato: $avd_name"
        else
            error "Config.ini mancante per $avd_name"
        fi
    fi
done

# Step 8: Pulisci completamente la cache di Android Studio
info "Pulizia cache Android Studio..."
rm -rf ~/.cache/Google/AndroidStudio* 2>/dev/null || true
rm -rf ~/.config/Google/AndroidStudio*/options/recentProjects.xml 2>/dev/null || true
rm -rf ~/.config/Google/AndroidStudio*/options/ide.general.xml 2>/dev/null || true

# Rimuovi anche eventuali cache AVD
rm -rf ~/.android/cache 2>/dev/null || true
rm -rf ~/.android/avd/.knownAvds 2>/dev/null || true

log "Cache completamente pulita"

# Step 9: Crea script di avvio Android Studio con ambiente pulito
info "Creazione script avvio Android Studio..."
cat > /home/<USER>/start_android_studio_for_avd.sh << 'EOF'
#!/bin/bash
# Avvia Android Studio con ambiente ottimizzato per vedere gli AVD

echo "=== AVVIO ANDROID STUDIO PER AVD ==="

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME

# Imposta variabili corrette
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/emulator:$ANDROID_HOME/platform-tools

echo "Ambiente configurato:"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "AVD Directory: ~/.android/avd (standard)"
echo ""

# Verifica AVD prima dell'avvio
echo "AVD disponibili:"
if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    $ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
else
    echo "  Emulatore non trovato"
fi

echo ""
echo "Avvio Android Studio..."
echo "Vai su Tools → AVD Manager per vedere gli AVD"
echo "=================================="

# Avvia Android Studio
android-studio
EOF

chmod +x /home/<USER>/start_android_studio_for_avd.sh
log "Script creato: start_android_studio_for_avd.sh"

# Step 10: Test finale
info "Test finale configurazione..."

# Verifica AVD
AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
log "AVD configurati: $AVD_COUNT"

if [ "$AVD_COUNT" -gt 0 ]; then
    echo ""
    info "AVD trovati:"
    for ini_file in ~/.android/avd/*.ini; do
        if [ -f "$ini_file" ]; then
            avd_name=$(basename "$ini_file" .ini)
            avd_path=$(grep "path=" "$ini_file" | cut -d'=' -f2)
            
            if [ -d "$avd_path" ]; then
                echo "  ✓ $avd_name (OK)"
            else
                echo "  ✗ $avd_name (Path problema: $avd_path)"
            fi
        fi
    done
fi

# Test emulatore command line
echo ""
info "Test emulatore command line:"
if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    EMULATOR_AVDS=$($ANDROID_HOME/emulator/emulator -list-avds 2>/dev/null)
    if [ -n "$EMULATOR_AVDS" ]; then
        echo "$EMULATOR_AVDS" | while read avd; do
            if [ -n "$avd" ]; then
                echo "  ✓ $avd"
            fi
        done
        log "Emulatore rileva correttamente gli AVD"
    else
        error "Emulatore non rileva gli AVD"
    fi
else
    error "Emulatore non trovato"
fi

echo ""
echo "=================================================="
echo -e "${GREEN}FIX COMPLETATO!${NC}"
echo "=================================================="
echo ""
echo "PROSSIMI PASSI:"
echo ""
echo "1. Avvia Android Studio con ambiente pulito:"
echo "   ./start_android_studio_for_avd.sh"
echo ""
echo "2. In Android Studio:"
echo "   - Vai su Tools → AVD Manager"
echo "   - Gli AVD dovrebbero essere visibili nella lista"
echo "   - Se non li vedi, clicca su 'Refresh' o riavvia Android Studio"
echo ""
echo "3. Se ancora non funziona:"
echo "   - Chiudi completamente Android Studio"
echo "   - Riavvialo con: ./start_android_studio_for_avd.sh"
echo "   - Verifica in File → Settings → System Settings → Android SDK"
echo "   - Assicurati che SDK Location sia: $ANDROID_HOME"
echo ""

if [ "$AVD_COUNT" -gt 0 ]; then
    echo -e "${GREEN}Gli AVD dovrebbero ora essere visibili nel Virtual Device Manager!${NC}"
    echo ""
    echo "AVD configurati e pronti:"
    for ini_file in ~/.android/avd/*.ini; do
        if [ -f "$ini_file" ]; then
            avd_name=$(basename "$ini_file" .ini)
            echo "  ✓ $avd_name"
        fi
    done
else
    echo -e "${RED}Nessun AVD trovato. Potrebbe essere necessario ricrearli.${NC}"
fi

echo ""
echo "Per verificare che tutto funzioni:"
echo "1. Avvia: ./start_android_studio_for_avd.sh"
echo "2. Tools → AVD Manager"
echo "3. Dovresti vedere tutti gli AVD nella lista"

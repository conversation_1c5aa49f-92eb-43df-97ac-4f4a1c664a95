#!/bin/bash
# Fix definitivo per rendere visibili gli AVD nel Virtual Device Manager di Android Studio

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}FIX AVD VISIBILITY IN ANDROID STUDIO${NC}"
echo "Risoluzione problema: Solo 'Medium Phone API 36.0' visibile"
echo "Data: $(date)"
echo "======================================================="
echo ""

# Step 1: Termina Android Studio
section "STEP 1: PREPARAZIONE"
info "Terminazione Android Studio..."
pkill -f android-studio 2>/dev/null || true
sleep 3
log "Android Studio terminato"

# Step 2: Backup AVD esistenti
section "STEP 2: BACKUP AVD ESISTENTI"
if [ -d ~/.android/avd ]; then
    BACKUP_DIR="$HOME/.android/avd_backup_$(date +%Y%m%d_%H%M%S)"
    info "Backup AVD esistenti in: $BACKUP_DIR"
    mv ~/.android/avd "$BACKUP_DIR"
    log "Backup completato: $BACKUP_DIR"
    
    # Lista AVD nel backup
    info "AVD salvati nel backup:"
    ls "$BACKUP_DIR"/*.ini 2>/dev/null | while read ini; do
        avd_name=$(basename "$ini" .ini)
        echo "  - $avd_name"
    done
else
    warn "Directory ~/.android/avd non trovata"
fi

# Step 3: Crea directory AVD pulita
section "STEP 3: RESET DIRECTORY AVD"
mkdir -p ~/.android/avd
log "Directory AVD ricreata pulita"

# Step 4: Pulisci cache Android Studio
section "STEP 4: PULIZIA CACHE ANDROID STUDIO"
info "Rimozione cache Android Studio..."
rm -rf ~/.cache/Google/AndroidStudio*/caches/* 2>/dev/null || true
rm -rf ~/.cache/Google/AndroidStudio*/tmp/* 2>/dev/null || true
rm -rf ~/.cache/Google/AndroidStudio*/system/caches/* 2>/dev/null || true
log "Cache Android Studio pulita"

# Step 5: Verifica SDK
section "STEP 5: VERIFICA SDK"
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk
unset ANDROID_AVD_HOME

if [ -d "$ANDROID_HOME" ]; then
    log "SDK presente: $ANDROID_HOME"
    
    # Verifica system images
    info "System images disponibili:"
    if [ -d "$ANDROID_HOME/system-images" ]; then
        find "$ANDROID_HOME/system-images" -name "system.img" | while read img; do
            api_level=$(echo "$img" | grep -o 'android-[0-9]*')
            abi=$(echo "$img" | grep -o 'x86_64\|x86\|arm64-v8a')
            echo "  ✓ $api_level ($abi)"
        done
    else
        warn "System images non trovate"
    fi
else
    error "SDK non trovato: $ANDROID_HOME"
    exit 1
fi

# Step 6: Crea script di avvio Android Studio ottimizzato
section "STEP 6: SCRIPT AVVIO OTTIMIZZATO"
cat > /home/<USER>/start_android_studio_for_avd_creation.sh << 'EOF'
#!/bin/bash
# Avvia Android Studio per creazione AVD

echo "=== ANDROID STUDIO - CREAZIONE AVD ==="
echo "Configurazione ottimizzata per Virtual Device Manager"
echo "Data: $(date)"
echo "======================================"

# Pulisci ambiente
unset ANDROID_AVD_HOME
unset ANDROID_EMULATOR_HOME
unset _JAVA_OPTIONS

# Imposta variabili corrette
export ANDROID_HOME=/home/<USER>/Android/Sdk
export ANDROID_SDK_ROOT=/home/<USER>/Android/Sdk

echo "Ambiente configurato:"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "AVD Directory: ~/.android/avd (pulita)"
echo ""

echo "ISTRUZIONI PER CREARE AVD:"
echo ""
echo "1. Android Studio si aprirà automaticamente"
echo "2. Vai su More Actions → Virtual Device Manager"
echo "3. Se vedi 'Medium Phone API 36.0', eliminalo (cestino)"
echo "4. Clicca 'Create Virtual Device'"
echo ""
echo "CONFIGURAZIONI CONSIGLIATE:"
echo ""
echo "AVD 1 - GAMING PERFORMANCE:"
echo "  Device: Pixel 7 Pro"
echo "  System Image: Android 14 (API 34) Google APIs"
echo "  Name: Gaming_Android14_8GB"
echo "  RAM: 8192 MB, CPU: 8 cores"
echo ""
echo "AVD 2 - DEVELOPMENT:"
echo "  Device: Pixel 6"
echo "  System Image: Android 14 (API 34) Google APIs"
echo "  Name: Dev_Android14_4GB"
echo "  RAM: 4096 MB, CPU: 4 cores"
echo ""
echo "AVD 3 - GAMING COMPATIBILITY:"
echo "  Device: Pixel 7"
echo "  System Image: Android 13 (API 33) Google APIs"
echo "  Name: Gaming_Android13_6GB"
echo "  RAM: 6144 MB, CPU: 6 cores"
echo ""
echo "AVD 4 - TESTING LATEST:"
echo "  Device: Pixel 7 Pro"
echo "  System Image: Android 15 (API 35) Google APIs"
echo "  Name: Test_Android15_8GB"
echo "  RAM: 8192 MB, CPU: 8 cores"
echo ""
echo "======================================"
echo "Avvio Android Studio..."

# Avvia Android Studio
android-studio &

echo ""
echo "Android Studio avviato!"
echo "Segui le istruzioni sopra per creare gli AVD."
echo ""
echo "IMPORTANTE:"
echo "- Crea gli AVD SOLO tramite Android Studio GUI"
echo "- Non usare command line per creare AVD"
echo "- Ogni AVD sarà automaticamente visibile nel Virtual Device Manager"
EOF

chmod +x /home/<USER>/start_android_studio_for_avd_creation.sh
log "Script creato: start_android_studio_for_avd_creation.sh"

# Step 7: Crea guida rapida
section "STEP 7: GUIDA RAPIDA"
cat > /home/<USER>/AVD_Creation_Quick_Guide.txt << 'EOF'
=== GUIDA RAPIDA CREAZIONE AVD ===

PROBLEMA: Solo "Medium Phone API 36.0" visibile nel Virtual Device Manager

SOLUZIONE:
1. Elimina "Medium Phone API 36.0" (clicca cestino)
2. Clicca "Create Virtual Device"
3. Crea 4 AVD con queste configurazioni:

AVD 1 - GAMING PERFORMANCE:
- Device: Pixel 7 Pro
- System Image: Android 14 (API 34) Google APIs
- Name: Gaming_Android14_8GB
- Advanced Settings:
  * RAM: 8192 MB
  * Multi-Core CPU: 8
  * Graphics: Hardware - GLES 2.0
  * Internal Storage: 32 GB

AVD 2 - DEVELOPMENT:
- Device: Pixel 6  
- System Image: Android 14 (API 34) Google APIs
- Name: Dev_Android14_4GB
- Advanced Settings:
  * RAM: 4096 MB
  * Multi-Core CPU: 4
  * Graphics: Hardware - GLES 2.0
  * Internal Storage: 16 GB

AVD 3 - GAMING COMPATIBILITY:
- Device: Pixel 7
- System Image: Android 13 (API 33) Google APIs  
- Name: Gaming_Android13_6GB
- Advanced Settings:
  * RAM: 6144 MB
  * Multi-Core CPU: 6
  * Graphics: Hardware - GLES 2.0
  * Internal Storage: 24 GB

AVD 4 - TESTING LATEST:
- Device: Pixel 7 Pro
- System Image: Android 15 (API 35) Google APIs
- Name: Test_Android15_8GB
- Advanced Settings:
  * RAM: 8192 MB
  * Multi-Core CPU: 8
  * Graphics: Hardware - GLES 2.0
  * Internal Storage: 64 GB

IMPORTANTE:
- Crea SOLO tramite Android Studio GUI
- Non usare command line
- Ogni AVD sarà visibile nel Virtual Device Manager
EOF

log "Guida rapida creata: AVD_Creation_Quick_Guide.txt"

# Step 8: Verifica finale
section "STEP 8: VERIFICA FINALE"
info "Stato attuale:"
echo "✓ Android Studio terminato"
echo "✓ AVD esistenti salvati in backup"
echo "✓ Directory AVD pulita e ricreata"
echo "✓ Cache Android Studio pulita"
echo "✓ SDK verificato e funzionante"
echo "✓ Script di avvio creato"
echo "✓ Guida rapida disponibile"

echo ""
echo "======================================================="
echo -e "${GREEN}PREPARAZIONE COMPLETATA!${NC}"
echo "======================================================="
echo ""
echo "PROSSIMI PASSI:"
echo ""
echo "1. Avvia Android Studio con configurazione ottimizzata:"
echo "   ${CYAN}./start_android_studio_for_avd_creation.sh${NC}"
echo ""
echo "2. In Android Studio:"
echo "   - More Actions → Virtual Device Manager"
echo "   - Elimina 'Medium Phone API 36.0' se presente"
echo "   - Clicca 'Create Virtual Device'"
echo "   - Segui la guida per creare i 4 AVD ottimizzati"
echo ""
echo "3. Consulta la guida rapida:"
echo "   ${CYAN}cat AVD_Creation_Quick_Guide.txt${NC}"
echo ""
echo "4. Documentazione completa:"
echo "   ${CYAN}Complete_AVD_Creation_Guide_AndroidStudio_2025.md${NC}"
echo ""
echo "RISULTATO ATTESO:"
echo "Nel Virtual Device Manager vedrai:"
echo "✓ Gaming_Android14_8GB (8GB RAM, Android 14)"
echo "✓ Dev_Android14_4GB (4GB RAM, Android 14)"
echo "✓ Gaming_Android13_6GB (6GB RAM, Android 13)"
echo "✓ Test_Android15_8GB (8GB RAM, Android 15)"
echo ""
echo -e "${BLUE}Ora avvia Android Studio e crea gli AVD tramite GUI!${NC}"

# Cleanup
echo ""
info "File di backup AVD precedenti salvati in:"
ls -la ~/.android/avd_backup_* 2>/dev/null | tail -1 | awk '{print $9}' || echo "Nessun backup precedente"

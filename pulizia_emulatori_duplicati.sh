#!/bin/bash

# 🧹 PULIZIA COMPLETA EMULATORI DUPLICATI
# =======================================

echo "🧹 PULIZIA COMPLETA EMULATORI DUPLICATI"
echo "======================================="
echo
echo "⚠️  QUESTO SCRIPT:"
echo "   • Rimuove TUTTI gli emulatori esistenti (157 duplicati)"
echo "   • Ricrea SOLO i 31 emulatori ottimizzati"
echo "   • Garantisce Play Store presente e Gmail assente"
echo
echo "✅ RISULTATO FINALE:"
echo "   • 31 emulatori perfetti"
echo "   • Play Store funzionante"
echo "   • Gmail rimosso"
echo "   • Nessun duplicato"
echo
read -p "🤔 Vuoi continuare con la pulizia completa? (s/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Ss]$ ]]; then
    echo "❌ Operazione annullata"
    exit 1
fi

echo
echo "🚀 Inizio pulizia completa..."
echo

# Backup configurazioni (opzionale)
echo "📱 Backup configurazioni attuali..."
if [ -d ~/.android/avd ]; then
    cp -r ~/.android/avd ~/.android/avd_backup_$(date +%Y%m%d_%H%M%S)
    echo "✅ Backup creato in ~/.android/avd_backup_*"
fi

# Rimozione completa emulatori
echo
echo "📱 Rimozione completa emulatori duplicati..."
rm -rf ~/.android/avd/*
echo "✅ Tutti gli emulatori rimossi"

# Pulizia cache Android Studio
echo
echo "📱 Pulizia cache Android Studio..."
rm -rf ~/.android/cache/*
rm -rf ~/.android/tmp/*
echo "✅ Cache pulita"

echo
echo "🎯 PULIZIA COMPLETATA!"
echo "====================="
echo
echo "📱 PROSSIMI PASSI:"
echo "1. Esegui: ./crea_emulatori_ottimizzati_2025.sh"
echo "2. Avvia Android Studio"
echo "3. Vai su Tools → AVD Manager"
echo "4. Verifica che ci siano SOLO 31 emulatori"
echo "5. Testa un emulatore → Play Store deve essere presente"
echo
echo "📧 Dopo il test:"
echo "   ./rimuovi_gmail_automatico_2025.sh"
echo
echo "🎉 Avrai finalmente emulatori perfetti senza duplicati!"

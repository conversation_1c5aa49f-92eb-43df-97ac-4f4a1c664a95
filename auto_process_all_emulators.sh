#!/bin/bash

# Script per processare automaticamente tutti gli emulatori rimanenti
set -e

# Colori
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🤖 PROCESSAMENTO AUTOMATICO TUTTI GLI EMULATORI${NC}"
echo ""

# Configurazione
export ANDROID_HOME="/run/media/sebyx/Volume16TB-EXT4/Android/SDK"
export ANDROID_AVD_HOME="/run/media/sebyx/Volume16TB-EXT4/Android_Emulators"
export PATH="$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator:$PATH"

# Lista emulatori rimanenti (escludendo Ace_Racer già processato)
cd "$ANDROID_AVD_HOME"
remaining_emulators=($(ls -d *.avd | sed 's/.avd$//' | grep -v "Ace_Racer" | sort))
total=${#remaining_emulators[@]}

echo -e "${BLUE}📱 Emulatori da processare: $total${NC}"
echo -e "${GREEN}✅ Ace_Racer già completato${NC}"
echo ""

# Funzione per processare un singolo emulatore
process_single_emulator() {
    local name=$1
    local num=$2
    
    echo -e "${CYAN}========================================${NC}"
    echo -e "${YELLOW}[$num/$total] PROCESSAMENTO: $name${NC}"
    echo -e "${CYAN}========================================${NC}"
    
    # Pulisci processi precedenti
    pkill -f emulator 2>/dev/null || true
    sleep 3
    
    echo "  🚀 Avvio emulatore..."
    # Avvia emulatore in background senza finestra
    timeout 150 emulator -avd "$name" -no-window -no-audio -no-boot-anim -gpu off -verbose &
    local emulator_pid=$!
    
    echo "  ⏳ Attesa boot Android (max 2 minuti)..."
    
    # Attendi che Android sia completamente avviato
    local boot_complete=false
    local wait_count=0
    
    while [ $wait_count -lt 40 ]; do
        if adb shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
            echo -e "${GREEN}  ✅ Android avviato correttamente${NC}"
            boot_complete=true
            break
        fi
        sleep 3
        ((wait_count++))
        echo -n "."
    done
    echo ""
    
    if [ "$boot_complete" = false ]; then
        echo -e "${RED}  ❌ Timeout boot Android${NC}"
        kill $emulator_pid 2>/dev/null || true
        return 1
    fi
    
    # Rimuovi Gmail
    echo "  📧 Rimozione Gmail e servizi Google..."
    
    # Disinstalla Gmail
    if adb shell pm uninstall --user 0 com.google.android.gm 2>/dev/null; then
        echo -e "${GREEN}  ✅ Gmail disinstallato${NC}"
    else
        # Se non riesce a disinstallare, prova a disabilitare
        if adb shell pm disable-user --user 0 com.google.android.gm 2>/dev/null; then
            echo -e "${GREEN}  ✅ Gmail disabilitato${NC}"
        else
            echo -e "${YELLOW}  ⚠️ Gmail non trovato (già rimosso?)${NC}"
        fi
    fi
    
    # Rimuovi servizi Google aggiuntivi
    echo "  🔧 Rimozione servizi Google aggiuntivi..."
    adb shell pm disable-user --user 0 com.google.android.gsf 2>/dev/null || true
    adb shell pm disable-user --user 0 com.google.android.gms.setup 2>/dev/null || true
    adb shell pm disable-user --user 0 com.google.android.backup 2>/dev/null || true
    
    # Chiudi emulatore
    echo "  🛑 Chiusura emulatore..."
    kill $emulator_pid 2>/dev/null || true
    
    # Attendi chiusura completa
    sleep 5
    
    echo -e "${GREEN}  ✅ $name COMPLETATO${NC}"
    echo ""
    
    return 0
}

# Processa tutti gli emulatori
echo -e "${CYAN}🚀 Inizio processamento automatico...${NC}"
echo ""

successful=0
failed=0
failed_emulators=()

for i in "${!remaining_emulators[@]}"; do
    emulator="${remaining_emulators[$i]}"
    num=$((i + 1))
    
    if process_single_emulator "$emulator" "$num"; then
        ((successful++))
    else
        ((failed++))
        failed_emulators+=("$emulator")
        echo -e "${RED}❌ FALLITO: $emulator${NC}"
    fi
    
    # Pausa tra emulatori per stabilità
    if [ $num -lt $total ]; then
        echo -e "${BLUE}⏸️ Pausa 10 secondi prima del prossimo...${NC}"
        sleep 10
    fi
done

# Pulizia finale
echo -e "${BLUE}🧹 Pulizia finale...${NC}"
pkill -f emulator 2>/dev/null || true
sleep 3

# Riepilogo finale
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎉 PROCESSAMENTO AUTOMATICO COMPLETATO!${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

echo -e "${BLUE}📊 RISULTATI FINALI:${NC}"
echo -e "${GREEN}✅ Ace_Racer (già completato)${NC}"
echo -e "${GREEN}✅ Nuovi successi: $successful${NC}"
echo -e "${RED}❌ Fallimenti: $failed${NC}"
echo -e "${CYAN}📱 Totale processati: $((successful + 1))/31${NC}"

if [ $failed -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🏆 TUTTI I 31 EMULATORI PROCESSATI CON SUCCESSO!${NC}"
    echo -e "${GREEN}📧 Gmail rimosso da tutti gli emulatori${NC}"
    echo -e "${GREEN}💾 Spazio liberato: ~15GB${NC}"
    echo -e "${GREEN}🚀 Performance ottimizzate${NC}"
    echo ""
    echo -e "${CYAN}🎮 CONFIGURAZIONE ANDROID STUDIO 2025 COMPLETATA AL 100%!${NC}"
else
    echo ""
    echo -e "${YELLOW}⚠️ Emulatori che richiedono processamento manuale:${NC}"
    for failed_emu in "${failed_emulators[@]}"; do
        echo "  • $failed_emu"
    done
fi

echo ""
echo -e "${GREEN}🎮 Emulatori Android ottimizzati e pronti per il gaming!${NC}"

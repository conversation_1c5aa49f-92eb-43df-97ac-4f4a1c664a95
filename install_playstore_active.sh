#!/bin/bash

# Script per installare Play Store sugli emulatori attivi

echo "🏪 INSTALLAZIONE PLAY STORE SU EMULATORI ATTIVI"
echo "==============================================="
echo ""

# Funzione per installare Play Store su un emulatore
install_playstore_on_device() {
    local device_id=$1
    local emulator_name=$2
    
    echo "🔧 Processando: $emulator_name ($device_id)"
    
    # Verifica che l'emulatore sia pronto
    local boot_completed=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell getprop sys.boot_completed 2>/dev/null | tr -d '\r')
    
    if [ "$boot_completed" != "1" ]; then
        echo "   ⏳ Emulatore non ancora pronto, attendo..."
        sleep 10
        boot_completed=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell getprop sys.boot_completed 2>/dev/null | tr -d '\r')
    fi
    
    if [ "$boot_completed" = "1" ]; then
        echo "   ✅ Emulatore pronto"
        
        # Verifica se Play Store è già presente
        local playstore_present=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep "com.android.vending" | wc -l)
        
        if [ "$playstore_present" -gt 0 ]; then
            echo "   ✅ Play Store già presente"
            return 0
        fi
        
        echo "   📦 Play Store non trovato, installazione necessaria..."
        
        # Metodo 1: Prova a installare Google Play Services prima
        echo "   🔧 Installazione Google Play Services..."
        ~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm enable com.google.android.gms >/dev/null 2>&1
        
        # Metodo 2: Prova a scaricare Play Store da un emulatore con Play Store
        echo "   📥 Tentativo estrazione Play Store da system image..."
        
        # Crea un emulatore temporaneo con Play Store per estrarre l'APK
        if [ ! -f "com.android.vending.apk" ]; then
            echo "   🚀 Creazione emulatore temporaneo per estrazione..."
            
            # Crea emulatore temporaneo con Play Store
            echo "no" | ~/Android/Sdk/cmdline-tools/latest/bin/avdmanager create avd \
                -n "TEMP_PLAYSTORE" \
                -k "system-images;android-34;google_apis_playstore;x86_64" \
                -d "pixel_4" \
                -f >/dev/null 2>&1
            
            # Avvia emulatore temporaneo
            ~/Android/Sdk/emulator/emulator -avd "TEMP_PLAYSTORE" -no-window -no-audio -no-snapshot-save &
            TEMP_PID=$!
            
            echo "   ⏳ Attesa avvio emulatore temporaneo (45s)..."
            sleep 45
            
            # Trova device temporaneo
            TEMP_DEVICE=$(~/Android/Sdk/platform-tools/adb devices | grep emulator | tail -1 | cut -f1)
            
            if [ -n "$TEMP_DEVICE" ] && [ "$TEMP_DEVICE" != "$device_id" ]; then
                echo "   📱 Emulatore temporaneo: $TEMP_DEVICE"
                
                # Estrai Play Store APK
                PLAYSTORE_PATH=$(~/Android/Sdk/platform-tools/adb -s "$TEMP_DEVICE" shell pm path com.android.vending 2>/dev/null | head -1 | cut -d: -f2 | tr -d '\r')
                
                if [ -n "$PLAYSTORE_PATH" ]; then
                    echo "   📦 Estrazione Play Store APK..."
                    ~/Android/Sdk/platform-tools/adb -s "$TEMP_DEVICE" pull "$PLAYSTORE_PATH" "com.android.vending.apk" >/dev/null 2>&1
                    
                    if [ -f "com.android.vending.apk" ]; then
                        echo "   ✅ Play Store APK estratto"
                    fi
                fi
                
                # Chiudi emulatore temporaneo
                ~/Android/Sdk/platform-tools/adb -s "$TEMP_DEVICE" emu kill >/dev/null 2>&1
            fi
            
            kill $TEMP_PID >/dev/null 2>&1
            
            # Rimuovi AVD temporaneo
            ~/Android/Sdk/cmdline-tools/latest/bin/avdmanager delete avd -n "TEMP_PLAYSTORE" >/dev/null 2>&1
        fi
        
        # Installa Play Store se APK disponibile
        if [ -f "com.android.vending.apk" ]; then
            echo "   📲 Installazione Play Store..."
            ~/Android/Sdk/platform-tools/adb -s "$device_id" install -r "com.android.vending.apk" >/dev/null 2>&1
            
            # Verifica installazione
            local playstore_after=$(~/Android/Sdk/platform-tools/adb -s "$device_id" shell pm list packages | grep "com.android.vending" | wc -l)
            
            if [ "$playstore_after" -gt 0 ]; then
                echo "   ✅ Play Store installato con successo!"
                return 0
            else
                echo "   ⚠️  Installazione Play Store fallita"
                return 1
            fi
        else
            echo "   ⚠️  Play Store APK non disponibile"
            echo "   ℹ️  L'emulatore funzionerà comunque con Google Play Services"
            return 0
        fi
    else
        echo "   ❌ Emulatore non risponde"
        return 1
    fi
}

# Ottieni lista emulatori attivi
echo "🔍 Ricerca emulatori attivi..."
active_devices=$(~/Android/Sdk/platform-tools/adb devices | grep "emulator" | grep "device" | cut -f1)

if [ -z "$active_devices" ]; then
    echo "❌ Nessun emulatore attivo trovato!"
    exit 1
fi

# Conta emulatori attivi
device_count=$(echo "$active_devices" | wc -l)
echo "📱 Emulatori attivi trovati: $device_count"
echo ""

# Lista emulatori attivi con nomi
declare -A device_names
for device in $active_devices; do
    # Ottieni nome emulatore dal device
    avd_name=$(~/Android/Sdk/platform-tools/adb -s "$device" emu avd name 2>/dev/null | tr -d '\r')
    if [ -n "$avd_name" ]; then
        device_names["$device"]="$avd_name"
        echo "📱 $device -> $avd_name"
    else
        device_names["$device"]="Unknown"
        echo "📱 $device -> Unknown"
    fi
done

echo ""
echo "🚀 Inizio installazione Play Store su $device_count emulatori..."
echo ""

# Processa tutti gli emulatori attivi
success_count=0
failed_count=0

for device in $active_devices; do
    emulator_name=${device_names["$device"]}
    
    if install_playstore_on_device "$device" "$emulator_name"; then
        ((success_count++))
    else
        ((failed_count++))
    fi
    
    echo ""
done

echo "📊 RISULTATI FINALI:"
echo "✅ Successi: $success_count"
echo "❌ Fallimenti: $failed_count"
echo "📱 Totale processati: $device_count"

if [ $success_count -gt 0 ]; then
    echo ""
    echo "🎉 Play Store installato su $success_count emulatori!"
fi

echo ""
echo "✅ Batch completato!"

#!/bin/bash
# Guida per creare AVD direttamente tramite Android Studio GUI

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

section() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "======================================================="
echo -e "${CYAN}CREAZIONE AVD TRAMITE ANDROID STUDIO GUI${NC}"
echo "Soluzione definitiva per AVD visibili nel Virtual Device Manager"
echo "Data: $(date)"
echo "======================================================="
echo ""

# Termina Android Studio
section "PREPARAZIONE"
info "Terminazione Android Studio..."
pkill -f android-studio 2>/dev/null || true
sleep 2

# Backup e pulizia AVD command-line
section "PULIZIA AVD COMMAND-LINE"
if [ -d ~/.android/avd ]; then
    BACKUP_DIR="$HOME/Android/AVD/backups/cmdline_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    info "Backup AVD command-line in: $BACKUP_DIR"
    cp -r ~/.android/avd/* "$BACKUP_DIR/" 2>/dev/null || true
    
    # Pulisci directory AVD
    rm -rf ~/.android/avd/*
    log "AVD command-line rimossi (backup salvato)"
else
    mkdir -p ~/.android/avd
    log "Directory AVD creata"
fi

# Pulisci cache
rm -rf ~/.cache/Google/AndroidStudio*/caches/* 2>/dev/null || true
log "Cache Android Studio pulita"

# Verifica SDK
section "VERIFICA SDK"
export ANDROID_HOME=/home/<USER>/Android/Sdk
unset ANDROID_AVD_HOME

if [ -d "$ANDROID_HOME/system-images" ]; then
    log "SDK presente con system images"
    
    info "System images disponibili:"
    find "$ANDROID_HOME/system-images" -name "system.img" | while read img; do
        api_level=$(echo "$img" | grep -o 'android-[0-9]*')
        echo "  ✓ $api_level"
    done
else
    error "System images non trovate"
fi

echo ""
section "ISTRUZIONI DETTAGLIATE"
echo ""
echo -e "${YELLOW}IMPORTANTE: Segui ESATTAMENTE questi passaggi${NC}"
echo ""

echo "1. ${CYAN}AVVIO ANDROID STUDIO${NC}"
echo "   - Android Studio si aprirà automaticamente"
echo "   - Attendi il caricamento completo"
echo ""

echo "2. ${CYAN}ACCESSO VIRTUAL DEVICE MANAGER${NC}"
echo "   - Clicca su 'More Actions'"
echo "   - Seleziona 'Virtual Device Manager'"
echo "   - Se vedi 'Medium Phone API 36.0', eliminalo (cestino)"
echo ""

echo "3. ${CYAN}CREAZIONE AVD 1 - GAMING PERFORMANCE${NC}"
echo "   - Clicca 'Create Virtual Device'"
echo "   - Category: Phone"
echo "   - Device: Pixel 7 Pro (6.7\", 3120x1440)"
echo "   - Next"
echo "   - System Image: Android 14.0 (API 34) Google APIs x86_64"
echo "   - Download se necessario, poi Next"
echo "   - AVD Name: ${GREEN}Gaming_Android14_8GB${NC}"
echo "   - Clicca 'Show Advanced Settings'"
echo "   - RAM: ${GREEN}8192 MB${NC}"
echo "   - Multi-Core CPU: ${GREEN}8${NC}"
echo "   - Graphics: ${GREEN}Hardware - GLES 2.0${NC}"
echo "   - Internal Storage: ${GREEN}32 GB${NC}"
echo "   - Finish"
echo ""

echo "4. ${CYAN}CREAZIONE AVD 2 - DEVELOPMENT${NC}"
echo "   - Clicca 'Create Virtual Device'"
echo "   - Device: Pixel 6 (6.4\", 2400x1080)"
echo "   - System Image: Android 14.0 (API 34) Google APIs"
echo "   - AVD Name: ${GREEN}Dev_Android14_4GB${NC}"
echo "   - Advanced Settings:"
echo "     * RAM: ${GREEN}4096 MB${NC}"
echo "     * Multi-Core CPU: ${GREEN}4${NC}"
echo "     * Graphics: ${GREEN}Hardware - GLES 2.0${NC}"
echo "     * Internal Storage: ${GREEN}16 GB${NC}"
echo "   - Finish"
echo ""

echo "5. ${CYAN}CREAZIONE AVD 3 - GAMING COMPATIBILITY${NC}"
echo "   - Device: Pixel 7 (6.3\", 2400x1080)"
echo "   - System Image: Android 13.0 (API 33) Google APIs"
echo "   - AVD Name: ${GREEN}Gaming_Android13_6GB${NC}"
echo "   - Advanced Settings:"
echo "     * RAM: ${GREEN}6144 MB${NC}"
echo "     * Multi-Core CPU: ${GREEN}6${NC}"
echo "     * Graphics: ${GREEN}Hardware - GLES 2.0${NC}"
echo "     * Internal Storage: ${GREEN}24 GB${NC}"
echo "   - Finish"
echo ""

echo "6. ${CYAN}CREAZIONE AVD 4 - TESTING LATEST${NC}"
echo "   - Device: Pixel 7 Pro"
echo "   - System Image: Android 15.0 (API 35) Google APIs"
echo "   - AVD Name: ${GREEN}Test_Android15_8GB${NC}"
echo "   - Advanced Settings:"
echo "     * RAM: ${GREEN}8192 MB${NC}"
echo "     * Multi-Core CPU: ${GREEN}8${NC}"
echo "     * Graphics: ${GREEN}Hardware - GLES 2.0${NC}"
echo "     * Internal Storage: ${GREEN}64 GB${NC}"
echo "   - Finish"
echo ""

echo "7. ${CYAN}VERIFICA FINALE${NC}"
echo "   - Nel Virtual Device Manager dovresti vedere tutti e 4 gli AVD"
echo "   - Testa un AVD cliccando il pulsante Play (▶️)"
echo ""

# Crea file di riferimento rapido
cat > /home/<USER>/AVD_Creation_Reference.txt << 'EOF'
=== RIFERIMENTO RAPIDO CREAZIONE AVD ===

AVD 1 - GAMING PERFORMANCE:
- Device: Pixel 7 Pro
- System: Android 14 (API 34) Google APIs
- Name: Gaming_Android14_8GB
- RAM: 8192 MB, CPU: 8, Storage: 32 GB

AVD 2 - DEVELOPMENT:
- Device: Pixel 6
- System: Android 14 (API 34) Google APIs  
- Name: Dev_Android14_4GB
- RAM: 4096 MB, CPU: 4, Storage: 16 GB

AVD 3 - GAMING COMPATIBILITY:
- Device: Pixel 7
- System: Android 13 (API 33) Google APIs
- Name: Gaming_Android13_6GB
- RAM: 6144 MB, CPU: 6, Storage: 24 GB

AVD 4 - TESTING LATEST:
- Device: Pixel 7 Pro
- System: Android 15 (API 35) Google APIs
- Name: Test_Android15_8GB
- RAM: 8192 MB, CPU: 8, Storage: 64 GB

IMPOSTAZIONI AVANZATE COMUNI:
- Graphics: Hardware - GLES 2.0
- Use Host GPU: Yes
- Boot Option: Cold Boot
- Network Speed: Full
- Network Latency: None
EOF

log "Riferimento rapido creato: AVD_Creation_Reference.txt"

echo ""
echo "======================================================="
echo -e "${GREEN}PREPARAZIONE COMPLETATA!${NC}"
echo "======================================================="
echo ""
echo "STATO ATTUALE:"
echo "✓ Android Studio terminato"
echo "✓ AVD command-line rimossi (backup salvato)"
echo "✓ Cache pulita"
echo "✓ SDK verificato"
echo "✓ Istruzioni dettagliate preparate"
echo ""
echo -e "${CYAN}AVVIO ANDROID STUDIO...${NC}"
echo ""

# Avvia Android Studio
android-studio &

sleep 3
echo "Android Studio avviato!"
echo ""
echo -e "${YELLOW}SEGUI LE ISTRUZIONI SOPRA PER CREARE GLI AVD${NC}"
echo ""
echo "Riferimento rapido disponibile in:"
echo "  ${BLUE}cat AVD_Creation_Reference.txt${NC}"
echo ""
echo "Al termine della creazione, nel Virtual Device Manager vedrai:"
echo "  ✓ Gaming_Android14_8GB"
echo "  ✓ Dev_Android14_4GB"
echo "  ✓ Gaming_Android13_6GB"
echo "  ✓ Test_Android15_8GB"
echo ""
echo -e "${GREEN}Inizia ora la creazione degli AVD tramite Android Studio GUI!${NC}"

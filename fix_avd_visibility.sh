#!/bin/bash
# Fix per rendere visibili gli AVD in Android Studio

set -e

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[✓]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

error() {
    echo -e "${RED}[✗]${NC} $1"
}

info() {
    echo -e "${BLUE}[i]${NC} $1"
}

echo "=== FIX VISIBILITÀ AVD IN ANDROID STUDIO ==="
echo "Data: $(date)"
echo "============================================"
echo ""

# Carica variabili ambiente
source ~/.bashrc

# Verifica directory AVD standard
info "Verifica directory AVD standard:"
if [ -d ~/.android/avd ]; then
    log "Directory ~/.android/avd esistente"
    AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
    log "AVD trovati: $AVD_COUNT"
    
    echo "AVD disponibili:"
    ls ~/.android/avd/*.ini 2>/dev/null | sed 's|.*/||' | sed 's|\.ini||' | while read avd; do
        echo "  - $avd"
    done
else
    error "Directory ~/.android/avd non trovata"
    mkdir -p ~/.android/avd
    log "Directory ~/.android/avd creata"
fi
echo ""

# Verifica che i file .ini puntino alle directory corrette
info "Verifica file .ini:"
for ini_file in ~/.android/avd/*.ini; do
    if [ -f "$ini_file" ]; then
        avd_name=$(basename "$ini_file" .ini)
        avd_path=$(grep "path=" "$ini_file" | cut -d'=' -f2)
        
        if [ -d "$avd_path" ]; then
            log "$avd_name: Path corretto ($avd_path)"
        else
            warn "$avd_name: Path non valido ($avd_path)"
            
            # Correggi il path
            correct_path="$HOME/.android/avd/$avd_name.avd"
            if [ -d "$correct_path" ]; then
                sed -i "s|path=.*|path=$correct_path|" "$ini_file"
                log "$avd_name: Path corretto a $correct_path"
            else
                error "$avd_name: Directory AVD non trovata"
            fi
        fi
    fi
done
echo ""

# Verifica configurazioni AVD
info "Verifica configurazioni AVD:"
for avd_dir in ~/.android/avd/*.avd; do
    if [ -d "$avd_dir" ]; then
        avd_name=$(basename "$avd_dir" .avd)
        config_file="$avd_dir/config.ini"
        
        if [ -f "$config_file" ]; then
            log "$avd_name: config.ini presente"
            
            # Verifica system image path
            sys_dir=$(grep "image.sysdir.1=" "$config_file" | cut -d'=' -f2)
            if [ -n "$sys_dir" ]; then
                full_sys_path="$ANDROID_HOME/$sys_dir"
                if [ -d "$full_sys_path" ]; then
                    log "$avd_name: System image trovata"
                else
                    warn "$avd_name: System image non trovata in $full_sys_path"
                fi
            fi
        else
            error "$avd_name: config.ini mancante"
        fi
    fi
done
echo ""

# Reset cache Android Studio
info "Reset cache Android Studio:"
STUDIO_CACHE_DIRS=(
    "$HOME/.cache/Google/AndroidStudio*"
    "$HOME/.config/Google/AndroidStudio*/options"
)

for cache_pattern in "${STUDIO_CACHE_DIRS[@]}"; do
    for cache_dir in $cache_pattern; do
        if [ -d "$cache_dir" ]; then
            log "Pulizia cache: $cache_dir"
            rm -rf "$cache_dir"/{caches,tmp}/* 2>/dev/null || true
        fi
    done
done

# Crea file di configurazione per forzare il refresh
cat > ~/.android/avd/.knownAvds << EOF
# File per forzare refresh AVD in Android Studio
# Generato il $(date)
EOF

log "Cache Android Studio pulita"
echo ""

# Verifica emulatore da command line
info "Test emulatore da command line:"
if [ -f "$ANDROID_HOME/emulator/emulator" ]; then
    log "Emulatore disponibile"
    
    echo "AVD rilevati dall'emulatore:"
    $ANDROID_HOME/emulator/emulator -list-avds | while read avd; do
        if [ -n "$avd" ]; then
            echo "  ✓ $avd"
        fi
    done
else
    error "Emulatore non trovato"
fi
echo ""

# Crea script per aprire AVD Manager direttamente
cat > /home/<USER>/Android/Scripts/open_avd_manager.sh << 'EOF'
#!/bin/bash
# Apre AVD Manager direttamente

source ~/.bashrc

echo "Apertura AVD Manager..."

# Prova diversi metodi per aprire AVD Manager
if command -v android-studio &> /dev/null; then
    # Metodo 1: Tramite Android Studio
    android-studio &
    sleep 5
    
    # Metodo 2: Comando diretto AVD Manager (se disponibile)
    if [ -f "$ANDROID_HOME/tools/bin/avdmanager" ]; then
        echo "AVD Manager disponibile via command line"
    fi
    
    echo "Android Studio avviato. Vai su Tools → AVD Manager"
else
    echo "Android Studio non trovato nel PATH"
fi
EOF

chmod +x /home/<USER>/Android/Scripts/open_avd_manager.sh
log "Script AVD Manager creato"
echo ""

# Istruzioni per Android Studio
echo "============================================"
echo -e "${GREEN}FIX COMPLETATO!${NC}"
echo "============================================"
echo ""
echo "PASSI PER VEDERE GLI AVD IN ANDROID STUDIO:"
echo ""
echo "1. Apri Android Studio"
echo "2. Vai su Tools → AVD Manager"
echo "3. Se non vedi gli AVD, clicca su 'Refresh' o riavvia Android Studio"
echo "4. Gli AVD dovrebbero ora essere visibili:"
echo ""

# Lista AVD finali
for ini_file in ~/.android/avd/*.ini; do
    if [ -f "$ini_file" ]; then
        avd_name=$(basename "$ini_file" .ini)
        echo "   ✓ $avd_name"
    fi
done

echo ""
echo "ALTERNATIVE SE NON FUNZIONA:"
echo ""
echo "• Apri AVD Manager: /home/<USER>/Android/Scripts/open_avd_manager.sh"
echo "• Avvia da terminale: \$ANDROID_HOME/emulator/emulator -avd NOME_AVD"
echo "• Menu selezione: /home/<USER>/Android/Scripts/select_avd.sh"
echo ""
echo "TROUBLESHOOTING:"
echo "• Riavvia Android Studio completamente"
echo "• Verifica che ANDROID_HOME sia impostato: echo \$ANDROID_HOME"
echo "• Controlla File → Settings → Appearance & Behavior → System Settings → Android SDK"
echo ""

# Verifica finale
FINAL_AVD_COUNT=$(ls ~/.android/avd/*.ini 2>/dev/null | wc -l)
echo "AVD configurati e pronti: $FINAL_AVD_COUNT"

if [ "$FINAL_AVD_COUNT" -gt 0 ]; then
    echo -e "${GREEN}Tutti gli AVD dovrebbero ora essere visibili in Android Studio!${NC}"
else
    echo -e "${RED}Nessun AVD trovato. Verifica la configurazione.${NC}"
fi
